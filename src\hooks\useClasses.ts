import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const useClasses = () => {
  return useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('classes')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });
};

export const useCreateClass = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (classData: any) => {
      // Get current user's school_id and active academic year
      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      const { data: activeYear } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile?.school_id)
        .eq('is_active', true)
        .single();

      // Extract grade from level - now supporting I-XII
      const gradeMap: { [key: string]: number } = { 
        'I': 1, 'II': 2, 'III': 3, 'IV': 4, 'V': 5, 'VI': 6,
        'VII': 7, 'VIII': 8, 'IX': 9, 'X': 10, 'XI': 11, 'XII': 12 
      };
      const grade = gradeMap[classData.level] || 1;

      const { data, error } = await supabase
        .from('classes')
        .insert({
          ...classData,
          grade,
          school_id: profile?.school_id,
          academic_year_id: activeYear?.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] });
      toast({
        title: "Berhasil",
        description: "Data kelas berhasil ditambahkan",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menambahkan data kelas: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateClass = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...classData }: any) => {
      // Extract grade from level if level is being updated - now supporting I-XII
      if (classData.level) {
        const gradeMap: { [key: string]: number } = { 
          'I': 1, 'II': 2, 'III': 3, 'IV': 4, 'V': 5, 'VI': 6,
          'VII': 7, 'VIII': 8, 'IX': 9, 'X': 10, 'XI': 11, 'XII': 12 
        };
        classData.grade = gradeMap[classData.level] || 1;
      }

      const { data, error } = await supabase
        .from('classes')
        .update(classData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] });
      toast({
        title: "Berhasil",
        description: "Data kelas berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal memperbarui data kelas: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteClass = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('classes')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] });
      toast({
        title: "Berhasil",
        description: "Data kelas berhasil dihapus",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menghapus data kelas: " + error.message,
        variant: "destructive",
      });
    },
  });
};
