
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

export interface ExtracurricularClass {
  id: string;
  extracurricular_id: string;
  class_id: string;
  hours_per_week: number;
  hours_per_year: number;
  school_id: string;
  created_at: string;
  updated_at: string;
  extracurriculars?: {
    name: string;
    category_id?: string; // ✅ ADDED: Foreign key to session_categories
  } | null;
  classes?: {
    name: string;
    grade: number;
    level: string;
  } | null;
}

export const useExtracurricularClasses = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['extracurricular-classes'],
    queryFn: async () => {
      if (!profile?.school_id) return [];

      // First, get extracurricular_classes without relations to avoid foreign key issues
      const { data, error } = await supabase
        .from('extracurricular_classes')
        .select('*')
        .eq('school_id', profile.school_id);

      if (error) throw error;
      return data as ExtracurricularClass[];
    },
    enabled: !!profile?.school_id,
  });
};

export const useCreateExtracurricularClass = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async (data: {
      extracurricular_id: string;
      class_id: string;
      hours_per_year: number;
    }) => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      const { data: result, error } = await supabase
        .from('extracurricular_classes')
        .insert({
          ...data,
          hours_per_week: Math.round(data.hours_per_year / 36), // Keep backward compatibility
          school_id: profile.school_id
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['extracurricular-classes'] });
      toast({
        title: "Berhasil",
        description: "Ekstrakurikuler berhasil ditambahkan ke kelas",
      });
    },
    onError: (error) => {
      console.error('Error creating extracurricular class:', error);
      toast({
        title: "Error",
        description: "Gagal menambahkan ekstrakurikuler ke kelas",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateExtracurricularClass = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, hours_per_year }: { id: string; hours_per_year: number }) => {
      const { data, error } = await supabase
        .from('extracurricular_classes')
        .update({
          hours_per_year,
          hours_per_week: Math.round(hours_per_year / 36) // Keep backward compatibility
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['extracurricular-classes'] });
      toast({
        title: "Berhasil",
        description: "JP ekstrakurikuler berhasil diperbarui",
      });
    },
    onError: (error) => {
      console.error('Error updating extracurricular class:', error);
      toast({
        title: "Error",
        description: "Gagal memperbarui JP ekstrakurikuler",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteExtracurricularClass = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      console.log('🗑️ Deleting extracurricular class:', id);

      const { error } = await supabase
        .from('extracurricular_classes')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Error deleting extracurricular class:', error);
        throw error;
      }

      console.log('✅ Extracurricular class deleted successfully');
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['extracurricular-classes'] });
      queryClient.invalidateQueries({ queryKey: ['extracurriculars'] });

      // ✅ FIXED: Invalidate JP progress queries for real-time updates
      queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
      queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

      toast({
        title: "Berhasil",
        description: "Ekstrakurikuler berhasil dihapus dari kelas",
      });

      console.log('✅ Extracurricular class delete successful - cache invalidated');
    },
    onError: (error: any) => {
      console.error('❌ Extracurricular class delete failed:', error);
      toast({
        title: "Gagal",
        description: "Gagal menghapus ekstrakurikuler: " + error.message,
        variant: "destructive",
      });
    },
  });
};


