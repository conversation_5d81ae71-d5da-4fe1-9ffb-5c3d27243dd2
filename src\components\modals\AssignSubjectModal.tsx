
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useClasses } from '@/hooks/useClasses';
import { useSubjects } from '@/hooks/useSubjects';
import { useCreateClassSubject } from '@/hooks/useClassSubjects';

interface AssignSubjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const AssignSubjectModal: React.FC<AssignSubjectModalProps> = ({ open, onOpenChange }) => {
  const [formData, setFormData] = useState({
    class_id: '',
    subject_id: '',
    hours_per_year: 36 // <PERSON><PERSON>ult 1 hour per week * 36 weeks
  });

  const { data: classes } = useClasses();
  const { data: subjects } = useSubjects();
  const createClassSubject = useCreateClassSubject();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    createClassSubject.mutate(formData, {
      onSuccess: () => {
        setFormData({ class_id: '', subject_id: '', hours_per_year: 36 });
        onOpenChange(false);
      }
    });
  };

  const isLoading = createClassSubject.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-800 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-lime-400">
            Assign Mata Pelajaran ke Kelas
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="class" className="text-gray-300">Kelas</Label>
            <Select value={formData.class_id} onValueChange={(value) => setFormData(prev => ({ ...prev, class_id: value }))}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Pilih kelas" />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600">
                {classes?.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.level} {cls.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="subject" className="text-gray-300">Mata Pelajaran</Label>
            <Select value={formData.subject_id} onValueChange={(value) => setFormData(prev => ({ ...prev, subject_id: value }))}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Pilih mata pelajaran" />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600">
                {subjects?.map((subject) => (
                  <SelectItem key={subject.id} value={subject.id}>
                    {subject.name} ({subject.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="hours" className="text-gray-300">JP per Tahun</Label>
            <Input
              id="hours"
              type="number"
              min="0"
              max="360"
              value={formData.hours_per_year}
              onChange={(e) => setFormData(prev => ({ ...prev, hours_per_year: parseInt(e.target.value) || 36 }))}
              className="bg-gray-700 border-gray-600 text-white"
              placeholder="Contoh: 144 (atau 0 jika belum direncanakan)"
              required
            />
            <p className="text-xs text-gray-400">
              Masukkan 0 jika belum ada perencanaan pasti. Nanti akan otomatis terhitung dari menu Jadwal.
            </p>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)} 
              className="border-gray-600 text-gray-300"
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button 
              type="submit" 
              className="bg-lime-400 hover:bg-lime-500 text-gray-900"
              disabled={isLoading}
            >
              {isLoading ? 'Menyimpan...' : 'Simpan'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AssignSubjectModal;
