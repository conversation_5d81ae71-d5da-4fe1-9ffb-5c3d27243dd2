
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

export interface EducationLevel {
  id: string;
  school_id: string;
  name: string;
  code: string;
  min_grade: number;
  max_grade: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const useEducationLevels = () => {
  return useQuery({
    queryKey: ['education-levels'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('education_levels')
        .select('*')
        .eq('is_active', true)
        .order('min_grade');
      
      if (error) throw error;
      return data as EducationLevel[];
    },
  });
};

export const useCreateEducationLevel = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async (data: Omit<EducationLevel, 'id' | 'created_at' | 'updated_at' | 'school_id'>) => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      const { data: result, error } = await supabase
        .from('education_levels')
        .insert({
          ...data,
          school_id: profile.school_id
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['education-levels'] });
      toast({
        title: "Berhasil",
        description: "Jenjang pendidikan berhasil ditambahkan",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal menambahkan jenjang pendidikan",
        variant: "destructive",
      });
      console.error('Error creating education level:', error);
    },
  });
};

export const useUpdateEducationLevel = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...data }: Partial<EducationLevel> & { id: string }) => {
      const { data: result, error } = await supabase
        .from('education_levels')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['education-levels'] });
      toast({
        title: "Berhasil",
        description: "Jenjang pendidikan berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal memperbarui jenjang pendidikan",
        variant: "destructive",
      });
      console.error('Error updating education level:', error);
    },
  });
};

export const useDeleteEducationLevel = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('education_levels')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['education-levels'] });
      queryClient.invalidateQueries({ queryKey: ['grade-levels'] });
      toast({
        title: "Berhasil",
        description: "Jenjang pendidikan berhasil dihapus",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal menghapus jenjang pendidikan",
        variant: "destructive",
      });
      console.error('Error deleting education level:', error);
    },
  });
};
