import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Calendar, Users, MapPin, ArrowLeft, BarChart3, Clock, BookOpen, Info, Filter } from 'lucide-react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, getDay } from 'date-fns';
import { id } from 'date-fns/locale';

interface PublicOverviewData {
  school: {
    name: string;
    logo_url?: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  class: {
    id: string;
    name: string;
    level: string;
  };
  classes: Array<{
    id: string;
    name: string;
    level: string;
    grade: number;
  }>;
  schedules: any[];
  academicWeeks: any[];
  timeSessions: any[];
  sessionCategories: Array<{
    id: string;
    name: string;
    color: string;
  }>;
  scheduleSubjects: Array<{
    id: string;
    name: string;
    session_category_id: string;
  }>;
  extracurriculars: Array<{
    id: string;
    name: string;
    session_category_id: string;
  }>;
  academicYear: {
    id: string;
    year_name: string;
    start_date: string;
    end_date: string;
  };
}

const PublicExternalViewOverviewPage: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const [data, setData] = useState<PublicOverviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states - same as ScheduleOverview
  const [selectedClassId, setSelectedClassId] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [selectedSubjectId, setSelectedSubjectId] = useState<string | null>(null);
  const [selectedSemester, setSelectedSemester] = useState<string | null>(null);

  // Navigate back to calendar view
  const handleBackToCalendar = () => {
    navigate(`/external/${token}`);
  };

  useEffect(() => {
    const fetchPublicOverviewData = async () => {
      if (!token) {
        setError('Token tidak valid');
        setLoading(false);
        return;
      }

      try {
        console.log('🔍 Fetching public overview data for token:', token);

        // Decode token to get class_id and school_id
        let classId: string;
        let schoolId: string;

        try {
          const decoded = atob(token);
          const [decodedClassId, decodedSchoolId] = decoded.split(':');
          classId = decodedClassId;
          schoolId = decodedSchoolId;
          console.log('✅ Token decoded:', { classId, schoolId });
        } catch (decodeError) {
          console.error('❌ Token decode error:', decodeError);
          setError('Token tidak valid atau rusak');
          setLoading(false);
          return;
        }

        // Fetch school data
        const { data: schoolData, error: schoolError } = await supabase
          .from('schools')
          .select('name, logo_url, address, phone, email')
          .eq('id', schoolId)
          .single();

        if (schoolError) {
          console.error('❌ School fetch error:', schoolError);
          setError('Data sekolah tidak ditemukan');
          setLoading(false);
          return;
        }

        // Fetch class data
        const { data: classData, error: classError } = await supabase
          .from('classes')
          .select('id, name, level')
          .eq('id', classId)
          .eq('school_id', schoolId)
          .single();

        if (classError) {
          console.error('❌ Class fetch error:', classError);
          setError('Data kelas tidak ditemukan');
          setLoading(false);
          return;
        }

        // Fetch all classes for filter
        const { data: allClassesData, error: allClassesError } = await supabase
          .from('classes')
          .select('id, name, level, grade')
          .eq('school_id', schoolId)
          .order('name');

        if (allClassesError) {
          console.error('❌ All classes fetch error:', allClassesError);
        }

        // Fetch session categories
        const { data: sessionCategoriesData, error: sessionCategoriesError } = await supabase
          .from('session_categories')
          .select('id, name, color')
          .eq('school_id', schoolId)
          .order('name');

        if (sessionCategoriesError) {
          console.error('❌ Session categories fetch error:', sessionCategoriesError);
        }

        // We'll extract subjects and categories from schedules_view data
        // This is more reliable than trying to fetch from separate tables

        // Fetch active academic year
        const { data: activeAcademicYear, error: academicYearError } = await supabase
          .from('academic_years')
          .select('*')
          .eq('school_id', schoolId)
          .eq('is_active', true)
          .single();

        if (academicYearError) {
          console.error('❌ Academic year fetch error:', academicYearError);
          setError('Data tahun akademik tidak ditemukan');
          setLoading(false);
          return;
        }

        // Fetch schedules data using schedules_view only
        const { data: schedulesData, error: schedulesError } = await supabase
          .from('schedules_view')
          .select('*')
          .eq('school_id', schoolId)
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true });

        if (schedulesError) {
          console.error('❌ Schedules fetch error:', schedulesError);
          setError('Data jadwal tidak ditemukan');
          setLoading(false);
          return;
        }

        let academicWeeks;

        if (activeAcademicYear) {
          const startDate = new Date(activeAcademicYear.start_date);
          const endDate = new Date(activeAcademicYear.end_date);
          const weeks = [];

          let currentWeek = new Date(startDate);
          currentWeek.setDate(currentWeek.getDate() - currentWeek.getDay() + 1);
          let weekNumber = 1;

          while (currentWeek <= endDate && weekNumber <= 52) {
            const weekEnd = new Date(currentWeek);
            weekEnd.setDate(currentWeek.getDate() + 6);

            weeks.push({
              weekNumber,
              startDate: new Date(currentWeek),
              endDate: weekEnd,
              label: `Minggu ${weekNumber}`,
              dateRange: `${currentWeek.getDate()}/${currentWeek.getMonth() + 1} - ${weekEnd.getDate()}/${weekEnd.getMonth() + 1}`,
              isCurrentWeek: false
            });

            currentWeek.setDate(currentWeek.getDate() + 7);
            weekNumber++;
          }

          academicWeeks = weeks;
        } else {
          const baseDate = new Date(2025, 0, 6);
          academicWeeks = Array.from({ length: 52 }, (_, i) => {
            const weekNumber = i + 1;
            const startDate = new Date(baseDate);
            startDate.setDate(baseDate.getDate() + (i * 7));

            const endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 6);

            return {
              weekNumber,
              startDate,
              endDate,
              label: `Minggu ${weekNumber}`,
              dateRange: `${startDate.getDate()}/${startDate.getMonth() + 1} - ${endDate.getDate()}/${endDate.getMonth() + 1}`,
              isCurrentWeek: false
            };
          });
        }

        // Fetch time sessions
        const { data: timeSessionsData, error: timeError } = await supabase
          .from('time_sessions')
          .select('*')
          .eq('school_id', schoolId)
          .order('start_time', { ascending: true });

        if (timeError) {
          console.error('❌ Time sessions fetch error:', timeError);
        }

        // Extract unique subjects and categories from schedules data
        const uniqueSubjects = new Map();
        const uniqueCategories = new Map();

        schedulesData?.forEach(schedule => {
          if (schedule.subject_id && schedule.subject_name) {
            uniqueSubjects.set(schedule.subject_id, {
              id: schedule.subject_id,
              name: schedule.subject_name,
              session_category_id: schedule.session_category_id || ''
            });
          }
          if (schedule.session_category_id && schedule.session_category_name) {
            uniqueCategories.set(schedule.session_category_id, {
              id: schedule.session_category_id,
              name: schedule.session_category_name,
              color: schedule.session_category_color || '#6B7280'
            });
          }
        });

        const extractedSubjects = Array.from(uniqueSubjects.values());
        const extractedCategories = Array.from(uniqueCategories.values());

        setData({
          school: schoolData,
          class: classData,
          classes: allClassesData || [],
          schedules: schedulesData || [],
          academicWeeks,
          timeSessions: timeSessionsData || [],
          sessionCategories: sessionCategoriesData || extractedCategories,
          scheduleSubjects: extractedSubjects,
          extracurriculars: [], // Will be empty for now, can be populated if needed
          academicYear: activeAcademicYear
        });

        // Set default selected class to the token class
        setSelectedClassId(classId);

        console.log('✅ Public overview data loaded:', {
          school: schoolData.name,
          class: classData.name,
          schedulesCount: schedulesData?.length || 0,
          academicWeeksCount: academicWeeks.length,
          timeSessionsCount: timeSessionsData?.length || 0
        });

      } catch (fetchError) {
        console.error('❌ General fetch error:', fetchError);
        setError('Terjadi kesalahan saat memuat data');
      } finally {
        setLoading(false);
      }
    };

    fetchPublicOverviewData();
  }, [token]);

  // Combine all subjects like in OverviewFilters
  const allSubjects = useMemo(() => {
    if (!data?.scheduleSubjects && !data?.extracurriculars) return [];
    
    return [
      // Add schedule subjects (KBM subjects)
      ...data.scheduleSubjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        session_category_id: subject.session_category_id,
        type: 'subject' as const
      })),
      // Add extracurriculars
      ...data.extracurriculars.map(extracurricular => ({
        id: extracurricular.id,
        name: extracurricular.name,
        session_category_id: extracurricular.session_category_id,
        type: 'extracurricular' as const
      }))
    ];
  }, [data?.scheduleSubjects, data?.extracurriculars]);

  // Filter subjects based on selected category
  const filteredSubjects = useMemo(() => {
    if (!selectedCategoryId) return allSubjects;
    return allSubjects.filter(subject => subject.session_category_id === selectedCategoryId);
  }, [allSubjects, selectedCategoryId]);

  // Filter schedules based on selected filters - same logic as ScheduleOverview
  const filteredSchedules = useMemo(() => {
    if (!data?.schedules) return [];

    return data.schedules.filter(schedule => {
      // Filter by class
      if (selectedClassId && selectedClassId !== 'all' && schedule.class_id !== selectedClassId) {
        return false;
      }

      // Filter by category
      if (selectedCategoryId && schedule.session_category_id !== selectedCategoryId) {
        return false;
      }

      // Filter by subject
      if (selectedSubjectId && schedule.subject_id !== selectedSubjectId) {
        return false;
      }

      // Filter by semester
      if (selectedSemester && data.academicWeeks.length > 0) {
        const academicWeek = data.academicWeeks.find(week => week.weekNumber === schedule.academic_week);
        if (academicWeek) {
          const scheduleDate = new Date(academicWeek.startDate);
          const month = scheduleDate.getMonth() + 1; // 1-based month

          if (selectedSemester === '1') {
            // Semester 1: Juli-Desember (months 7-12)
            if (month < 7 || month > 12) {
              return false;
            }
          } else if (selectedSemester === '2') {
            // Semester 2: Januari-Juni (months 1-6)
            if (month < 1 || month > 6) {
              return false;
            }
          }
        }
      }

      return true;
    });
  }, [data?.schedules, data?.academicWeeks, selectedClassId, selectedCategoryId, selectedSubjectId, selectedSemester]);

  // Calculate comprehensive overview statistics (adapted from OverviewSummary)
  const overviewStats = useMemo(() => {
    if (!filteredSchedules || !data?.academicWeeks) return null;

    const schedules = filteredSchedules;
    const academicWeeks = data.academicWeeks;
    
    // Convert schedules to actual dates
    const scheduleDates = new Set<string>();
    const subjectIds = new Set<string>();
    let totalMinutes = 0;

    schedules.forEach(schedule => {
      // Calculate actual date from academic_week and day_of_week
      if (schedule.academic_week && schedule.day_of_week) {
        const academicWeek = academicWeeks.find(week => week.weekNumber === schedule.academic_week);
        
        if (academicWeek) {
          const weekStartDate = new Date(academicWeek.startDate);
          const dayOffset = schedule.day_of_week === 7 ? 6 : schedule.day_of_week - 1;
          
          const scheduleDate = new Date(weekStartDate);
          scheduleDate.setDate(weekStartDate.getDate() + dayOffset);
          
          const dateString = format(scheduleDate, 'yyyy-MM-dd');
          scheduleDates.add(dateString);
        }
      }

      // Count unique subjects
      if (schedule.subject_id) {
        subjectIds.add(schedule.subject_id);
      }

      // Calculate total duration
      if (schedule.start_time && schedule.end_time) {
        const startTime = new Date(`2000-01-01 ${schedule.start_time}`);
        const endTime = new Date(`2000-01-01 ${schedule.end_time}`);
        const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
        totalMinutes += durationMinutes;
      }
    });

    // Calculate monthly breakdown
    const monthlyBreakdown = [];

    // Get academic year from academicWeeks if available
    let startYear = new Date().getFullYear();
    if (academicWeeks.length > 0) {
      startYear = new Date(academicWeeks[0].startDate).getFullYear();
    }

    // Academic year months (July to June)
    const academicMonths = [
      { name: 'Juli', monthIndex: 6, year: startYear },
      { name: 'Agustus', monthIndex: 7, year: startYear },
      { name: 'September', monthIndex: 8, year: startYear },
      { name: 'Oktober', monthIndex: 9, year: startYear },
      { name: 'November', monthIndex: 10, year: startYear },
      { name: 'Desember', monthIndex: 11, year: startYear },
      { name: 'Januari', monthIndex: 0, year: startYear + 1 },
      { name: 'Februari', monthIndex: 1, year: startYear + 1 },
      { name: 'Maret', monthIndex: 2, year: startYear + 1 },
      { name: 'April', monthIndex: 3, year: startYear + 1 },
      { name: 'Mei', monthIndex: 4, year: startYear + 1 },
      { name: 'Juni', monthIndex: 5, year: startYear + 1 },
    ];

    academicMonths.forEach(month => {
      const monthStart = startOfMonth(new Date(month.year, month.monthIndex, 1));
      const monthEnd = endOfMonth(new Date(month.year, month.monthIndex, 1));
      const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });
      
      let daysWithSchedule = 0;
      daysInMonth.forEach(day => {
        const dayString = format(day, 'yyyy-MM-dd');
        if (scheduleDates.has(dayString)) {
          daysWithSchedule++;
        }
      });

      monthlyBreakdown.push({
        month: month.name,
        totalDays: daysInMonth.length,
        daysWithSchedule,
        percentage: Math.round((daysWithSchedule / daysInMonth.length) * 100)
      });
    });

    const totalHours = Math.round(totalMinutes / 60 * 10) / 10;

    return {
      totalSchedules: schedules.length,
      totalDaysWithSchedule: scheduleDates.size,
      totalSubjects: subjectIds.size,
      totalHours,
      totalJP: Math.round((totalHours * 60) / 45),
      monthlyBreakdown,
      scheduleDates
    };
  }, [data?.schedules, data?.academicWeeks]);

  // Generate months for yearly calendar view (adapted from YearlyCalendarView)
  const yearlyCalendarMonths = useMemo(() => {
    if (!data?.academicWeeks || data.academicWeeks.length === 0) return [];

    const startYear = new Date(data.academicWeeks[0].startDate).getFullYear();

    return [
      // July to December of start year (Semester 1)
      { name: 'Juli', year: startYear, monthIndex: 6, semester: 1 },
      { name: 'Agustus', year: startYear, monthIndex: 7, semester: 1 },
      { name: 'September', year: startYear, monthIndex: 8, semester: 1 },
      { name: 'Oktober', year: startYear, monthIndex: 9, semester: 1 },
      { name: 'November', year: startYear, monthIndex: 10, semester: 1 },
      { name: 'Desember', year: startYear, monthIndex: 11, semester: 1 },
      // January to June of end year (Semester 2)
      { name: 'Januari', year: startYear + 1, monthIndex: 0, semester: 2 },
      { name: 'Februari', year: startYear + 1, monthIndex: 1, semester: 2 },
      { name: 'Maret', year: startYear + 1, monthIndex: 2, semester: 2 },
      { name: 'April', year: startYear + 1, monthIndex: 3, semester: 2 },
      { name: 'Mei', year: startYear + 1, monthIndex: 4, semester: 2 },
      { name: 'Juni', year: startYear + 1, monthIndex: 5, semester: 2 },
    ];
  }, [data?.academicWeeks]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Memuat overview jadwal...</p>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                {error || 'Data tidak ditemukan'}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleBackToCalendar}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Kembali ke Jadwal
              </Button>
              
              {data.school.logo_url && (
                <img 
                  src={data.school.logo_url} 
                  alt="Logo Sekolah" 
                  className="h-12 w-12 object-contain"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                  Overview Jadwal - Timeline Perbulan
                </h1>
                <p className="text-gray-600">
                  {data.school.name} - Kelas {data.class.name}
                </p>
              </div>
            </div>
            
            <div className="text-right text-sm text-gray-500">
              <p className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Tahun Akademik 2025/2026
              </p>
              <p className="flex items-center mt-1">
                <Users className="h-4 w-4 mr-1" />
                Kelas {data.class.name}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-6 space-y-6">
        {/* Filters Section */}
        <Card className="bg-card border-border shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Filter className="h-5 w-5 text-primary" />
              Filter Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Class Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Filter Kelas:</label>
                <Select
                  value={selectedClassId || 'all'}
                  onValueChange={(value) => setSelectedClassId(value === 'all' ? null : value)}
                >
                  <SelectTrigger className="w-full bg-background border-border text-foreground">
                    <SelectValue placeholder="Semua Kelas" />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border-border">
                    <SelectItem value="all" className="text-foreground hover:bg-accent">
                      Semua Kelas
                    </SelectItem>
                    {data.classes.map(cls => (
                      <SelectItem key={cls.id} value={cls.id} className="text-foreground hover:bg-accent">
                        {cls.name} ({cls.level} {cls.grade})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Filter Kategori:</label>
                <Select
                  value={selectedCategoryId || 'all'}
                  onValueChange={(value) => {
                    setSelectedCategoryId(value === 'all' ? null : value);
                    // Reset subject filter when category changes
                    setSelectedSubjectId(null);
                  }}
                >
                  <SelectTrigger className="w-full bg-background border-border text-foreground">
                    <SelectValue placeholder="Semua Kategori" />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border-border">
                    <SelectItem value="all" className="text-foreground hover:bg-accent">
                      Semua Kategori
                    </SelectItem>
                    {data.sessionCategories.map(category => (
                      <SelectItem key={category.id} value={category.id} className="text-foreground hover:bg-accent">
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Semester Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Filter Semester:</label>
                <Select
                  value={selectedSemester || 'all'}
                  onValueChange={(value) => setSelectedSemester(value === 'all' ? null : value)}
                >
                  <SelectTrigger className="w-full bg-background border-border text-foreground">
                    <SelectValue placeholder="Semua Semester" />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border-border">
                    <SelectItem value="all" className="text-foreground hover:bg-accent">
                      Semua Semester
                    </SelectItem>
                    <SelectItem value="1" className="text-foreground hover:bg-accent">
                      Semester 1 (Juli - Desember)
                    </SelectItem>
                    <SelectItem value="2" className="text-foreground hover:bg-accent">
                      Semester 2 (Januari - Juni)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Subject Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Filter Mata Pelajaran:</label>
                <Select
                  value={selectedSubjectId || 'all'}
                  onValueChange={(value) => setSelectedSubjectId(value === 'all' ? null : value)}
                >
                  <SelectTrigger className="w-full bg-background border-border text-foreground">
                    <SelectValue placeholder="Semua Mata Pelajaran" />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border-border">
                    <SelectItem value="all" className="text-foreground hover:bg-accent">
                      Semua Mata Pelajaran
                    </SelectItem>
                    {filteredSubjects.map(subject => (
                      <SelectItem key={subject.id} value={subject.id} className="text-foreground hover:bg-accent">
                        {subject.name}
                        {subject.type === 'extracurricular' && (
                          <span className="text-xs text-muted-foreground ml-1">(Ekstrakurikuler)</span>
                        )}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Statistics Cards */}
        {overviewStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card className="bg-card border-border shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-primary/10 rounded-lg">
                    <Calendar className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Jadwal</p>
                    <p className="text-2xl font-bold text-foreground">{overviewStats.totalSchedules}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-blue-500/10 rounded-lg">
                    <Clock className="h-6 w-6 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Hari Aktif</p>
                    <p className="text-2xl font-bold text-foreground">{overviewStats.totalDaysWithSchedule}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-green-500/10 rounded-lg">
                    <BookOpen className="h-6 w-6 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Mata Pelajaran</p>
                    <p className="text-2xl font-bold text-foreground">{overviewStats.totalSubjects}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-orange-500/10 rounded-lg">
                    <Users className="h-6 w-6 text-orange-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Jam</p>
                    <p className="text-2xl font-bold text-foreground">{overviewStats.totalHours}h</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-card border-border shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-purple-500/10 rounded-lg">
                    <Info className="h-6 w-6 text-purple-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total JP</p>
                    <p className="text-2xl font-bold text-foreground">{overviewStats.totalJP} JP</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Monthly Breakdown */}
        {overviewStats && (
          <Card className="bg-card border-border shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-foreground">
                Ringkasan Bulanan - Timeline Mata Pelajaran
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Jumlah hari dengan jadwal/kegiatan mata pelajaran per bulan dalam tahun akademik
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {overviewStats.monthlyBreakdown.map((month, index) => (
                  <div key={index} className="p-4 bg-muted/20 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium text-foreground">{month.month}</h4>
                      <span className="text-sm text-muted-foreground">{month.percentage}%</span>
                    </div>
                    <div className="text-sm text-muted-foreground mb-2">
                      {month.daysWithSchedule} dari {month.totalDays} hari
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${month.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Yearly Calendar View - Timeline Perbulan */}
        {overviewStats && yearlyCalendarMonths.length > 0 && (
          <Card className="bg-card border-border shadow-sm">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-foreground">
                Kalender Tahunan - Timeline Perbulan Mata Pelajaran
                <span className="text-lg font-normal text-muted-foreground ml-2">
                  Tahun Akademik 2025/2026
                </span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Hari yang berwarna biru menunjukkan adanya jadwal mata pelajaran. Klik pada tanggal untuk melihat detail.
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {yearlyCalendarMonths.map((month) => {
                  const monthDate = new Date(month.year, month.monthIndex, 1);
                  const monthStart = startOfMonth(monthDate);
                  const monthEnd = endOfMonth(monthDate);
                  const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });

                  return (
                    <div key={`${month.name}-${month.year}`} className="bg-card border border-border rounded-lg p-4">
                      <div className="flex items-center gap-4">
                        {/* Month Name */}
                        <div className="w-24 text-left">
                          <h3 className="text-sm font-semibold text-foreground">{month.name} {month.year}</h3>
                          <p className="text-xs text-muted-foreground">Semester {month.semester}</p>
                        </div>

                        {/* Days Grid - Horizontal layout */}
                        <div className="flex flex-wrap gap-1">
                          {Array.from({ length: 31 }, (_, i) => {
                            const dayNumber = i + 1;
                            const monthDate = new Date(month.year, month.monthIndex, dayNumber);

                            // Check if this day exists in the month
                            if (monthDate.getMonth() !== month.monthIndex) {
                              return null;
                            }

                            const dayString = format(monthDate, 'yyyy-MM-dd');
                            const hasSchedule = overviewStats.scheduleDates.has(dayString);

                            return (
                              <div
                                key={dayNumber}
                                className={`
                                  h-8 w-8 flex items-center justify-center text-xs rounded cursor-pointer transition-colors
                                  ${hasSchedule
                                    ? 'bg-blue-500 text-white font-semibold hover:bg-blue-600'
                                    : 'text-foreground hover:bg-accent border border-border'
                                  }
                                `}
                                title={hasSchedule ? `Ada jadwal mata pelajaran pada ${format(monthDate, 'dd MMMM yyyy', { locale: id })}` : `${format(monthDate, 'dd MMMM yyyy', { locale: id })} - Tidak ada jadwal`}
                              >
                                {dayNumber}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 mt-8">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="text-center text-sm text-gray-500">
            <p className="flex items-center justify-center">
              <MapPin className="h-4 w-4 mr-1" />
              {data.school.address}
            </p>
            {(data.school.phone || data.school.email) && (
              <p className="mt-1">
                {data.school.phone && (
                  <span className="mr-4">📞 {data.school.phone}</span>
                )}
                {data.school.email && (
                  <span>📧 {data.school.email}</span>
                )}
              </p>
            )}
            <p className="mt-2 text-xs text-gray-400">
              📅 Timeline Overview - Tampilan Publik untuk Kelas {data.class.name}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicExternalViewOverviewPage;