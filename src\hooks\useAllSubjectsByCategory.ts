import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useSubjects } from './useSubjects';
import { useExtracurriculars } from './useExtracurriculars';
import { useSessionCategories } from './useSessionCategories';

export interface SubjectItem {
  id: string;
  name: string;
  code?: string;
  category: string;
  category_id?: string;
  type: 'subject' | 'extracurricular';
  session_categories?: {
    id: string;
    name: string;
    color: string;
  } | null;
}

export interface CategoryWithSubjects {
  id: string;
  name: string;
  color: string;
  subjects: SubjectItem[];
}

/**
 * Hook untuk mendapatkan semua mata pelajaran (subjects + extracurriculars) 
 * yang dikelompokkan berdasarkan session_categories
 */
export const useAllSubjectsByCategory = () => {
  const { data: subjects } = useSubjects();
  const { data: extracurriculars } = useExtracurriculars();
  const { data: sessionCategories } = useSessionCategories();

  return useQuery({
    queryKey: ['all-subjects-by-category', subjects, extracurriculars, sessionCategories],
    queryFn: (): CategoryWithSubjects[] => {
      console.log('🔍 useAllSubjectsByCategory - Raw data:', {
        subjects: subjects?.length || 0,
        extracurriculars: extracurriculars?.length || 0,
        sessionCategories: sessionCategories?.length || 0,
        subjectsData: subjects?.map(s => ({ name: s.name, category_id: s.category_id })),
        sessionCategoriesData: sessionCategories?.map(sc => ({ name: sc.name, id: sc.id }))
      });

      if (!sessionCategories) return [];

      const categoriesWithSubjects: CategoryWithSubjects[] = [];

      // Process each session category
      sessionCategories.forEach(category => {
        const categorySubjects: SubjectItem[] = [];

        // Add subjects that belong to this category
        if (subjects) {
          subjects.forEach(subject => {
            if (subject.category_id === category.id) {
              console.log(`✅ Subject "${subject.name}" matches category "${category.name}"`);
              categorySubjects.push({
                id: subject.id,
                name: subject.name,
                code: subject.code,
                category: subject.category,
                category_id: subject.category_id,
                type: 'subject',
                session_categories: subject.session_categories
              });
            }
          });
        }

        // Add extracurriculars that belong to this category
        if (extracurriculars) {
          extracurriculars.forEach(extracurricular => {
            if (extracurricular.category_id === category.id) {
              categorySubjects.push({
                id: extracurricular.id,
                name: extracurricular.name,
                code: undefined,
                category: 'ekstrakurikuler',
                category_id: extracurricular.category_id,
                type: 'extracurricular',
                session_categories: extracurricular.session_categories
              });
            }
          });
        }

        // Always include category even if no subjects (this fixes the filter issue)
        categoriesWithSubjects.push({
          id: category.id,
          name: category.name,
          color: category.color || '#6B7280',
          subjects: categorySubjects
        });
      });

      console.log('📚 All subjects by category:', {
        totalCategories: categoriesWithSubjects.length,
        categoriesWithSubjects: categoriesWithSubjects.map(cat => ({
          name: cat.name,
          subjectCount: cat.subjects.length,
          subjects: cat.subjects.map(s => ({ name: s.name, type: s.type }))
        }))
      });

      return categoriesWithSubjects;
    },
    enabled: !!sessionCategories,
    // ✅ ENHANCED: More aggressive real-time updates
    staleTime: 0, // Force fresh data
    gcTime: 0, // Don't cache
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    refetchInterval: false, // Disable polling, rely on real-time subscriptions
  });
};

/**
 * Hook untuk mendapatkan mata pelajaran yang tersedia untuk kelas tertentu
 * berdasarkan session_categories
 */
export const useClassSubjectsByCategory = (classId: string | null) => {
  const { data: allCategoriesWithSubjects } = useAllSubjectsByCategory();

  return useQuery({
    queryKey: ['class-subjects-by-category', classId, allCategoriesWithSubjects],
    queryFn: async (): Promise<CategoryWithSubjects[]> => {
      if (!classId || !allCategoriesWithSubjects) return allCategoriesWithSubjects || [];

      // Get class subjects (KBM subjects assigned to this class)
      const { data: classSubjects } = await supabase
        .from('class_subjects')
        .select('subject_id')
        .eq('class_id', classId);

      // Get extracurricular classes (extracurriculars assigned to this class)
      const { data: extracurricularClasses } = await supabase
        .from('extracurricular_classes')
        .select('extracurricular_id')
        .eq('class_id', classId);

      const assignedSubjectIds = new Set(classSubjects?.map(cs => cs.subject_id) || []);
      const assignedExtracurricularIds = new Set(extracurricularClasses?.map(ec => ec.extracurricular_id) || []);

      // Filter subjects based on class assignments
      const filteredCategories = allCategoriesWithSubjects.map(category => ({
        ...category,
        subjects: category.subjects.filter(subject => {
          if (subject.type === 'subject') {
            return assignedSubjectIds.has(subject.id);
          } else if (subject.type === 'extracurricular') {
            return assignedExtracurricularIds.has(subject.id);
          }
          return false;
        })
      }));

      console.log('📚 Class subjects by category:', {
        classId,
        totalCategories: filteredCategories.length,
        categoriesWithSubjects: filteredCategories.map(cat => ({
          name: cat.name,
          subjectCount: cat.subjects.length,
          subjects: cat.subjects.map(s => ({ name: s.name, type: s.type }))
        }))
      });

      return filteredCategories;
    },
    enabled: !!classId && !!allCategoriesWithSubjects,
    staleTime: 30000,
    gcTime: 300000,
  });
};
