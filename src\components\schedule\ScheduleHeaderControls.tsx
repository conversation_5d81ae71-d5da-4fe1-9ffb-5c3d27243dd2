import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Calculator, Copy, Settings, Calendar, ChevronLeft, ChevronRight, School, X, Trash2, Merge, Share2, ExternalLink } from 'lucide-react';
import { ClassFilter } from './ClassFilter';
import { useSchools } from '@/hooks/useSchools';
import { useActiveAcademicYear } from '@/hooks/useAcademicYears';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useJPCalculation } from '@/hooks/useJPCalculation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { JPCalculationModal } from './JPCalculationModal';
import { CopyScheduleModal } from './CopyScheduleModal';
import { DeleteScheduleModal } from './DeleteScheduleModal';
import { MergeScheduleModal } from './MergeScheduleModal';
import { useQueryClient } from '@tanstack/react-query';
import { useScheduleRealTime } from '@/hooks/useRealTimeSync';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
interface ScheduleHeaderControlsProps {
  selectedClassId: string | null;
  onClassChange: (classId: string | null) => void;
  selectedWeek: number;
  onWeekSelect: (week: number) => void;
  onCurrentWeekClick: () => void;
}
export const ScheduleHeaderControls: React.FC<ScheduleHeaderControlsProps> = ({
  selectedClassId,
  onClassChange,
  selectedWeek,
  onWeekSelect,
  onCurrentWeekClick
}) => {
  const { data: school } = useSchools();
  const { data: activeAcademicYear } = useActiveAcademicYear();
  const { academicWeeks } = useAcademicWeeks();
  const [showJPModal, setShowJPModal] = useState(false);
  const [showCopyModal, setShowCopyModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showMergeModal, setShowMergeModal] = useState(false);

  const { toast } = useToast();
  const { profile } = useAuth();

  // ✅ REAL-TIME SYNC: Use query client and real-time hooks for proper data refresh
  const queryClient = useQueryClient();
  const { forceRefresh } = useScheduleRealTime();

  // ✅ GENERATE PUBLIC LINK: Create shareable token for external view
  const generatePublicLink = () => {
    if (!selectedClassId || !profile?.school_id) {
      toast({
        title: "Pilih Kelas",
        description: "Silakan pilih kelas terlebih dahulu untuk membuat link publik",
        variant: "destructive"
      });
      return;
    }

    try {
      // Create simple token: base64(class_id:school_id)
      const tokenData = `${selectedClassId}:${profile.school_id}`;
      const token = btoa(tokenData);
      const publicUrl = `${window.location.origin}/external/${token}`;

      // Copy to clipboard
      navigator.clipboard.writeText(publicUrl).then(() => {
        toast({
          title: "Link Publik Berhasil Dibuat! 🎉",
          description: "Link telah disalin ke clipboard. Bagikan link ini untuk akses publik tanpa login.",
        });
      }).catch(() => {
        // Fallback: show the link in a prompt
        prompt("Link Publik (Copy manual):", publicUrl);
        toast({
          title: "Link Publik Dibuat",
          description: "Silakan copy link dari dialog yang muncul",
        });
      });

      console.log('✅ Public link generated:', {
        classId: selectedClassId,
        schoolId: profile.school_id,
        token,
        publicUrl
      });

    } catch (error) {
      console.error('❌ Error generating public link:', error);
      toast({
        title: "Gagal Membuat Link",
        description: "Terjadi kesalahan saat membuat link publik",
        variant: "destructive"
      });
    }
  };

  // ✅ JP Calculation (keep for backward compatibility)
  const { jpData, totalJP } = useJPCalculation({
    selectedClassId: selectedClassId || undefined,
    selectedWeek
  });

  const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);

  const goToPreviousWeek = () => {
    if (selectedWeek > 1) {
      onWeekSelect(selectedWeek - 1);
    }
  };

  const goToNextWeek = () => {
    if (selectedWeek < academicWeeks.length) {
      onWeekSelect(selectedWeek + 1);
    }
  };
  return <div className="p-2 space-y-2 bg-transparent relative">
      <div className="flex items-center justify-between p-2 rounded-lg">
        {/* Left Section: Filter Only */}
        <div className="flex items-center gap-4 flex-1">
          <ClassFilter selectedClassId={selectedClassId} onClassChange={onClassChange} />
        </div>

        {/* Center Section: School Info with Logo */}
        <div className="flex items-center gap-6 flex-1 justify-center">
          <div className="flex items-center justify-center w-14 h-14 rounded-none overflow-hidden">
            {school?.logo_url ? (
              <img
                src={school.logo_url}
                alt="Logo Sekolah"
                className="w-full h-full object-cover"
              />
            ) : (
              <School className="h-6 w-6 text-foreground" />
            )}
          </div>
          <div className="flex flex-col text-center">
            <div className="text-foreground font-semibold text-base leading-tight">
              {school?.name || 'SMA Negeri 1 Jakarta'}
            </div>
            <div className="text-muted-foreground text-sm leading-tight">
              {school?.address || 'Jl. Pendidikan No. 123, Jakarta Pusat'}
            </div>
            <div className="text-muted-foreground text-xs">
              Tahun Akademik {activeAcademicYear?.year_name || '2025/2026'}
            </div>
          </div>
        </div>

        {/* Right Section: Weekly Navigation + Action Buttons */}
        <div className="flex gap-3 flex-1 justify-end items-center">
          {/* Weekly Navigation */}
          <Button
            variant="outline"
            size="sm"
            onClick={goToPreviousWeek}
            disabled={selectedWeek <= 1}
            className="bg-background border-border text-foreground hover:bg-primary hover:text-primary-foreground flex items-center justify-center"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="text-center">
            <div className="text-bold text-foreground px-2 py-1 text-sm">
              Minggu {selectedWeek}
            </div>
            {currentWeek && (
              <div className="text-xs text-muted-foreground whitespace-nowrap">
                {currentWeek.dateRange}
              </div>
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={goToNextWeek}
            disabled={selectedWeek >= academicWeeks.length}
            className="bg-background border-border text-foreground hover:bg-primary hover:text-primary-foreground flex items-center justify-center"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Minggu Ini Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onCurrentWeekClick}
            className="w-10 h-10 rounded-full bg-background border border-blue-500 text-blue-500 hover:bg-blue-500/10 hover:border-blue-500/70 hover:text-blue-600 transition-all duration-300 transform hover:scale-105"
            title="Minggu Ini"
          >
            <Calendar className="h-4 w-4" />
          </Button>

          {/* Action Buttons */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowJPModal(true)}
            className="w-10 h-10 rounded-full bg-background border border-green-500 text-green-500 hover:bg-green-500/10 hover:border-green-500/70 hover:text-green-600 transition-all duration-300 transform hover:scale-105"
            title="Hitung JP"
          >
            <Calculator className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowCopyModal(true)}
            className="w-10 h-10 rounded-full bg-background border border-cyan-500 text-cyan-500 hover:bg-cyan-500/10 hover:border-cyan-500/70 hover:text-cyan-600 transition-all duration-300 transform hover:scale-105"
            title="Salin Jadwal"
          >
            <Copy className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDeleteModal(true)}
            className="w-10 h-10 rounded-full bg-background border border-red-500 text-red-500 hover:bg-red-500/10 hover:border-red-500/70 hover:text-red-600 transition-all duration-300 transform hover:scale-105"
            title="Hapus Jadwal"
          >
            <Trash2 className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowMergeModal(true)}
            className="w-10 h-10 rounded-full bg-background border border-purple-500 text-purple-500 hover:bg-purple-500/10 hover:border-purple-500/70 hover:text-purple-600 transition-all duration-300 transform hover:scale-105"
            title="Merge Jadwal"
          >
            <Merge className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={generatePublicLink}
            className="w-10 h-10 rounded-full bg-background border border-orange-500 text-orange-500 hover:bg-orange-500/10 hover:border-orange-500/70 hover:text-orange-600 transition-all duration-300 transform hover:scale-105"
            title="Buat Link Publik"
          >
            <ExternalLink className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="w-10 h-10 rounded-full bg-lime-400/0 border border-amber-600 text-amber-600 hover:bg-gray-700/20 hover:border-amber-600/50 hover:text-amber-600 transition-all duration-300 transform hover:scale-105"
            title="Setting"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* ✅ JP Calculation Modal */}
      <JPCalculationModal
        isOpen={showJPModal}
        onClose={() => setShowJPModal(false)}
        selectedClassId={selectedClassId || undefined}
        selectedWeek={selectedWeek}
      />

      {/* ✅ Copy Schedule Modal */}
      <CopyScheduleModal
        isOpen={showCopyModal}
        onClose={() => setShowCopyModal(false)}
        selectedClassId={selectedClassId || undefined}
        currentWeek={selectedWeek}
      />

      {/* ✅ Delete Schedule Modal */}
      <DeleteScheduleModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        selectedClassId={selectedClassId || undefined}
        currentWeek={selectedWeek}
        onScheduleDeleted={() => {
          console.log('🔄 Schedule deleted, refreshing data without page reload...');

          // ✅ FIXED: Invalidate queries instead of full page reload to stay on current week
          queryClient.invalidateQueries({ queryKey: ['schedules'] });
          queryClient.invalidateQueries({ queryKey: ['schedules-simple'] });
          queryClient.invalidateQueries({ queryKey: ['schedules', selectedClassId] });
          queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
          queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

          // Force refresh real-time data
          forceRefresh();

          console.log('✅ Data refresh completed, staying on week', selectedWeek);
        }}
      />

      {/* ✅ Merge Schedule Modal */}
      <MergeScheduleModal
        isOpen={showMergeModal}
        onClose={() => setShowMergeModal(false)}
        selectedClassId={selectedClassId || undefined}
        currentWeek={selectedWeek}
      />
    </div>;
};