import React, { useState, useMemo } from 'react';
import { 
  Calendar, 
  Clock, 
  Users, 
  BookOpen, 
  Bell, 
  Plus, 
  CalendarDays,
  Target,
  Activity,
  BarChart3,
  CheckCircle,
  School,
  TrendingUp,
  Filter,
  PieChart
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { format, isToday, startOfMonth, endOfMonth, eachDayOfInterval, getDay } from 'date-fns';
import { id } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';
import { PieChart as RechartsPieChart, Cell, ResponsiveContainer, BarChart, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, Pie } from 'recharts';

// Import hooks untuk data real
import { useSchedulesComplete } from '@/hooks/useSchedulesPaginated';
import { useHolidays } from '@/hooks/useHolidays';
import { useClasses } from '@/hooks/useClasses';
import { useScheduleSubjects } from '@/hooks/useScheduleSubjects';
import { useTeachers } from '@/hooks/useTeachers';
import { useActiveAcademicYear } from '@/hooks/useAcademicYears';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useAuth } from '@/contexts/AuthContext';

interface DashboardProps {
  username: string;
}

const Dashboard = ({ username }: DashboardProps) => {
  const [selectedDate] = useState(new Date());
  const [selectedClassId, setSelectedClassId] = useState<string>('all');
  const { user } = useAuth();
  const navigate = useNavigate();

  // Data hooks
  const { data: schedules = [], isLoading: schedulesLoading } = useSchedulesComplete();
  const { holidays = [], isLoading: holidaysLoading } = useHolidays();
  const { data: classes = [] } = useClasses();
  const { data: scheduleSubjects = [] } = useScheduleSubjects();
  const { data: teachers = [] } = useTeachers();
  const { data: activeAcademicYear } = useActiveAcademicYear();
  const { academicWeeks } = useAcademicWeeks();

  // Filter schedules berdasarkan kelas yang dipilih
  const filteredSchedules = useMemo(() => {
    if (selectedClassId === 'all') return schedules;
    return schedules.filter(schedule => schedule.class_id === selectedClassId);
  }, [schedules, selectedClassId]);

  // Get selected class info
  const selectedClass = useMemo(() => {
    if (selectedClassId === 'all') return null;
    return classes.find(cls => cls.id === selectedClassId);
  }, [classes, selectedClassId]);

  // Statistik utama berdasarkan filter kelas
  const mainStats = useMemo(() => {
    const today = new Date();
    const currentWeek = academicWeeks.find(week => week.isCurrentWeek);
    
    // Jadwal hari ini
    const todaySchedules = filteredSchedules.filter(schedule => {
      if (!currentWeek) return false;
      const scheduleWeek = parseInt(schedule.academic_week?.toString() || '0');
      const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay();
      return scheduleWeek === currentWeek.weekNumber && schedule.day_of_week === dayOfWeek;
    });

    // Jadwal minggu ini
    const thisWeekSchedules = filteredSchedules.filter(schedule => {
      if (!currentWeek) return false;
      const scheduleWeek = parseInt(schedule.academic_week?.toString() || '0');
      return scheduleWeek === currentWeek.weekNumber;
    });

    // Hari efektif tersisa
    const currentDate = new Date();
    const yearEndDate = activeAcademicYear ? new Date(activeAcademicYear.end_date) : new Date();
    
    const daysToCheck = eachDayOfInterval({ start: currentDate, end: yearEndDate });
    const effectiveDays = daysToCheck.filter(day => {
      const dayOfWeek = getDay(day);
      if (dayOfWeek === 0) return false;
      
      const dayString = format(day, 'yyyy-MM-dd');
      const hasHoliday = holidays.some(holiday => {
        const holidayStart = format(new Date(holiday.start_date), 'yyyy-MM-dd');
        const holidayEnd = format(new Date(holiday.end_date), 'yyyy-MM-dd');
        return dayString >= holidayStart && dayString <= holidayEnd;
      });
      
      return !hasHoliday;
    }).length;

    // Unique subjects dan teachers untuk kelas yang dipilih
    const uniqueSubjects = [...new Set(filteredSchedules.map(s => s.subject_id))];
    const uniqueTeachers = [...new Set(filteredSchedules.map(s => s.teacher_id).filter(Boolean))];

    return {
      todaySchedules: todaySchedules.length,
      thisWeekSchedules: thisWeekSchedules.length,
      totalClasses: selectedClassId === 'all' ? classes.length : 1,
      totalSubjects: selectedClassId === 'all' ? scheduleSubjects.length : uniqueSubjects.length,
      totalTeachers: selectedClassId === 'all' ? teachers.length : uniqueTeachers.length,
      effectiveDaysLeft: effectiveDays,
      totalSchedules: filteredSchedules.length
    };
  }, [filteredSchedules, classes, scheduleSubjects, teachers, holidays, academicWeeks, activeAcademicYear, selectedClassId]);

  // Jadwal hari ini dengan status
  const todaySchedules = useMemo(() => {
    const today = new Date();
    const currentWeek = academicWeeks.find(week => week.isCurrentWeek);
    
    if (!currentWeek) return [];
    
    const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay();
    const todaySchedulesList = filteredSchedules.filter(schedule => {
      const scheduleWeek = parseInt(schedule.academic_week?.toString() || '0');
      return scheduleWeek === currentWeek.weekNumber && schedule.day_of_week === dayOfWeek;
    });

    return todaySchedulesList
      .sort((a, b) => (a.start_time || '').localeCompare(b.start_time || ''))
      .map(schedule => {
        const now = new Date();
        const currentTime = format(now, 'HH:mm');
        const startTime = schedule.start_time || '';
        const endTime = schedule.end_time || '';
        
        let status: 'upcoming' | 'ongoing' | 'completed' = 'upcoming';
        if (currentTime >= startTime && currentTime <= endTime) {
          status = 'ongoing';
        } else if (currentTime > endTime) {
          status = 'completed';
        }

        return {
          ...schedule,
          status,
          progress: status === 'ongoing' ? 
            Math.round(((new Date(`2000-01-01 ${currentTime}`).getTime() - new Date(`2000-01-01 ${startTime}`).getTime()) / 
            (new Date(`2000-01-01 ${endTime}`).getTime() - new Date(`2000-01-01 ${startTime}`).getTime())) * 100) : 0
        };
      });
  }, [filteredSchedules, academicWeeks]);

  // Event mendatang
  const upcomingEvents = useMemo(() => {
    const today = new Date();
    return holidays
      .filter(holiday => new Date(holiday.start_date) >= today)
      .sort((a, b) => new Date(a.start_date).getTime() - new Date(b.start_date).getTime())
      .slice(0, 5);
  }, [holidays]);

  // Calendar data untuk mini calendar
  const calendarData = useMemo(() => {
    const today = new Date();
    const monthStart = startOfMonth(today);
    const monthEnd = endOfMonth(today);
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });
    
    return daysInMonth.map(day => {
      const dayString = format(day, 'yyyy-MM-dd');
      const hasEvent = holidays.some(holiday => {
        const holidayStart = format(new Date(holiday.start_date), 'yyyy-MM-dd');
        const holidayEnd = format(new Date(holiday.end_date), 'yyyy-MM-dd');
        return dayString >= holidayStart && dayString <= holidayEnd;
      });
      
      const dayOfWeek = day.getDay() === 0 ? 7 : day.getDay();
      const currentWeek = academicWeeks.find(week => week.isCurrentWeek);
      const hasSchedules = currentWeek && filteredSchedules.some(schedule => {
        const scheduleWeek = parseInt(schedule.academic_week?.toString() || '0');
        return scheduleWeek === currentWeek.weekNumber && schedule.day_of_week === dayOfWeek;
      });

      return {
        date: day,
        isToday: isToday(day),
        hasEvent,
        hasSchedules,
        dayNumber: day.getDate()
      };
    });
  }, [holidays, filteredSchedules, academicWeeks]);

  // Data untuk Donut Chart - Distribusi Mata Pelajaran
  const subjectDistribution = useMemo(() => {
    const subjectCounts = filteredSchedules.reduce((acc, schedule) => {
      const subjectName = schedule.subject_name || 'Unknown';
      acc[subjectName] = (acc[subjectName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const colors = ['#84cc16', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#06b6d4', '#10b981'];
    
    return Object.entries(subjectCounts)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 8) // Top 8 subjects
      .map(([name, value], index) => ({
        name,
        value: value as number,
        color: colors[index % colors.length]
      }));
  }, [filteredSchedules]);

  // Data untuk Bar Chart - Distribusi per Hari
  const weeklyAnalysis = useMemo(() => {
    const currentWeek = academicWeeks.find(week => week.isCurrentWeek);
    if (!currentWeek) return [];

    const days = [
      { name: 'Senin', key: 1 },
      { name: 'Selasa', key: 2 },
      { name: 'Rabu', key: 3 },
      { name: 'Kamis', key: 4 },
      { name: 'Jumat', key: 5 },
      { name: 'Sabtu', key: 6 },
      { name: 'Minggu', key: 7 }
    ];
    
    return days.map(({ name, key }) => {
      const daySchedules = filteredSchedules.filter(schedule => {
        const scheduleWeek = parseInt(schedule.academic_week?.toString() || '0');
        return scheduleWeek === currentWeek.weekNumber && schedule.day_of_week === key;
      });

      return {
        day: name,
        jadwal: daySchedules.length,
        percentage: filteredSchedules.length > 0 ? (daySchedules.length / filteredSchedules.length) * 100 : 0
      };
    });
  }, [filteredSchedules, academicWeeks]);

  // Data untuk Status Chart
  const statusDistribution = useMemo(() => {
    const statusCounts = todaySchedules.reduce((acc, schedule) => {
      acc[schedule.status] = (acc[schedule.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return [
      { name: 'Akan Datang', value: statusCounts.upcoming || 0, color: '#3b82f6' },
      { name: 'Berlangsung', value: statusCounts.ongoing || 0, color: '#10b981' },
      { name: 'Selesai', value: statusCounts.completed || 0, color: '#6b7280' }
    ].filter(item => item.value > 0);
  }, [todaySchedules]);

  if (schedulesLoading || holidaysLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-400 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Memuat dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Background effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-lime-400/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Welcome Section with Class Filter */}
        <div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-foreground mb-2">
                Selamat Datang, {username}!
              </h1>
              <p className="text-muted-foreground">
                Tahun Akademik {activeAcademicYear?.year_name || '2024/2025'}
                {selectedClass && (
                  <span className="ml-2 px-2 py-1 bg-lime-400/20 text-lime-400 rounded-md text-sm font-medium">
                    {selectedClass.name}
                  </span>
                )}
              </p>
            </div>
            
            {/* Class Filter */}
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                <SelectTrigger className="w-48 bg-card border-border">
                  <SelectValue placeholder="Pilih Kelas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kelas</SelectItem>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name} - {cls.level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Data Source Info */}
          <div className="mt-4 p-3 bg-muted/30 rounded-lg border border-border">
            <p className="text-sm text-muted-foreground">
              <span className="font-medium">Sumber Data:</span>
              {selectedClassId === 'all'
                ? ` Menampilkan data dari semua ${classes.length} kelas dengan total ${mainStats.totalSchedules} jadwal`
                : ` Menampilkan data khusus untuk kelas ${selectedClass?.name} dengan ${mainStats.totalSchedules} jadwal`
              }
            </p>
          </div>
        </div>

        {/* Main Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-lime-400/10 to-lime-600/10 border-lime-400/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-lime-400">Jadwal Hari Ini</CardTitle>
              <Clock className="h-4 w-4 text-lime-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{mainStats.todaySchedules}</div>
              <p className="text-xs text-muted-foreground">
                dari {mainStats.thisWeekSchedules} jadwal minggu ini
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blue-400/10 to-blue-600/10 border-blue-400/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-400">
                {selectedClassId === 'all' ? 'Total Kelas' : 'Mata Pelajaran'}
              </CardTitle>
              <School className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">
                {selectedClassId === 'all' ? mainStats.totalClasses : mainStats.totalSubjects}
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedClassId === 'all' ? 'kelas aktif' : 'mata pelajaran unik'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-400/10 to-purple-600/10 border-purple-400/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-400">Total Guru</CardTitle>
              <Users className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{mainStats.totalTeachers}</div>
              <p className="text-xs text-muted-foreground">
                {selectedClassId === 'all' ? 'guru aktif' : 'guru mengajar'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-400/10 to-orange-600/10 border-orange-400/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-400">Hari Efektif</CardTitle>
              <CalendarDays className="h-4 w-4 text-orange-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{mainStats.effectiveDaysLeft}</div>
              <p className="text-xs text-muted-foreground">
                hari tersisa tahun ini
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Donut Chart - Subject Distribution */}
          <Card className="bg-card/40 backdrop-blur-sm border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-purple-400" />
                Distribusi Mata Pelajaran
              </CardTitle>
              <CardDescription>
                {selectedClassId === 'all' 
                  ? 'Distribusi jadwal per mata pelajaran (semua kelas)'
                  : `Distribusi jadwal mata pelajaran untuk kelas ${selectedClass?.name}`
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {subjectDistribution.length > 0 ? (
                <div className="flex items-center gap-6">
                  <div className="flex-1">
                    <ResponsiveContainer width="100%" height={200}>
                      <RechartsPieChart>
                        <Pie
                          data={subjectDistribution}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={80}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {subjectDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="flex-1 space-y-2">
                    {subjectDistribution.map((item, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: item.color }}
                        ></div>
                        <span className="flex-1 truncate">{item.name}</span>
                        <span className="font-medium">{item.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Tidak ada data jadwal untuk ditampilkan
                </div>
              )}
            </CardContent>
          </Card>

          {/* Bar Chart - Weekly Distribution */}
          <Card className="bg-card/40 backdrop-blur-sm border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-400" />
                Distribusi Jadwal Mingguan
              </CardTitle>
              <CardDescription>
                {selectedClassId === 'all' 
                  ? 'Jumlah jadwal per hari dalam seminggu (semua kelas)'
                  : `Jadwal per hari untuk kelas ${selectedClass?.name}`
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {weeklyAnalysis.length > 0 ? (
                <ResponsiveContainer width="100%" height={200}>
                  <BarChart data={weeklyAnalysis}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="jadwal" fill="#84cc16" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Tidak ada data jadwal mingguan
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Calendar & Today's Schedule */}
          <div className="lg:col-span-2 space-y-6">
            {/* Mini Calendar */}
            <Card className="bg-card/40 backdrop-blur-sm border-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-lime-400" />
                  Kalender {format(selectedDate, 'MMMM yyyy', { locale: id })}
                </CardTitle>
                <CardDescription>
                  {selectedClassId === 'all' 
                    ? 'Kalender dengan highlight jadwal dari semua kelas'
                    : `Kalender dengan highlight jadwal kelas ${selectedClass?.name}`
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-7 gap-2 mb-4">
                  {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map((day) => (
                    <div key={day} className="text-center text-sm text-muted-foreground font-medium p-2">
                      {day}
                    </div>
                  ))}
                </div>
                <div className="grid grid-cols-7 gap-2">
                  {calendarData.map((day, index) => (
                    <div
                      key={index}
                      className={`
                        h-10 flex items-center justify-center text-sm rounded-lg cursor-pointer transition-all duration-200 relative
                        ${day.isToday 
                          ? 'bg-lime-400 text-lime-900 font-bold' 
                          : day.hasEvent 
                            ? 'bg-red-400/20 text-red-400 hover:bg-red-400/30' 
                            : day.hasSchedules
                              ? 'bg-blue-400/20 text-blue-400 hover:bg-blue-400/30'
                              : 'text-muted-foreground hover:bg-muted/50'
                        }
                      `}
                    >
                      {day.dayNumber}
                      {day.hasEvent && (
                        <div className="absolute top-1 right-1 w-2 h-2 bg-red-400 rounded-full"></div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Today's Schedule */}
            <Card className="bg-card/40 backdrop-blur-sm border-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-blue-400" />
                  Jadwal Hari Ini
                </CardTitle>
                <CardDescription>
                  {format(new Date(), 'EEEE, dd MMMM yyyy', { locale: id })}
                  {selectedClassId !== 'all' && (
                    <span className="ml-2 text-blue-400">• Kelas {selectedClass?.name}</span>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {todaySchedules.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      {selectedClassId === 'all' 
                        ? 'Tidak ada jadwal hari ini'
                        : `Tidak ada jadwal hari ini untuk kelas ${selectedClass?.name}`
                      }
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {todaySchedules.map((schedule, index) => (
                      <div key={index} className="flex items-center gap-4 p-4 bg-muted/30 rounded-lg">
                        <div className="flex-shrink-0">
                          <div className={`w-3 h-3 rounded-full ${
                            schedule.status === 'ongoing' ? 'bg-green-400' :
                            schedule.status === 'completed' ? 'bg-gray-400' : 'bg-blue-400'
                          }`}></div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-foreground truncate">
                              {schedule.subject_name || 'Mata Pelajaran'}
                            </h4>
                            <Badge variant={
                              schedule.status === 'ongoing' ? 'default' :
                              schedule.status === 'completed' ? 'secondary' : 'outline'
                            }>
                              {schedule.status === 'ongoing' ? 'Berlangsung' :
                               schedule.status === 'completed' ? 'Selesai' : 'Akan Datang'}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {schedule.start_time} - {schedule.end_time}
                            {selectedClassId === 'all' && (
                              <span> • {schedule.class_name}</span>
                            )}
                          </div>
                          {schedule.teacher_name && (
                            <div className="text-sm text-muted-foreground">
                              Guru: {schedule.teacher_name}
                            </div>
                          )}
                          {schedule.status === 'ongoing' && (
                            <Progress value={schedule.progress} className="mt-2 h-2" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Quick Actions & Info */}
          <div className="space-y-6">
            {/* Status Chart for Today */}
            {statusDistribution.length > 0 && (
              <Card className="bg-card/40 backdrop-blur-sm border-border">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-green-400" />
                    Status Jadwal Hari Ini
                  </CardTitle>
                  <CardDescription>
                    {selectedClassId === 'all' 
                      ? 'Status jadwal dari semua kelas'
                      : `Status jadwal kelas ${selectedClass?.name}`
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <ResponsiveContainer width="100%" height={120}>
                        <RechartsPieChart>
                          <Pie
                            data={statusDistribution}
                            cx="50%"
                            cy="50%"
                            innerRadius={30}
                            outerRadius={50}
                            paddingAngle={5}
                            dataKey="value"
                          >
                            {statusDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="flex-1 space-y-2">
                      {statusDistribution.map((item, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: item.color }}
                          ></div>
                          <span className="flex-1">{item.name}</span>
                          <span className="font-medium">{item.value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions */}
            <Card className="bg-card/40 backdrop-blur-sm border-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5 text-lime-400" />
                  Aksi Cepat
                </CardTitle>
                <CardDescription>
                  Navigasi cepat ke fitur utama
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={() => navigate('/schedules')}
                  className="w-full justify-start bg-lime-400/10 hover:bg-lime-400/20 text-lime-400 border-lime-400/20"
                  variant="outline"
                >
                  <BookOpen className="h-4 w-4 mr-2" />
                  Kelola Jadwal
                </Button>
                <Button
                  onClick={() => navigate('/classes')}
                  className="w-full justify-start bg-blue-400/10 hover:bg-blue-400/20 text-blue-400 border-blue-400/20"
                  variant="outline"
                >
                  <School className="h-4 w-4 mr-2" />
                  Kelola Kelas
                </Button>
                <Button
                  onClick={() => navigate('/holidays')}
                  className="w-full justify-start bg-purple-400/10 hover:bg-purple-400/20 text-purple-400 border-purple-400/20"
                  variant="outline"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Kelola Event
                </Button>
              </CardContent>
            </Card>

            {/* Upcoming Events */}
            <Card className="bg-card/40 backdrop-blur-sm border-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5 text-orange-400" />
                  Event Mendatang
                </CardTitle>
                <CardDescription>
                  5 event terdekat dalam kalender
                </CardDescription>
              </CardHeader>
              <CardContent>
                {upcomingEvents.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground text-sm">Tidak ada event mendatang</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {upcomingEvents.map((event, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-muted/20 rounded-lg">
                        <div className="flex-shrink-0 w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-foreground text-sm truncate">
                            {event.name}
                          </h4>
                          <p className="text-xs text-muted-foreground">
                            {format(new Date(event.start_date), 'dd MMM yyyy', { locale: id })}
                            {event.start_date !== event.end_date && (
                              <span> - {format(new Date(event.end_date), 'dd MMM yyyy', { locale: id })}</span>
                            )}
                          </p>
                          {event.description && (
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                              {event.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* System Summary */}
            <Card className="bg-card/40 backdrop-blur-sm border-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-400" />
                  Ringkasan Sistem
                </CardTitle>
                <CardDescription>
                  {selectedClassId === 'all'
                    ? 'Statistik keseluruhan sistem'
                    : `Statistik untuk kelas ${selectedClass?.name}`
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Jadwal</span>
                  <span className="font-medium">{mainStats.totalSchedules}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Jadwal Minggu Ini</span>
                  <span className="font-medium">{mainStats.thisWeekSchedules}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Mata Pelajaran</span>
                  <span className="font-medium">{mainStats.totalSubjects}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Guru Aktif</span>
                  <span className="font-medium">{mainStats.totalTeachers}</span>
                </div>
                <div className="pt-2 border-t border-border">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Tahun Akademik</span>
                    <span className="font-medium text-xs">{activeAcademicYear?.year_name}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
