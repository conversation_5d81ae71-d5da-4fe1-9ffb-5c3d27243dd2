-- =====================================================
-- ADD LEARNING FIELDS TO SCHEDULES TABLE
-- =====================================================
-- Add tujuan_pembelajaran and materi_pembelajaran fields to schedules table

-- 1. Add new columns to schedules table
ALTER TABLE schedules 
ADD COLUMN IF NOT EXISTS tujuan_pembelajaran TEXT,
ADD COLUMN IF NOT EXISTS materi_pembelajaran TEXT;

-- 2. Add comments for documentation
COMMENT ON COLUMN schedules.tujuan_pembelajaran IS 'Tujuan pembelajaran untuk jadwal ini';
COMMENT ON COLUMN schedules.materi_pembelajaran IS 'Materi pembelajaran untuk jadwal ini';

-- 3. Create index for better performance when searching by learning objectives
CREATE INDEX IF NOT EXISTS idx_schedules_tujuan_pembelajaran ON schedules USING gin(to_tsvector('indonesian', tujuan_pembelajaran));
CREATE INDEX IF NOT EXISTS idx_schedules_materi_pembelajaran ON schedules USING gin(to_tsvector('indonesian', materi_pembelajaran));

RAISE NOTICE 'Learning fields added to schedules table successfully';
