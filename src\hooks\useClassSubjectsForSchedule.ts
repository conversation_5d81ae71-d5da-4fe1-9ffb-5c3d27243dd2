import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useSubjects } from './useSubjects';
import { useExtracurriculars } from './useExtracurriculars';

export interface SubjectForSchedule {
  id: string;
  name: string;
  code?: string;
  category: string;
  type: 'subject' | 'extracurricular';
}

export const useClassSubjectsForSchedule = (classId: string | null) => {
  const { data: allSubjects } = useSubjects();
  const { data: allExtracurriculars } = useExtracurriculars();

  return useQuery({
    queryKey: ['class-subjects-for-schedule', classId],
    queryFn: async (): Promise<SubjectForSchedule[]> => {
      if (!classId) return [];

      // Get class subjects (KBM subjects assigned to this class)
      console.log('🔍 useClassSubjectsForSchedule: Fetching for class:', classId);

      const { data: classSubjects, error: classSubjectsError } = await supabase
        .from('class_schedules')
        .select(`
          subject_id,
          schedule_subjects (
            id,
            name,
            code,
            session_categories (
              name
            )
          )
        `)
        .eq('class_id', classId)
        .is('day_of_week', null); // Only unscheduled subjects

      if (classSubjectsError) {
        console.error('❌ useClassSubjectsForSchedule error:', classSubjectsError);
        console.log('⚠️ Returning empty array to prevent errors');
        // Don't throw, just return empty array to prevent cascading errors
        return [] as SubjectForSchedule[];
      }

      // Get extracurricular classes (extracurriculars assigned to this class)
      const { data: extracurricularClasses, error: extracurricularError } = await supabase
        .from('extracurricular_classes')
        .select(`
          extracurricular_id,
          extracurriculars (
            id,
            name
          )
        `)
        .eq('class_id', classId);

      if (extracurricularError) {
        console.error('Error fetching extracurricular classes:', extracurricularError);
        throw extracurricularError;
      }

      // Combine subjects and extracurriculars
      const subjects: SubjectForSchedule[] = [];

      // Add KBM subjects
      if (classSubjects) {
        classSubjects.forEach(cs => {
          if (cs.schedule_subjects) {
            subjects.push({
              id: cs.schedule_subjects.id,
              name: cs.schedule_subjects.name,
              code: cs.schedule_subjects.code,
              category: cs.schedule_subjects.session_categories?.name || 'KBM',
              type: 'subject'
            });
          }
        });
      }

      // Add extracurriculars
      if (extracurricularClasses) {
        extracurricularClasses.forEach(ec => {
          if (ec.extracurriculars) {
            subjects.push({
              id: ec.extracurriculars.id,
              name: ec.extracurriculars.name,
              code: undefined,
              category: 'ekstrakurikuler',
              type: 'extracurricular'
            });
          }
        });
      }

      console.log('📚 Class subjects for schedule:', {
        classId,
        totalSubjects: subjects.length,
        kbmSubjects: subjects.filter(s => s.type === 'subject').length,
        extracurriculars: subjects.filter(s => s.type === 'extracurricular').length,
        subjects
      });

      return subjects;
    },
    enabled: !!classId,
    staleTime: 30000,
    gcTime: 300000,
  });
};

// Helper function to group subjects by category
export const useGroupedClassSubjects = (classId: string | null) => {
  const { data: subjects } = useClassSubjectsForSchedule(classId);

  const groupedSubjects = subjects?.reduce((acc, subject) => {
    const category = subject.category || 'lainnya';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(subject);
    return acc;
  }, {} as Record<string, SubjectForSchedule[]>) || {};

  return {
    data: groupedSubjects,
    categories: Object.keys(groupedSubjects),
    totalSubjects: subjects?.length || 0
  };
};
