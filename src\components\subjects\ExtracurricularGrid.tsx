import React, { useState } from 'react';
import { Button } from '@/components/ui/button';

import { Plus, Trash2, Edit3, Trophy } from 'lucide-react';
import { useClasses } from '@/hooks/useClasses';
import { useExtracurriculars, useDeleteExtracurricular } from '@/hooks/useExtracurriculars';
import { useExtracurricularClasses, useCreateExtracurricularClass, useDeleteExtracurricularClass } from '@/hooks/useExtracurricularClasses';
import AddExtracurricularModal from '@/components/modals/AddExtracurricularModal';
import DeleteConfirmationModal from '@/components/modals/DeleteConfirmationModal';
interface ExtracurricularGridProps {
  activeAcademicYear: any;
  onEditAssignment?: (assignment: any, extracurricularName: string, className: string) => void;
}
const ExtracurricularGrid: React.FC<ExtracurricularGridProps> = ({
  activeAcademicYear,
  onEditAssignment
}) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);

  const [editingExtracurricular, setEditingExtracurricular] = useState<any>(null);
  const [extracurricularToDelete, setExtracurricularToDelete] = useState<any>(null);
  const {
    data: classes = []
  } = useClasses();
  const {
    data: extracurriculars = []
  } = useExtracurriculars();
  const {
    data: extracurricularClasses = []
  } = useExtracurricularClasses();
  const createExtracurricularClass = useCreateExtracurricularClass();
  const deleteExtracurricularClass = useDeleteExtracurricularClass();
  const deleteExtracurricular = useDeleteExtracurricular();
  const sortedClasses = [...classes].sort((a, b) => {
    if (a.level !== b.level) return a.level.localeCompare(b.level);
    if (a.grade !== b.grade) return a.grade - b.grade;
    return a.name.localeCompare(b.name);
  });
  const getExtracurricularClass = (extracurricularId: string, classId: string) => {
    return extracurricularClasses.find(ec => ec.extracurricular_id === extracurricularId && ec.class_id === classId);
  };
  const handleAddExtracurricular = (extracurricularId: string, classId: string) => {
    createExtracurricularClass.mutate({
      extracurricular_id: extracurricularId,
      class_id: classId,
      hours_per_year: 36 // Default 1 hour per week * 36 weeks
    });
  };

  // Handler untuk edit assignment ekstrakurikuler (menggunakan modal seperti mata pelajaran)
  const handleEditAssignment = (assignment: any, extracurricularName: string, className: string) => {
    if (onEditAssignment) {
      onEditAssignment(assignment, extracurricularName, className);
    }
  };

  // Handler untuk delete assignment ekstrakurikuler
  const handleDeleteAssignment = (assignment: any) => {
    if (confirm(`Apakah Anda yakin ingin menghapus assignment ini?`)) {
      deleteExtracurricularClass.mutate(assignment.id);
    }
  };

  // Handler untuk edit ekstrakurikuler
  const handleEditExtracurricular = (extracurricular: any) => {
    setEditingExtracurricular(extracurricular);
    setIsAddModalOpen(true);
  };

  // Handler untuk delete ekstrakurikuler
  const handleDeleteExtracurricular = (extracurricular: any) => {
    setExtracurricularToDelete(extracurricular);
    setIsDeleteConfirmModalOpen(true);
  };

  // Handler untuk konfirmasi delete ekstrakurikuler
  const handleConfirmDeleteExtracurricular = () => {
    if (extracurricularToDelete) {
      deleteExtracurricular.mutate(extracurricularToDelete.id);
      setExtracurricularToDelete(null);
    }
  };

  // Handler untuk menutup modal add/edit
  const handleCloseAddModal = (open: boolean) => {
    setIsAddModalOpen(open);
    if (!open) {
      setEditingExtracurricular(null);
    }
  };
  if (!activeAcademicYear) {
    return <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-6">
              <p className="text-orange-400">Tidak ada tahun akademik yang aktif. Silakan buat tahun akademik terlebih dahulu.</p>
            </div>
          </div>
        </div>
      </div>;
  }
  return <div className="overflow-x-auto">
      <div className="min-w-max">
        {/* Header with glow effect */}
        {/* <div className="mb-8 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-lime-400/20 to-blue-400/20 blur-xl rounded-2xl"></div>
          <Card className="relative bg-gray-800/90 border-gray-700 backdrop-blur-sm">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-2xl font-bold text-foreground">
                  Manajemen Ekstrakurikuler
                </CardTitle>
                <Button onClick={() => setIsAddModalOpen(true)} className="bg-orange-500 hover:bg-orange-600 text-white shadow-lg hover:shadow-orange-500/25">
                  <Plus className="w-4 h-4 mr-2" />
                  Tambah Ekstrakurikuler
                </Button>
              </div>
            </CardHeader>
          </Card>
        </div> */}

        {/* Matrix Grid with enhanced styling - similar to SubjectMatrixGrid */}
        {/* Header with class names */}
        <div className="grid grid-cols-[250px_repeat(auto-fit,minmax(120px,1fr))] gap-2 p-4">
          <div className="flex items-center justify-center bg-muted/20 border border-border rounded-lg p-3 shadow-sm">
            <div className="text-center">
              <Trophy className="h-5 w-5 text-orange-500 mx-auto mb-1" />
              <div className="text-foreground font-medium text-sm">EKSTRAKURIKULER</div>
            </div>
          </div>
          {sortedClasses.map(classItem => (
            <div key={classItem.id} className="text-center bg-muted/10 border border-border rounded-lg p-3 py-[12px] px-0 shadow-sm">
              <div className="text-foreground font-medium text-sm">Kelas {classItem.level}</div>
              <div className="text-muted-foreground text-xs">{classItem.name}</div>
            </div>
          ))}
        </div>

        {/* Extracurricular rows */}
        <div className="space-y-2 p-4">
          {extracurriculars.map((extracurricular) => {
            return (
              <div key={extracurricular.id} className="grid grid-cols-[250px_repeat(auto-fit,minmax(120px,1fr))] gap-2 items-center">
                {/* Extracurricular name with color-coded design and action buttons */}
                <div
                  className="group relative rounded-xl p-3 h-16 flex flex-col justify-center border-2"
                  style={{
                    backgroundColor: `${extracurricular.color || '#F97316'}20`,
                    borderColor: `${extracurricular.color || '#F97316'}40`
                  }}
                >
                  <div className="text-foreground text-sm font-medium">
                    {extracurricular.name}
                  </div>

                  {/* Action buttons - appear on hover, contained within box */}
                  <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditExtracurricular(extracurricular);
                      }}
                      className="h-5 w-5 p-0 hover:bg-accent/20 text-muted-foreground hover:text-foreground rounded-sm"
                    >
                      <Edit3 className="h-2.5 w-2.5" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteExtracurricular(extracurricular);
                      }}
                      className="h-5 w-5 p-0 hover:bg-red-500/20 text-muted-foreground hover:text-red-400 rounded-sm"
                    >
                      <Trash2 className="h-2.5 w-2.5" />
                    </Button>
                  </div>
                </div>

                {/* Class assignments */}
                {sortedClasses.map(classItem => {
                  const extracurricularClass = getExtracurricularClass(extracurricular.id, classItem.id);

                  return (
                    <div key={classItem.id} className="flex justify-center">
                      {extracurricularClass ? (
                        <div className="group relative h-16 w-full">
                          <div
                            className="p-3 h-16 w-full flex flex-col justify-center items-center cursor-pointer hover:opacity-80 transition-opacity rounded-lg border-2"
                            style={{
                              backgroundColor: `${extracurricular.color || '#F97316'}20`,
                              borderColor: `${extracurricular.color || '#F97316'}60`
                            }}
                          >
                            <div className="text-foreground text-sm font-medium">
                              {extracurricular.name}
                            </div>
                            <div className="text-muted-foreground opacity-70 text-xs mt-1">
                              {extracurricularClass.hours_per_year || extracurricularClass.hours_per_week * 36} JP/Tahun
                            </div>
                          </div>
                          <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1">
                            <Button
                              size="sm"
                              onClick={() => handleEditAssignment(extracurricularClass, extracurricular.name, `${classItem.level} ${classItem.name}`)}
                              className="h-5 w-5 p-0 bg-blue-600/80 hover:bg-blue-700 rounded-sm"
                            >
                              <Edit3 className="w-2.5 h-2.5" />
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleDeleteAssignment(extracurricularClass)}
                              className="h-5 w-5 p-0 bg-red-600/80 hover:bg-red-700 rounded-sm"
                            >
                              <Trash2 className="w-2.5 h-2.5" />
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <Button
                          variant="ghost"
                          onClick={() => handleAddExtracurricular(extracurricular.id, classItem.id)}
                          className="h-16 w-full text-primary hover:bg-primary/10 border border-dashed border-primary/30 flex flex-col items-center justify-center rounded-lg my-0 bg-muted/5"
                        >
                          <Plus className="h-4 w-4 mb-1" />
                          <span className="text-xs">Tambah</span>
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      </div>

      <AddExtracurricularModal
        open={isAddModalOpen}
        onOpenChange={handleCloseAddModal}
        editingExtracurricular={editingExtracurricular}
      />

      <DeleteConfirmationModal
        open={isDeleteConfirmModalOpen}
        onOpenChange={setIsDeleteConfirmModalOpen}
        onConfirm={handleConfirmDeleteExtracurricular}
        title="Hapus Ekstrakurikuler"
        description="Apakah Anda yakin ingin menghapus ekstrakurikuler ini? Semua data terkait akan ikut terhapus."
        itemName={extracurricularToDelete?.name}
        isLoading={deleteExtracurricular.isPending}
      />
    </div>;
};
export default ExtracurricularGrid;