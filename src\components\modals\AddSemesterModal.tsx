import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Clock, Save, X } from 'lucide-react';
import { useCreateSemester, useUpdateSemester } from '@/hooks/useSemesters';
import { useAcademicYears } from '@/hooks/useAcademicYears';
import { Semester, getSemesterDateRange, SEMESTER_CONSTANTS } from '@/types/semester';

interface AddSemesterModalProps {
  isOpen: boolean;
  onClose: () => void;
  editingSemester?: Semester | null;
}

export const AddSemesterModal: React.FC<AddSemesterModalProps> = ({
  isOpen,
  onClose,
  editingSemester
}) => {
  const [formData, setFormData] = useState({
    name: '',
    semester_number: 1 as 1 | 2,
    start_date: '',
    end_date: '',
    academic_year_id: ''
  });

  const { data: academicYears } = useAcademicYears();
  const createSemester = useCreateSemester();
  const updateSemester = useUpdateSemester();

  const isEditing = !!editingSemester;
  const isLoading = createSemester.isPending || updateSemester.isPending;

  useEffect(() => {
    if (editingSemester) {
      setFormData({
        name: editingSemester.name,
        semester_number: editingSemester.semester_number,
        start_date: editingSemester.start_date,
        end_date: editingSemester.end_date,
        academic_year_id: editingSemester.academic_year_id
      });
    } else {
      // Reset form for new semester
      setFormData({
        name: '',
        semester_number: 1,
        start_date: '',
        end_date: '',
        academic_year_id: ''
      });
    }
  }, [editingSemester, isOpen]);

  // Auto-generate dates when academic year and semester number change
  useEffect(() => {
    if (formData.academic_year_id && formData.semester_number) {
      const academicYear = academicYears?.find(year => year.id === formData.academic_year_id);
      if (academicYear) {
        const dateRange = getSemesterDateRange(academicYear.start_date, formData.semester_number);
        setFormData(prev => ({
          ...prev,
          start_date: dateRange.start_date,
          end_date: dateRange.end_date,
          name: formData.semester_number === 1 ? 'Semester 1' : 'Semester 2'
        }));
      }
    }
  }, [formData.academic_year_id, formData.semester_number, academicYears]);

  const handleClose = () => {
    setFormData({
      name: '',
      semester_number: 1,
      start_date: '',
      end_date: '',
      academic_year_id: ''
    });
    onClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.academic_year_id || !formData.start_date || !formData.end_date) {
      return;
    }

    if (isEditing && editingSemester) {
      updateSemester.mutate({
        id: editingSemester.id,
        name: formData.name.trim(),
        semester_number: formData.semester_number,
        start_date: formData.start_date,
        end_date: formData.end_date
      }, {
        onSuccess: handleClose
      });
    } else {
      createSemester.mutate({
        name: formData.name.trim(),
        semester_number: formData.semester_number,
        start_date: formData.start_date,
        end_date: formData.end_date,
        academic_year_id: formData.academic_year_id
      }, {
        onSuccess: handleClose
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-gray-900/95 border-gray-600/30 backdrop-blur-xl text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-semibold">
            <Calendar className="h-5 w-5 text-purple-400" />
            {isEditing ? 'Edit Semester' : 'Tambah Semester'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {!isEditing && (
            <div>
              <Label htmlFor="academic_year" className="text-gray-300 mb-2 block">
                Tahun Ajaran
              </Label>
              <Select 
                value={formData.academic_year_id} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, academic_year_id: value }))}
              >
                <SelectTrigger className="bg-gray-800/50 border-gray-600/30 text-white focus:border-purple-400/50 focus:ring-purple-400/20 rounded-xl h-12">
                  <SelectValue placeholder="Pilih tahun ajaran" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900/95 border-gray-600/30 backdrop-blur-xl">
                  {academicYears?.map((year) => (
                    <SelectItem key={year.id} value={year.id} className="text-white hover:bg-purple-400/20">
                      {year.year_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div>
            <Label htmlFor="semester_number" className="text-gray-300 mb-2 block">
              Semester
            </Label>
            <Select 
              value={formData.semester_number.toString()} 
              onValueChange={(value) => setFormData(prev => ({ ...prev, semester_number: parseInt(value) as 1 | 2 }))}
            >
              <SelectTrigger className="bg-gray-800/50 border-gray-600/30 text-white focus:border-purple-400/50 focus:ring-purple-400/20 rounded-xl h-12">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-900/95 border-gray-600/30 backdrop-blur-xl">
                <SelectItem value="1" className="text-white hover:bg-purple-400/20">
                  <div className="flex items-center gap-2">
                    <span>{SEMESTER_CONSTANTS.SEMESTER_1.name}</span>
                    <span className="text-xs text-gray-400">({SEMESTER_CONSTANTS.SEMESTER_1.description})</span>
                  </div>
                </SelectItem>
                <SelectItem value="2" className="text-white hover:bg-purple-400/20">
                  <div className="flex items-center gap-2">
                    <span>{SEMESTER_CONSTANTS.SEMESTER_2.name}</span>
                    <span className="text-xs text-gray-400">({SEMESTER_CONSTANTS.SEMESTER_2.description})</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="name" className="text-gray-300 mb-2 block">
              Nama Semester
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="bg-gray-800/50 border-gray-600/30 text-white focus:border-purple-400/50 focus:ring-purple-400/20 rounded-xl h-12"
              placeholder="Contoh: Semester 1"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start_date" className="text-gray-300 mb-2 block">
                Tanggal Mulai
              </Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                className="bg-gray-800/50 border-gray-600/30 text-white focus:border-purple-400/50 focus:ring-purple-400/20 rounded-xl h-12"
                required
              />
            </div>
            <div>
              <Label htmlFor="end_date" className="text-gray-300 mb-2 block">
                Tanggal Selesai
              </Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                className="bg-gray-800/50 border-gray-600/30 text-white focus:border-purple-400/50 focus:ring-purple-400/20 rounded-xl h-12"
                required
              />
            </div>
          </div>

          <DialogFooter className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="bg-gray-800/50 border-gray-600/30 text-white hover:bg-gray-700/50 rounded-xl"
            >
              <X className="h-4 w-4 mr-2" />
              Batal
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !formData.name.trim() || !formData.academic_year_id || !formData.start_date || !formData.end_date}
              className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl"
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Menyimpan...' : isEditing ? 'Perbarui' : 'Simpan'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddSemesterModal;
