
import React, { useState } from 'react';
import Navigation from '@/components/Navigation';
import LandingPage from '@/components/LandingPage';
import AuthModal from '@/components/AuthModal';
import DashboardLayout from '@/components/DashboardLayout';
import { Toaster } from '@/components/ui/toaster';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

const Index = () => {
  const [activeTab, setActiveTab] = useState('beranda');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const { user, loading, signOut } = useAuth();

  const handleLoginClick = () => {
    setIsAuthModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsAuthModalOpen(false);
  };

  const handleLogin = () => {
    setActiveTab('dashboard');
  };

  const handleLogout = async () => {
    await signOut();
    setActiveTab('beranda');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-lime-400 mx-auto mb-4" />
          <div className="text-foreground text-lg">Memuat IndoJadwal...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {user ? (
        <DashboardLayout />
      ) : (
        <>
          <Navigation
            activeTab={activeTab}
            onTabChange={setActiveTab}
            isLoggedIn={!!user}
            onLoginClick={handleLoginClick}
            onLogoutClick={handleLogout}
          />
          <LandingPage onLoginClick={handleLoginClick} />
        </>
      )}

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={handleCloseModal}
        onLogin={handleLogin}
      />
      
      <Toaster />
    </div>
  );
};

export default Index;
