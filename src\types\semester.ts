export interface Semester {
  id: string;
  name: string;
  semester_number: 1 | 2;
  start_date: string;
  end_date: string;
  is_active: boolean;
  academic_year_id: string;
  school_id: string;
  created_at: string;
  updated_at: string;
}

export interface SemesterWithAcademicYear extends Semester {
  academic_year: {
    id: string;
    year_name: string;
    start_date: string;
    end_date: string;
    is_active: boolean;
  };
}

export interface CreateSemesterData {
  name: string;
  semester_number: 1 | 2;
  start_date: string;
  end_date: string;
  academic_year_id: string;
}

export interface UpdateSemesterData {
  name?: string;
  semester_number?: 1 | 2;
  start_date?: string;
  end_date?: string;
  is_active?: boolean;
}

// Utility types untuk semester Indonesia
export type SemesterNumber = 1 | 2;
export type SemesterName = 'Semester 1' | 'Semester 2';

// Constants untuk semester Indonesia
export const SEMESTER_CONSTANTS = {
  SEMESTER_1: {
    number: 1 as SemesterN<PERSON><PERSON>,
    name: 'Semester 1' as SemesterName,
    description: 'Juli - Desember'
  },
  SEMESTER_2: {
    number: 2 as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    name: 'Semester 2' as Semester<PERSON><PERSON>,
    description: '<PERSON><PERSON>ri - Juni'
  }
} as const;

// Helper functions
export const getSemesterName = (semesterNumber: SemesterNumber): SemesterName => {
  return semesterNumber === 1 ? 'Semester 1' : 'Semester 2';
};

export const getSemesterDescription = (semesterNumber: SemesterNumber): string => {
  return semesterNumber === 1 ? 'Juli - Desember' : 'Januari - Juni';
};

export const getCurrentSemester = (date: Date = new Date()): SemesterNumber => {
  const month = date.getMonth() + 1; // getMonth() returns 0-11
  // Semester 1: Juli (7) - Desember (12)
  // Semester 2: Januari (1) - Juni (6)
  return month >= 7 ? 1 : 2;
};

export const getSemesterDateRange = (academicYearStart: string, semesterNumber: SemesterNumber) => {
  const startYear = new Date(academicYearStart).getFullYear();
  
  if (semesterNumber === 1) {
    return {
      start_date: `${startYear}-07-01`,
      end_date: `${startYear}-12-31`
    };
  } else {
    return {
      start_date: `${startYear + 1}-01-01`,
      end_date: `${startYear + 1}-06-30`
    };
  }
};
