import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Se<PERSON>ter, SemesterWithAcademicYear, CreateSemesterData, UpdateSemesterData } from '@/types/semester';

// Hook untuk mengambil semua semester
export const useSemesters = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['semesters'],
    queryFn: async () => {
      if (!profile?.school_id) {
        return [];
      }

      const { data, error } = await supabase
        .from('semesters')
        .select(`
          *,
          academic_years (
            id,
            year_name,
            start_date,
            end_date,
            is_active
          )
        `)
        .eq('school_id', profile.school_id)
        .order('academic_year_id', { ascending: false })
        .order('semester_number', { ascending: true });
      
      if (error) throw error;
      return data as SemesterWithAcademicYear[];
    },
    enabled: !!profile?.school_id,
  });
};

// Hook untuk mengambil semester berdasarkan academic year
export const useSemestersByAcademicYear = (academicYearId?: string) => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['semesters', 'by-academic-year', academicYearId],
    queryFn: async () => {
      if (!profile?.school_id || !academicYearId) {
        return [];
      }

      const { data, error } = await supabase
        .from('semesters')
        .select('*')
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', academicYearId)
        .order('semester_number', { ascending: true });
      
      if (error) throw error;
      return data as Semester[];
    },
    enabled: !!profile?.school_id && !!academicYearId,
  });
};

// Hook untuk mengambil semester aktif
export const useActiveSemester = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['active-semester'],
    queryFn: async () => {
      if (!profile?.school_id) {
        return null;
      }

      const { data, error } = await supabase
        .from('semesters')
        .select(`
          *,
          academic_years (
            id,
            year_name,
            start_date,
            end_date,
            is_active
          )
        `)
        .eq('school_id', profile.school_id)
        .eq('is_active', true)
        .single();
      
      if (error && error.code !== 'PGRST116') throw error;
      return data as SemesterWithAcademicYear | null;
    },
    enabled: !!profile?.school_id,
  });
};

// Hook untuk membuat semester
export const useCreateSemester = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async (semesterData: CreateSemesterData) => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      const { data, error } = await supabase
        .from('semesters')
        .insert({
          ...semesterData,
          school_id: profile.school_id,
          is_active: false
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['semesters'] });
      toast({
        title: "Berhasil",
        description: "Semester baru berhasil ditambahkan",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal menambahkan semester",
        variant: "destructive",
      });
      console.error('Error creating semester:', error);
    },
  });
};

// Hook untuk mengupdate semester
export const useUpdateSemester = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateSemesterData & { id: string }) => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      const { data, error } = await supabase
        .from('semesters')
        .update(updateData)
        .eq('id', id)
        .eq('school_id', profile.school_id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['semesters'] });
      queryClient.invalidateQueries({ queryKey: ['active-semester'] });
      toast({
        title: "Berhasil",
        description: "Semester berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal memperbarui semester",
        variant: "destructive",
      });
      console.error('Error updating semester:', error);
    },
  });
};

// Hook untuk mengaktifkan semester
export const useUpdateActiveSemester = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async (semesterId: string) => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      // First, set all semesters as inactive for this school
      await supabase
        .from('semesters')
        .update({ is_active: false })
        .eq('school_id', profile.school_id);

      // Then set the selected one as active
      const { data, error } = await supabase
        .from('semesters')
        .update({ is_active: true })
        .eq('id', semesterId)
        .eq('school_id', profile.school_id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['semesters'] });
      queryClient.invalidateQueries({ queryKey: ['active-semester'] });
      toast({
        title: "Berhasil",
        description: "Semester aktif berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal mengaktifkan semester",
        variant: "destructive",
      });
      console.error('Error updating active semester:', error);
    },
  });
};

// Hook untuk menghapus semester
export const useDeleteSemester = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async (semesterId: string) => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      const { error } = await supabase
        .from('semesters')
        .delete()
        .eq('id', semesterId)
        .eq('school_id', profile.school_id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['semesters'] });
      queryClient.invalidateQueries({ queryKey: ['active-semester'] });
      toast({
        title: "Berhasil",
        description: "Semester berhasil dihapus",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal menghapus semester",
        variant: "destructive",
      });
      console.error('Error deleting semester:', error);
    },
  });
};
