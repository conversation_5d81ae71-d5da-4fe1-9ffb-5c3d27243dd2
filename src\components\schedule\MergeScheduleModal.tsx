import React, { useState, useMemo } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Merge, 
  Calendar,
  Clock,
  AlertTriangle,
  X,
  Users,
  BookOpen,
  Home,
  Activity
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useMergeSchedule } from '@/hooks/useMergeSchedule';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { MergeScheduleDebug } from './MergeScheduleDebug';
import { ScheduleViewTest } from './ScheduleViewTest';
import { DeleteScheduleDebug } from './DeleteScheduleDebug';

interface MergeScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedClassId?: string;
  currentWeek?: number;
}

export const MergeScheduleModal: React.FC<MergeScheduleModalProps> = ({
  isOpen,
  onClose,
  selectedClassId,
  currentWeek = 1
}) => {
  const [activeTab, setActiveTab] = useState<'category' | 'all'>('category');
  const [confirmMerge, setConfirmMerge] = useState(false);
  const [overwriteConflicts, setOverwriteConflicts] = useState(false);

  // Category merge states
  const [sourceClassId, setSourceClassId] = useState<string>('');
  const [targetClassId, setTargetClassId] = useState<string>(selectedClassId || '');
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set());

  // All merge states
  const [allSourceClassId, setAllSourceClassId] = useState<string>('');
  const [allTargetClassId, setAllTargetClassId] = useState<string>(selectedClassId || '');

  const { data: categories = [] } = useSessionCategories();
  const { mergeByCategory, mergeAll, getPreviewSchedules, checkMergeConflicts, isLoading, classes = [] } = useMergeSchedule(
    activeTab === 'category' ? sourceClassId : allSourceClassId,
    activeTab === 'category' ? targetClassId : allTargetClassId
  );

  // Handle category selection
  const handleCategoryToggle = (categoryId: string) => {
    const newSelected = new Set(selectedCategories);
    if (newSelected.has(categoryId)) {
      newSelected.delete(categoryId);
    } else {
      newSelected.add(categoryId);
    }
    setSelectedCategories(newSelected);
  };

  // Convert Set to Array for processing
  const selectedCategoriesArray = Array.from(selectedCategories);

  // Get preview schedules
  const previewSchedules = useMemo(() => {
    if (activeTab === 'category') {
      if (!sourceClassId || selectedCategoriesArray.length === 0) return [];
      return getPreviewSchedules(sourceClassId, selectedCategoriesArray, 'category') || [];
    } else {
      if (!allSourceClassId) return [];
      return getPreviewSchedules(allSourceClassId, [], 'all') || [];
    }
  }, [activeTab, sourceClassId, allSourceClassId, selectedCategoriesArray]);

  // Check conflicts
  const conflicts = useMemo(() => {
    if (!previewSchedules || previewSchedules.length === 0) return [];

    if (activeTab === 'category') {
      if (!targetClassId) return [];
      return checkMergeConflicts(previewSchedules, targetClassId) || [];
    } else {
      if (!allTargetClassId) return [];
      return checkMergeConflicts(previewSchedules, allTargetClassId) || [];
    }
  }, [activeTab, previewSchedules, targetClassId, allTargetClassId]);

  const handleMerge = async () => {
    if (!confirmMerge) {
      console.error('Please confirm merge');
      return;
    }

    if (!previewSchedules || previewSchedules.length === 0) {
      console.error('No schedules to merge');
      return;
    }

    try {
      if (activeTab === 'category') {
        if (!sourceClassId || !targetClassId || selectedCategoriesArray.length === 0) {
          console.error('Missing required fields for category merge');
          return;
        }

        await mergeByCategory.mutateAsync({
          sourceClassId,
          targetClassId,
          categories: selectedCategoriesArray,
          overwriteConflicts
        });
      } else {
        if (!allSourceClassId || !allTargetClassId) {
          console.error('Missing required fields for all merge');
          return;
        }

        await mergeAll.mutateAsync({
          sourceClassId: allSourceClassId,
          targetClassId: allTargetClassId,
          overwriteConflicts
        });
      }
      onClose();
    } catch (error) {
      console.error('Merge error:', error);
    }
  };

  const getCategoryIcon = (categoryName: string) => {
    const name = categoryName.toLowerCase();
    if (name.includes('kbm') || name.includes('pelajaran')) return <BookOpen className="h-4 w-4" />;
    if (name.includes('asrama') || name.includes('keasramaan')) return <Home className="h-4 w-4" />;
    if (name.includes('ekskul') || name.includes('ekstrakurikuler')) return <Activity className="h-4 w-4" />;
    return <Calendar className="h-4 w-4" />;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] bg-background border-border backdrop-blur-sm">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-bold text-foreground flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-500/20">
              <Merge className="h-6 w-6 text-purple-500" />
            </div>
            Merge Jadwal Kelas
          </DialogTitle>
        </DialogHeader>

        <div className="flex gap-6">
          {/* Left Sidebar - Conflicts Warning */}
          <div className="w-80 space-y-4">
            {/* Conflicts Warning */}
            {conflicts && conflicts.length > 0 && (
              <Card className="bg-orange-500/10 border-orange-500/30">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-orange-400 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-orange-300 mb-2">
                        {conflicts.length} Konflik Jadwal
                      </h4>
                      <div className="text-sm text-orange-200 space-y-1 max-h-40 overflow-y-auto">
                        {conflicts && conflicts.slice(0, 5).map((conflict, index) => (
                          <div key={index} className="text-xs">
                            • {conflict.conflictTime} - {conflict.schedule.subjects?.name}
                          </div>
                        ))}
                        {conflicts && conflicts.length > 5 && (
                          <div className="text-orange-300 text-xs">
                            ... dan {conflicts.length - 5} konflik lainnya
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Options */}
            <Card className="bg-card border-border">
              <CardContent className="p-4 space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="overwrite"
                    checked={overwriteConflicts}
                    onCheckedChange={setOverwriteConflicts}
                  />
                  <label htmlFor="overwrite" className="text-sm text-foreground">
                    Timpa jadwal yang konflik
                  </label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="confirm"
                    checked={confirmMerge}
                    onCheckedChange={setConfirmMerge}
                  />
                  <label htmlFor="confirm" className="text-sm text-foreground">
                    Saya yakin ingin merge jadwal
                  </label>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="space-y-3">
              <Button
                onClick={handleMerge}
                disabled={isLoading || !confirmMerge || (conflicts && conflicts.length > 0 && !overwriteConflicts)}
                className="w-full bg-purple-500 hover:bg-purple-600 text-white"
              >
                {isLoading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Merging...
                  </>
                ) : (
                  <>
                    <Merge className="h-4 w-4 mr-2" />
                    Merge Jadwal
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={onClose}
                className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                Batal
              </Button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 space-y-6">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'category' | 'all')}>
              <TabsList className="grid w-full grid-cols-2 bg-gray-700/50">
                <TabsTrigger
                  value="category"
                  className="flex items-center gap-2 data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-300"
                >
                  <BookOpen className="h-4 w-4" />
                  Merge Per Kategori
                </TabsTrigger>
                <TabsTrigger
                  value="all"
                  className="flex items-center gap-2 data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-300"
                >
                  <Users className="h-4 w-4" />
                  Merge Semua
                </TabsTrigger>
              </TabsList>

              <TabsContent value="category" className="space-y-6 mt-6">
                <div className="grid grid-cols-2 gap-6">
                  {/* Source Class */}
                  <Card className="bg-gray-700/30 border-gray-600/50">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-white text-sm flex items-center gap-2">
                        <Users className="h-4 w-4 text-blue-400" />
                        Kelas Sumber
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div>
                        <label className="text-sm font-medium text-gray-300 mb-2 block">Pilih Kelas Sumber</label>
                        <Select value={sourceClassId} onValueChange={setSourceClassId}>
                          <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                            <SelectValue placeholder="Pilih kelas..." />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-700 border-gray-600">
                            {classes.map(cls => (
                              <SelectItem key={cls.id} value={cls.id}>
                                {cls.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Target Class */}
                  <Card className="bg-gray-700/30 border-gray-600/50">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-white text-sm flex items-center gap-2">
                        <Users className="h-4 w-4 text-green-400" />
                        Kelas Tujuan
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div>
                        <label className="text-sm font-medium text-gray-300 mb-2 block">Pilih Kelas Tujuan</label>
                        <Select value={targetClassId} onValueChange={setTargetClassId}>
                          <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                            <SelectValue placeholder="Pilih kelas..." />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-700 border-gray-600">
                            {classes && classes.length > 0 ? classes.map(cls => (
                              <SelectItem key={cls.id} value={cls.id}>
                                {cls.name}
                              </SelectItem>
                            )) : (
                              <SelectItem value="" disabled>
                                Tidak ada kelas tersedia
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Categories Selection */}
                <Card className="bg-gray-700/30 border-gray-600/50">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white text-sm flex items-center gap-2">
                      <BookOpen className="h-4 w-4 text-purple-400" />
                      Pilih Kategori yang Akan Di-merge
                    </CardTitle>
                    <div className="text-xs text-gray-400 mt-1">
                      Pilih kategori jadwal yang ingin dipindahkan dari kelas sumber ke kelas tujuan.
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      {categories && categories.length > 0 ? categories.map(category => (
                        <div
                          key={category.id}
                          onClick={() => handleCategoryToggle(category.id)}
                          className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                            selectedCategories.has(category.id)
                              ? 'bg-purple-500/20 border-purple-500/50 text-purple-300'
                              : 'bg-gray-700/50 border-gray-600/50 text-gray-300 hover:bg-gray-600/50'
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            {getCategoryIcon(category.name)}
                            <span className="text-sm font-medium">{category.name}</span>
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            {category.description || 'Kategori jadwal'}
                          </div>
                        </div>
                      )) : (
                        <div className="col-span-2 text-center py-4 text-gray-400">
                          <p>Tidak ada kategori tersedia</p>
                        </div>
                      )}
                    </div>

                    {/* Selected Categories Preview */}
                    {selectedCategoriesArray.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <label className="text-sm font-medium text-gray-300">
                          Kategori Terpilih ({selectedCategoriesArray.length} kategori):
                        </label>
                        <div className="flex flex-wrap gap-2">
                          {selectedCategoriesArray.map(categoryId => {
                            const category = categories.find(c => c.id === categoryId);
                            return (
                              <Badge
                                key={categoryId}
                                variant="secondary"
                                className="bg-purple-500/20 text-purple-300 border-purple-500/30 cursor-pointer hover:bg-purple-500/30"
                                onClick={() => handleCategoryToggle(categoryId)}
                              >
                                {category?.name} ✕
                              </Badge>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="all" className="space-y-6 mt-6">
                <div className="grid grid-cols-2 gap-6">
                  {/* Source Class */}
                  <Card className="bg-gray-700/30 border-gray-600/50">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-white text-sm flex items-center gap-2">
                        <Users className="h-4 w-4 text-blue-400" />
                        Kelas Sumber
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div>
                        <label className="text-sm font-medium text-gray-300 mb-2 block">Pilih Kelas Sumber</label>
                        <Select value={allSourceClassId} onValueChange={setAllSourceClassId}>
                          <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                            <SelectValue placeholder="Pilih kelas..." />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-700 border-gray-600">
                            {classes && classes.length > 0 ? classes.map(cls => (
                              <SelectItem key={cls.id} value={cls.id}>
                                {cls.name}
                              </SelectItem>
                            )) : (
                              <SelectItem value="" disabled>
                                Tidak ada kelas tersedia
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Target Class */}
                  <Card className="bg-gray-700/30 border-gray-600/50">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-white text-sm flex items-center gap-2">
                        <Users className="h-4 w-4 text-green-400" />
                        Kelas Tujuan
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div>
                        <label className="text-sm font-medium text-gray-300 mb-2 block">Pilih Kelas Tujuan</label>
                        <Select value={allTargetClassId} onValueChange={setAllTargetClassId}>
                          <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                            <SelectValue placeholder="Pilih kelas..." />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-700 border-gray-600">
                            {classes && classes.length > 0 ? classes.map(cls => (
                              <SelectItem key={cls.id} value={cls.id}>
                                {cls.name}
                              </SelectItem>
                            )) : (
                              <SelectItem value="" disabled>
                                Tidak ada kelas tersedia
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* All Merge Info */}
                <Card className="bg-gray-700/30 border-gray-600/50">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-white text-sm flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-400" />
                      Informasi Merge Semua
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm text-gray-300 space-y-2">
                      <p>Semua jadwal dari kelas sumber akan dipindahkan ke kelas tujuan.</p>
                      <p className="text-yellow-300 font-medium">
                        Ini akan memindahkan seluruh jadwal tanpa memilih kategori tertentu.
                      </p>
                      <p className="text-orange-300">
                        Pastikan kelas tujuan sudah benar sebelum melanjutkan!
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Debug Section - Only show in development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="space-y-4">
                <MergeScheduleDebug />
                <ScheduleViewTest />
                <DeleteScheduleDebug />
              </div>
            )}

            {/* Preview Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">Preview Jadwal yang Akan Di-merge</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                    {previewSchedules?.length || 0} jadwal
                  </Badge>
                  {activeTab === 'category' && selectedCategoriesArray.length > 0 && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      dari {selectedCategoriesArray.length} kategori
                    </Badge>
                  )}
                </div>
              </div>

              {previewSchedules && previewSchedules.length > 0 ? (
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {previewSchedules.map((schedule, index) => (
                    <div
                      key={schedule.id || index}
                      className="flex items-center justify-between p-3 bg-purple-500/10 rounded-lg border-l-4 border-purple-500"
                    >
                      <div className="flex-1">
                        <div className="font-medium text-white text-sm">
                          {schedule.subjects?.name || 'Mata Pelajaran'}
                        </div>
                        <div className="text-xs text-gray-400">
                          {schedule.day_of_week} • {schedule.start_time}-{schedule.end_time}
                          {schedule.room && ` • ${schedule.room}`}
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs border-purple-500/30 text-purple-300">
                        {schedule.category?.name || 'Kategori'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <Calendar className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Tidak ada jadwal untuk di-merge</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
