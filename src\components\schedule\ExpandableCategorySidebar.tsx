
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Plus, ChevronRight, GripVertical } from 'lucide-react';
import { getSubjectColorWithCategory, getCategoryColor } from '@/utils/subjectColors';

import { useScheduleSubjectsByCategory } from '@/hooks/useScheduleSubjectsByCategory';
import { ScheduleSubjectModal } from './ScheduleSubjectModal';
import { ScheduleSubjectCRUD } from './ScheduleSubjectCRUD';

import { useJPCalculation } from '@/hooks/useJPCalculation';

interface ExpandableCategorySidebarProps {
  selectedClassId: string | null;
  selectedWeek?: number;
  onSubjectDrag: (subject: any) => void;
}

export const ExpandableCategorySidebar: React.FC<ExpandableCategorySidebarProps> = ({
  selectedClassId,
  selectedWeek,
  onSubjectDrag
}) => {
  // ✅ DEBUG: Log props yang diterima
  console.log('🚀 ExpandableCategorySidebar rendered with props:', {
    selectedClassId,
    selectedWeek,
    type: typeof selectedClassId,
    isNull: selectedClassId === null,
    isUndefined: selectedClassId === undefined,
    isEmpty: selectedClassId === ''
  });

  // ✅ NEW: Menggunakan sistem schedule categories terintegrasi
  const { data: scheduleSubjectsByCategory = [] } = useScheduleSubjectsByCategory(selectedClassId);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedCategoryForAdd, setSelectedCategoryForAdd] = useState<string>('');

  // ✅ JP Calculation untuk setiap subject (LEGACY - akan dihapus)
  const { jpData } = useJPCalculation({
    selectedClassId: selectedClassId || undefined,
    selectedWeek
  });

  // ✅ DEBUG: Log data yang diterima
  console.log('🔍 ExpandableCategorySidebar DEBUG:', {
    selectedClassId,
    scheduleSubjectsByCategory: scheduleSubjectsByCategory.length,
    jpData: jpData.length,
    categoriesWithJPData: scheduleSubjectsByCategory.map(cat => ({
      name: cat.name,
      subjects: cat.subjects.map(s => ({
        name: s.name,
        target_jp: s.target_jp,
        realisasi_jp: s.realisasi_jp,
        progress_percentage: s.progress_percentage
      }))
    }))
  });

  // Helper function to get JP data for a specific subject
  const getJPDataForSubject = (subjectId: string) => {
    const result = jpData.find(jp => jp.id === subjectId) || {
      targetJP: 0,
      realizationJP: 0,
      percentage: 0
    };

    // Debug log
    console.log(`🔍 JP Data for subject ${subjectId}:`, result);
    return result;
  };

  // Debug log for all JP data
  console.log('📊 All JP Data in sidebar:', jpData);
  console.log('🎯 Selected Class ID:', selectedClassId);

  // ✅ NEW: Menggunakan data dari schedule categories
  const categoriesToRender = scheduleSubjectsByCategory;

  console.log('🔍 ExpandableCategorySidebar - selectedClassId:', selectedClassId);
  console.log('🔍 ExpandableCategorySidebar - scheduleSubjectsByCategory:', scheduleSubjectsByCategory);
  console.log('🔍 ExpandableCategorySidebar - categoriesToRender:', categoriesToRender);

  console.log('🎯 Sidebar Categories to Render:', {
    selectedClassId,
    totalCategories: categoriesToRender.length,
    categories: categoriesToRender.map(cat => ({
      name: cat.name,
      subjectCount: cat.subjects.length,
      subjects: cat.subjects.map(s => s.name)
    }))
  });

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const handleDragStart = (e: React.DragEvent, subject: any) => {
    if (!selectedClassId) {
      e.preventDefault();
      return;
    }

    // ✅ ENHANCED: Ensure subject data has proper structure for drag and drop
    const enhancedSubjectData = {
      ...subject,
      type: subject.type || 'schedule_subject', // Default to schedule_subject
      source: 'sidebar',
      // ✅ FIXED: Ensure we have the essential fields with correct values
      id: subject.id,
      name: subject.name,
      color: subject.color || '#6B7280', // Fallback color
      session_category_id: subject.session_category_id,
      // ✅ ADDED: Include original data for reference
      original_data: subject
    };

    console.log('🎯 Drag Start - Enhanced Subject Data:', enhancedSubjectData);

    // Set data for external drag
    e.dataTransfer.setData('application/json', JSON.stringify(enhancedSubjectData));
    e.dataTransfer.effectAllowed = 'copy';

    onSubjectDrag(enhancedSubjectData);
  };

  const handleAddSubject = (categoryId: string) => {
    setSelectedCategoryForAdd(categoryId);
    setIsAddModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsAddModalOpen(false);
    setSelectedCategoryForAdd('');
  };

  return (
    <div
      id="external-events-container"
      className="h-full bg-muted/20 p-4 space-y-4 overflow-y-auto expandable-category-sidebar"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-base font-semibold text-foreground">
          Mata Pelajaran
        </h3>
        <Button
          size="sm"
          onClick={() => handleAddSubject('')} // ✅ NEW: Akan membuka modal untuk kategori custom
          className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold shadow-lg transition-all duration-300"
        >
          <Plus className="h-4 w-4 mr-1" />
          Mapel
        </Button>
      </div>

      {!selectedClassId && (
        <div className="bg-lime-500/10 border border-lime-500/30 rounded-lg p-3 mb-4">
          <p className="text-lime-400 text-sm text-center">
            Pilih kelas terlebih dahulu untuk menambahkan jadwal
          </p>
        </div>
      )}







      <div className="space-y-3">
        {categoriesToRender.map(category => {
          const isExpanded = expandedCategories[category.id];
          const categorySubjects = category.subjects;

          return (
            <Card
              key={category.id}
              className="border-border shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] overflow-hidden bg-card"
              style={{
                borderRadius: '20px', // Rounded category
                // border: `0.5px solid ${category.color}`, // border category
                boxShadow: `0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05), 0 0 0 1px ${category.color}40`
              }}
            >
              <Collapsible open={isExpanded} onOpenChange={() => toggleCategory(category.id)}>
                <CollapsibleTrigger asChild>
                  <CardHeader
                    className="p-4 cursor-pointer transition-all duration-300 hover:bg-accent/10"
                  >
                    <CardTitle className="flex items-center justify-between w-full">
                      {/* Text Content Category */}
                      <div className="flex-1">
                        <div className="text-foreground font-medium text-sm">
                          {category.name}
                        </div>
                      </div>

                      {/* Badge Counter */}
                      <div
                        className="bg-primary text-primary-foreground font-normal px-3 py-1 rounded-full text-sm min-w-[2.5rem] text-center mr-3"
                        style={{
                          // backgroundColor: category.color,
                          // color: 'white'
                        }}
                      >
                        {categorySubjects.length}
                      </div>

                      {/* Arrow */}
                      <ChevronRight
                        className={`h-5 w-5 text-white transition-transform duration-300 ${
                          isExpanded ? 'rotate-90' : ''
                        }`}
                      />
                    </CardTitle>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="pt-0 space-y-2">
                    {categorySubjects.map(subject => {
                      // ✅ NEW: Use consistent color system with category fallback
                      const colors = getSubjectColorWithCategory(subject, category);
                      const categoryColors = getCategoryColor(category);

                      // ✅ JP Data untuk subject ini (LEGACY)
                      const jpInfo = jpData.find(jp => jp.id === subject.id);

                      // ✅ DEBUG: Log subject data untuk melihat apakah JP data sudah ada
                      console.log(`🔍 SUBJECT DEBUG "${subject.name}":`, {
                        id: subject.id,
                        name: subject.name,
                        target_jp: subject.target_jp,
                        realisasi_jp: subject.realisasi_jp,
                        progress_percentage: subject.progress_percentage,
                        hasJPData: subject.target_jp !== undefined,
                        legacyJPInfo: jpInfo
                      });

                      // Debug logging untuk warna
                      console.log(`🎨 Subject "${subject.name}" - Category: ${category.name}`, {
                        subjectColor: subject.color,
                        categoryColor: category.color,
                        finalColors: colors,
                        categoryColors: categoryColors,
                        jpInfo
                      });

                      // ✅ ENHANCED: Prepare enhanced subject data for data attributes
                      const enhancedSubjectData = {
                        ...subject,
                        type: subject.type || 'schedule_subject',
                        source: 'sidebar',
                        session_category_id: subject.session_category_id
                      };

                      return (
                        <div
                          key={subject.id}
                          className={`
                            fc-event fc-event-draggable rounded-lg border-2 p-3 transition-all duration-300 group backdrop-blur-sm shadow-lg hover:shadow-2xl select-none
                            ${selectedClassId
                              ? 'cursor-move hover:scale-105 hover:shadow-2xl hover:z-50 hover:-translate-y-1'
                              : 'cursor-not-allowed opacity-50'
                            }
                          `}
                          style={{
                            background: selectedClassId
                              ? colors.background
                              : 'linear-gradient(135deg, #4B556340, #37415040)',
                            borderColor: selectedClassId
                              ? colors.border
                              : '#6B728080',
                            boxShadow: selectedClassId
                              ? `0 4px 12px rgba(0,0,0,0.15), 0 0 0 1px ${colors.border}30`
                              : '0 2px 8px rgba(0,0,0,0.1)'
                          }}
                          data-subject={JSON.stringify(enhancedSubjectData)}
                          data-subject-id={subject.id}
                          data-subject-name={subject.name}
                          data-subject-color={colors.background}
                          data-category-name={category.name}
                          data-duration="01:00"
                          draggable={!!selectedClassId}
                          onDragStart={(e) => handleDragStart(e, subject)}
                        >
                          <div className="flex items-center gap-3">
                            <GripVertical
                              className={`h-5 w-5 transition-all duration-300 ${
                                selectedClassId
                                  ? 'group-hover:scale-125 group-hover:rotate-12'
                                  : 'text-gray-600'
                              }`}
                              style={{
                                color: selectedClassId ? colors.text : '#6B7280',
                                filter: 'none'
                              }}
                            />
                            <div className="flex-1 min-w-0">
                              <div
                                className="text-sm font-bold truncate drop-shadow-sm"
                                style={{
                                  color: selectedClassId ? colors.text : '#9CA3AF',
                                  textShadow: 'none'
                                }}
                              >
                                {subject.name}
                              </div>

                              {/* ✅ JP Information - Menggunakan data akurat dari hook yang sama dengan tombol JP */}
                              {selectedClassId && subject.target_jp !== undefined && (
                                <div className="text-xs font-semibold mt-1 space-y-1">
                                  {/* Target dan Realisasi JP */}
                                  <div style={{ color: selectedClassId ? colors.text : '#9CA3AF' }}>
                                    {subject.realisasi_jp || 0}/{subject.target_jp || 0} JP/Tahun
                                  </div>

                                  {/* Progress Bar */}
                                  <div className="w-full">
                                    <Progress
                                      value={subject.progress_percentage || 0}
                                      className="h-2 bg-gray-700/50"
                                      indicatorClassName={
                                        (subject.progress_percentage || 0) >= 100 ? 'bg-green-500' :
                                        (subject.progress_percentage || 0) >= 75 ? 'bg-blue-500' :
                                        (subject.progress_percentage || 0) >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                                      }
                                    />
                                  </div>

                                  {/* Persentase Badge */}
                                  <div className="flex items-center justify-between">
                                    <span
                                      className="px-2 py-1 rounded text-xs font-bold"
                                      style={{
                                        backgroundColor: (subject.progress_percentage || 0) >= 100 ? '#10B981' :
                                                       (subject.progress_percentage || 0) >= 75 ? '#3B82F6' :
                                                       (subject.progress_percentage || 0) >= 50 ? '#F59E0B' : '#EF4444',
                                        color: 'white'
                                      }}
                                    >
                                      {Math.round(subject.progress_percentage || 0)}%
                                    </span>

                                    {/* Status Text */}
                                    <span className="text-xs" style={{ color: selectedClassId ? `${colors.text}80` : '#6B7280' }}>
                                      {(subject.progress_percentage || 0) >= 100 ? 'Tercapai' :
                                       (subject.progress_percentage || 0) >= 75 ? 'Baik' :
                                       (subject.progress_percentage || 0) >= 50 ? 'Cukup' : 'Kurang'}
                                    </span>
                                  </div>
                                </div>
                              )}

                              {subject.code && (
                                <div
                                  className="text-xs truncate font-semibold"
                                  style={{
                                    color: selectedClassId ? `${colors.text}90` : '#6B7280'
                                  }}
                                >
                                  {subject.code}
                                </div>
                              )}
                            </div>

                            {/* ✅ CRUD Buttons untuk schedule subjects - VISIBLE ON HOVER */}
                            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-200 z-20">
                              <ScheduleSubjectCRUD
                                subject={subject}
                                selectedClassId={selectedClassId}
                              />
                            </div>
                          </div>
                        </div>
                      );
                    })}

                    {categorySubjects.length === 0 && (
                      <div className="text-center py-4">
                        <div className="text-xs text-muted-foreground mb-2">Belum ada mata pelajaran</div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleAddSubject(category.id)}
                          className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Tambah
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          );
        })}
      </div>

      <ScheduleSubjectModal
        isOpen={isAddModalOpen}
        onClose={handleCloseModal}
        selectedCategoryId={selectedCategoryForAdd}
        selectedClassId={selectedClassId}
      />
    </div>
  );
};
