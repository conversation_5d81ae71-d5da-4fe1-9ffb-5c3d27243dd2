
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Plus } from 'lucide-react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { Holiday, EventCategory } from '@/types/event';
import { useClasses } from '@/hooks/useClasses';
import { useEventCategories } from '@/hooks/useEventCategories';
import { ManageCategoriesDialog } from './ManageCategoriesDialog';

interface EventDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  event?: Holiday | null;
  initialDate?: Date;
  onSave: (event: Omit<Holiday, 'id' | 'school_id' | 'academic_year_id' | 'created_at' | 'updated_at'>) => void;
  onUpdate?: (event: Partial<Holiday> & { id: string }) => void;
  onDelete?: (id: string) => void;
}

const PREDEFINED_COLORS = [
  '#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e', '#10b981',
  '#06b6d4', '#3b82f6', '#8b5cf6', '#d946ef', '#ec4899', '#78716c',
];

export const EventDialog: React.FC<EventDialogProps> = ({
  open,
  onOpenChange,
  event,
  initialDate,
  onSave,
  onUpdate,
  onDelete,
}) => {
  const [name, setName] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>();
  const [categoryId, setCategoryId] = useState('');
  const [description, setDescription] = useState('');
  const [color, setColor] = useState(PREDEFINED_COLORS[0]);
  const [isNationalHoliday, setIsNationalHoliday] = useState(false);
  const [selectedClassIds, setSelectedClassIds] = useState<string[]>([]);
  const [allClasses, setAllClasses] = useState(true);
  const [showManageCategories, setShowManageCategories] = useState(false);
  
  const { data: classes = [] } = useClasses();
  const { categories } = useEventCategories();

  useEffect(() => {
    if (event) {
      setName(event.name);
      setStartDate(new Date(event.start_date));
      setEndDate(new Date(event.end_date));
      setCategoryId(event.category_id);
      setDescription(event.description || '');
      setColor(event.color || PREDEFINED_COLORS[0]);
      setIsNationalHoliday(event.is_national_holiday || false);
      setSelectedClassIds(event.class_ids || []);
      setAllClasses(!event.class_ids || event.class_ids.length === 0);
    } else {
      setName('');
      setStartDate(initialDate);
      setEndDate(initialDate);
      setCategoryId('');
      setDescription('');
      setColor(PREDEFINED_COLORS[0]);
      setIsNationalHoliday(false);
      setSelectedClassIds([]);
      setAllClasses(true);
    }
  }, [event, initialDate, open]);

  const handleSave = () => {
    if (!name || !startDate || !endDate || !categoryId || !color) {
      return;
    }

    const eventData = {
      name,
      start_date: format(startDate, 'yyyy-MM-dd'),
      end_date: format(endDate, 'yyyy-MM-dd'),
      description,
      color: color,
      category_id: categoryId,
      is_national_holiday: isNationalHoliday,
      class_ids: allClasses ? [] : selectedClassIds,
    };

    if (event && onUpdate) {
      onUpdate({ id: event.id, ...eventData });
    } else {
      onSave(eventData);
    }

    onOpenChange(false);
  };

  const handleDelete = () => {
    if (event && onDelete) {
      onDelete(event.id);
      onOpenChange(false);
    }
  };

  const handleClassSelection = (classId: string, checked: boolean) => {
    if (checked) {
      setSelectedClassIds(prev => [...prev, classId]);
    } else {
      setSelectedClassIds(prev => prev.filter(id => id !== classId));
    }
  };

  const isFormValid = name && startDate && endDate && categoryId && color && endDate >= startDate;

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {event ? 'Edit Event' : 'Tambah Event Baru'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Nama Event *</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Masukkan nama event"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Tanggal Mulai *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, 'dd MMMM yyyy', { locale: id }) : 'Pilih tanggal'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label>Tanggal Selesai *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !endDate && "text-gray-400"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, 'dd MMMM yyyy', { locale: id }) : 'Pilih tanggal'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      disabled={(date) => startDate ? date < startDate : false}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div>
              <Label className="text-white">Kategori *</Label>
              <div className="flex gap-2">
                <Select value={categoryId} onValueChange={setCategoryId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih kategori" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: category.color }}
                          />
                          {category.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  type="button"
                  size="icon"
                  onClick={() => setShowManageCategories(true)}
                  variant="outline"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <Label>Warna *</Label>
              <div className="flex flex-wrap gap-2 pt-2">
                {PREDEFINED_COLORS.map((c) => (
                  <button
                    key={c}
                    type="button"
                    onClick={() => setColor(c)}
                    className={cn(
                      'h-8 w-8 rounded-full border-2 transition-all',
                      color === c
                        ? 'ring-2 ring-offset-2 ring-ring'
                        : 'ring-offset-background'
                    )}
                    style={{ backgroundColor: c }}
                    aria-label={`Select color ${c}`}
                  />
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="description">Deskripsi</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Deskripsi event (opsional)"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="national"
                checked={isNationalHoliday}
                onCheckedChange={(checked) => setIsNationalHoliday(checked === true)}
                
              />
              <Label htmlFor="national">
                Libur Nasional
              </Label>
            </div>

            <div>
              <Label>Kelas yang Terlibat</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="all-classes"
                    checked={allClasses}
                    onCheckedChange={(checked) => {
                      setAllClasses(checked === true);
                      if (checked) {
                        setSelectedClassIds([]);
                      }
                    }}
                    
                  />
                  <Label htmlFor="all-classes" className="font-medium">
                    Semua Kelas
                  </Label>
                </div>

                {!allClasses && (
                  <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto p-2 bg-muted rounded border">
                    {classes.map(cls => (
                      <div key={cls.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`class-${cls.id}`}
                          checked={selectedClassIds.includes(cls.id)}
                          onCheckedChange={(checked) => handleClassSelection(cls.id, checked === true)}
                          
                        />
                        <Label htmlFor={`class-${cls.id}`} className="text-sm">
                          {cls.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="flex gap-2">
            {event && onDelete && (
              <Button
                type="button"
                variant="destructive"
                onClick={handleDelete}
                className="mr-auto"
              >
                Hapus
              </Button>
            )}
            <Button
              type="button"
              onClick={() => onOpenChange(false)}
              variant="outline"
            >
              Batal
            </Button>
            <Button
              type="button"
              onClick={handleSave}
              disabled={!isFormValid}
            >
              {event ? 'Perbarui' : 'Simpan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ManageCategoriesDialog
        open={showManageCategories}
        onOpenChange={setShowManageCategories}
      />
    </>
  );
};
