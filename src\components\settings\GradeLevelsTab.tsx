
import React, { useState } from 'react';
import { Plus, Trash2, Edit2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useEducationLevels } from '@/hooks/useEducationLevels';
import { useGradeLevels, useCreateGradeLevel, useUpdateGradeLevel, useDeleteGradeLevel } from '@/hooks/useGradeLevels';

export const GradeLevelsTab: React.FC = () => {
  const [newGradeLevel, setNewGradeLevel] = useState({ 
    name: '', 
    grade_number: '', 
    education_level_id: '' 
  });
  const [editingGrade, setEditingGrade] = useState<any>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const { data: educationLevels } = useEducationLevels();
  const { data: gradeLevels } = useGradeLevels();
  const createGradeLevel = useCreateGradeLevel();
  const updateGradeLevel = useUpdateGradeLevel();
  const deleteGradeLevel = useDeleteGradeLevel();

  const handleAddGradeLevel = () => {
    if (newGradeLevel.name && newGradeLevel.grade_number && newGradeLevel.education_level_id) {
      createGradeLevel.mutate({
        name: newGradeLevel.name,
        grade_number: parseInt(newGradeLevel.grade_number),
        education_level_id: newGradeLevel.education_level_id,
        is_active: true
      });
      setNewGradeLevel({ name: '', grade_number: '', education_level_id: '' });
    }
  };

  const handleEditGradeLevel = (grade: any) => {
    setEditingGrade({
      ...grade,
      grade_number: grade.grade_number.toString()
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateGradeLevel = () => {
    if (editingGrade) {
      updateGradeLevel.mutate({
        id: editingGrade.id,
        name: editingGrade.name,
        grade_number: parseInt(editingGrade.grade_number),
        education_level_id: editingGrade.education_level_id,
      });
      setIsEditDialogOpen(false);
      setEditingGrade(null);
    }
  };

  const handleDeleteGradeLevel = (id: string) => {
    deleteGradeLevel.mutate(id);
  };

  const getEducationLevelName = (educationLevelId: string) => {
    const level = educationLevels?.find(el => el.id === educationLevelId);
    return level ? level.name : 'Unknown';
  };

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white">Kelola Tingkat Kelas</CardTitle>
        <CardDescription className="text-gray-400">
          Tambah dan kelola tingkat kelas untuk setiap jenjang (X, XI, XII, dll)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Grade Levels */}
        <div className="space-y-3">
          {gradeLevels?.map((grade) => (
            <div key={grade.id} className="flex items-center justify-between p-4 bg-gray-900 rounded-lg border border-gray-600">
              <div>
                <p className="text-white font-medium">{grade.name}</p>
                <p className="text-gray-400 text-sm">
                  Tingkat {grade.grade_number} - {getEducationLevelName(grade.education_level_id)}
                </p>
              </div>
              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className="text-blue-400 hover:bg-blue-400/10"
                  onClick={() => handleEditGradeLevel(grade)}
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button size="sm" variant="ghost" className="text-red-400 hover:bg-red-400/10">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-gray-800 border-gray-700">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-white">Hapus Tingkat Kelas</AlertDialogTitle>
                      <AlertDialogDescription className="text-gray-400">
                        Apakah Anda yakin ingin menghapus tingkat "{grade.name}"? Tindakan ini tidak dapat dibatalkan.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel className="bg-gray-700 text-white border-gray-600 hover:bg-gray-600">
                        Batal
                      </AlertDialogCancel>
                      <AlertDialogAction 
                        className="bg-red-600 hover:bg-red-700"
                        onClick={() => handleDeleteGradeLevel(grade.id)}
                      >
                        Hapus
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          ))}
        </div>
        
        {/* Add New Grade Level */}
        <div className="border-t border-gray-600 pt-6 space-y-4">
          <h3 className="text-lg font-medium text-white">Tambah Tingkat Baru</h3>
          <div className="space-y-4">
            <Select 
              value={newGradeLevel.education_level_id} 
              onValueChange={(value) => setNewGradeLevel(prev => ({ ...prev, education_level_id: value }))}
            >
              <SelectTrigger className="bg-gray-900 border-gray-600 text-white">
                <SelectValue placeholder="Pilih Jenjang" />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-600">
                {educationLevels?.map((level) => (
                  <SelectItem 
                    key={level.id} 
                    value={level.id}
                    className="text-white focus:bg-gray-700 focus:text-white"
                  >
                    {level.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder="Nama Tingkat (contoh: Kelas X)"
                value={newGradeLevel.name}
                onChange={(e) => setNewGradeLevel(prev => ({ ...prev, name: e.target.value }))}
                className="bg-gray-900 border-gray-600 text-white placeholder-gray-400"
              />
              <Input
                placeholder="Nomor Tingkat (contoh: 10)"
                type="number"
                value={newGradeLevel.grade_number}
                onChange={(e) => setNewGradeLevel(prev => ({ ...prev, grade_number: e.target.value }))}
                className="bg-gray-900 border-gray-600 text-white placeholder-gray-400"
              />
            </div>
          </div>
          <Button 
            onClick={handleAddGradeLevel}
            className="w-full bg-lime-400 hover:bg-lime-500 text-gray-900"
            disabled={createGradeLevel.isPending}
          >
            <Plus className="h-4 w-4 mr-2" />
            Tambah Tingkat
          </Button>
        </div>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="bg-gray-800 border-gray-700">
            <DialogHeader>
              <DialogTitle className="text-white">Edit Tingkat Kelas</DialogTitle>
            </DialogHeader>
            {editingGrade && (
              <div className="space-y-4">
                <Select 
                  value={editingGrade.education_level_id} 
                  onValueChange={(value) => setEditingGrade(prev => ({ ...prev, education_level_id: value }))}
                >
                  <SelectTrigger className="bg-gray-900 border-gray-600 text-white">
                    <SelectValue placeholder="Pilih Jenjang" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-gray-600">
                    {educationLevels?.map((level) => (
                      <SelectItem 
                        key={level.id} 
                        value={level.id}
                        className="text-white focus:bg-gray-700 focus:text-white"
                      >
                        {level.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    placeholder="Nama Tingkat"
                    value={editingGrade.name}
                    onChange={(e) => setEditingGrade(prev => ({ ...prev, name: e.target.value }))}
                    className="bg-gray-900 border-gray-600 text-white placeholder-gray-400"
                  />
                  <Input
                    placeholder="Nomor Tingkat"
                    type="number"
                    value={editingGrade.grade_number}
                    onChange={(e) => setEditingGrade(prev => ({ ...prev, grade_number: e.target.value }))}
                    className="bg-gray-900 border-gray-600 text-white placeholder-gray-400"
                  />
                </div>
              </div>
            )}
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsEditDialogOpen(false)}
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                Batal
              </Button>
              <Button 
                onClick={handleUpdateGradeLevel}
                className="bg-lime-400 hover:bg-lime-500 text-gray-900"
                disabled={updateGradeLevel.isPending}
              >
                Simpan
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};
