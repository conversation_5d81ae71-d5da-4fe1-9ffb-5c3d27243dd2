import React from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import AppSidebar from '@/components/AppSidebar';
import { SimpleThemeToggle } from '@/components/ui/theme-toggle';
import { Calendar, CalendarDays, Settings, BookUser, Users, Clock, Building, BarChart, Info } from 'lucide-react';

const getPageTitleAndIcon = (path: string) => {
  if (path.startsWith('/schedules')) return { title: 'Jadwal', Icon: Calendar };
  if (path.startsWith('/events')) return { title: 'Event', Icon: CalendarDays };
  if (path.startsWith('/classes')) return { title: 'Kelas', Icon: BookUser };
  if (path.startsWith('/teachers')) return { title: 'Guru', Icon: Users };
  if (path.startsWith('/subjects')) return { title: '<PERSON>', Icon: Bar<PERSON>hart };
  if (path.startsWith('/time-sessions')) return { title: '<PERSON><PERSON>', Icon: Clock };
  if (path.startsWith('/settings')) return { title: 'Pengaturan Umum', Icon: Settings };
  if (path.startsWith('/school-info')) return { title: 'Info Sekolah', Icon: Info };
  return { title: 'Dashboard', Icon: Building };
};

const getActiveItem = (path: string) => {
    if (path.startsWith('/schedules')) return 'schedules';
    if (path.startsWith('/events')) return 'events';
    if (path.startsWith('/classes')) return 'classes';
    if (path.startsWith('/teachers')) return 'teachers';
    if (path.startsWith('/subjects')) return 'subjects';
    if (path.startsWith('/time-sessions')) return 'time-sessions';
    if (path.startsWith('/settings')) return 'general-settings';
    if (path.startsWith('/school-info')) return 'school-info';
    return 'dashboard';
};

const MainLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleItemChange = (item: string) => {
    const pathMap: { [key: string]: string } = {
      'dashboard': '/',
      'classes': '/classes',
      'teachers': '/teachers',
      'subjects': '/subjects',
      'time-sessions': '/time-sessions',
      'schedules': '/schedules',
      'events': '/events',
      'general-settings': '/settings',
      'school-info': '/school-info',
    };
    navigate(pathMap[item] || '/');
  };

  const { title, Icon } = getPageTitleAndIcon(location.pathname);
  const activeItem = getActiveItem(location.pathname);

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full bg-background">
        <AppSidebar activeItem={activeItem} onItemChange={handleItemChange} />
        <SidebarInset className="flex-1 min-w-0">
          <header className="flex h-16 shrink-0 items-center gap-2 bg-background border-b border-border px-4">
            <SidebarTrigger className="text-foreground hover:text-foreground/80" />
            <div className="flex items-center space-x-2 text-xl font-semibold text-foreground ml-4 flex-1">
              <Icon className="h-6 w-6 text-primary" />
              <span>{title}</span>
            </div>
            <SimpleThemeToggle />
          </header>
          <main className="flex-1 overflow-auto bg-background w-full">
            <div className="w-full h-full">
              <Outlet />
            </div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;
