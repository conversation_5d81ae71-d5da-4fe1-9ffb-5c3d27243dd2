
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import AppSidebar from '@/components/AppSidebar';
import { BookOpen, GraduationCap } from 'lucide-react';
import SubjectsContentPage from '@/components/subjects/SubjectsContentPage';

const SubjectsPage = () => {
  const navigate = useNavigate();

  const handleItemChange = (item: string) => {
    switch (item) {
      case 'dashboard':
        navigate('/dashboard');
        break;
      case 'classes':
        navigate('/classes');
        break;
      case 'teachers':
        navigate('/teachers');
        break;
      case 'subjects':
        navigate('/subjects');
        break;
      case 'time-sessions':
        navigate('/time-sessions');
        break;
      case 'schedules':
        navigate('/schedules');
        break;
      case 'events':
        navigate('/events');
        break;
      case 'general-settings':
        navigate('/general-settings');
        break;
      case 'school-info':
        navigate('/school-info');
        break;
      default:
        navigate('/dashboard');
    }
  };

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <AppSidebar activeItem="subjects" onItemChange={handleItemChange} />
        <SidebarInset className="flex-1">
          <header className="flex h-16 shrink-0 items-center gap-2 bg-background border-b border-border px-4">
            <SidebarTrigger className="text-foreground hover:text-foreground/80" />
            <div className="flex items-center space-x-2 text-xl font-semibold text-foreground ml-4">
              <BookOpen className="h-6 w-6 text-lime-400" />
              <GraduationCap className="h-6 w-6 text-blue-400" />
              <span>Mata Pelajaran & Kelas</span>
            </div>
          </header>

          <SubjectsContentPage />
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default SubjectsPage;
