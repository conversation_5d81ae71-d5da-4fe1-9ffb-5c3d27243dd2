// =====================================================
// TIME UTILITIES FOR 5-MINUTE INTERVAL SNAPPING
// =====================================================

/**
 * Snap time to nearest 5-minute interval
 * @param time - Time string in HH:MM format or Date object
 * @returns Time string in HH:MM format snapped to 5-minute interval
 */
export const snapToFiveMinutes = (time: string | Date): string => {
  let date: Date;
  
  if (typeof time === 'string') {
    // Parse HH:MM format
    const [hours, minutes] = time.split(':').map(Number);
    date = new Date();
    date.setHours(hours, minutes, 0, 0);
  } else {
    date = new Date(time);
  }
  
  const minutes = date.getMinutes();
  const remainder = minutes % 5;
  
  // Round to nearest 5-minute interval
  let snappedMinutes: number;
  if (remainder < 2.5) {
    // Round down
    snappedMinutes = minutes - remainder;
  } else {
    // Round up
    snappedMinutes = minutes + (5 - remainder);
  }
  
  // Handle minute overflow
  if (snappedMinutes >= 60) {
    date.setHours(date.getHours() + 1);
    snappedMinutes = 0;
  }
  
  date.setMinutes(snappedMinutes, 0, 0);
  
  return date.toTimeString().slice(0, 5);
};

/**
 * Snap Date object to nearest 5-minute interval
 * @param date - Date object to snap
 * @returns New Date object snapped to 5-minute interval
 */
export const snapDateToFiveMinutes = (date: Date): Date => {
  const snappedTime = snapToFiveMinutes(date);
  const [hours, minutes] = snappedTime.split(':').map(Number);
  
  const newDate = new Date(date);
  newDate.setHours(hours, minutes, 0, 0);
  
  return newDate;
};

/**
 * Add duration in minutes to a time string and snap to 5-minute interval
 * @param startTime - Start time in HH:MM format
 * @param durationMinutes - Duration to add in minutes
 * @returns End time in HH:MM format snapped to 5-minute interval
 */
export const addDurationAndSnap = (startTime: string, durationMinutes: number): string => {
  const [hours, minutes] = startTime.split(':').map(Number);
  const date = new Date();
  date.setHours(hours, minutes, 0, 0);
  
  // Add duration
  date.setMinutes(date.getMinutes() + durationMinutes);
  
  // Snap to 5-minute interval
  return snapToFiveMinutes(date);
};

/**
 * Calculate duration between two time strings in minutes
 * @param startTime - Start time in HH:MM format
 * @param endTime - End time in HH:MM format
 * @returns Duration in minutes
 */
export const calculateDurationMinutes = (startTime: string, endTime: string): number => {
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);
  
  const startDate = new Date();
  startDate.setHours(startHours, startMinutes, 0, 0);
  
  const endDate = new Date();
  endDate.setHours(endHours, endMinutes, 0, 0);
  
  // Handle next day scenario
  if (endDate < startDate) {
    endDate.setDate(endDate.getDate() + 1);
  }
  
  return Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60));
};

/**
 * Validate if time is in 5-minute interval
 * @param time - Time string in HH:MM format
 * @returns True if time is in 5-minute interval
 */
export const isValidFiveMinuteInterval = (time: string): boolean => {
  const [, minutes] = time.split(':').map(Number);
  return minutes % 5 === 0;
};

/**
 * Format duration in minutes to HH:MM format
 * @param durationMinutes - Duration in minutes
 * @returns Duration in HH:MM format
 */
export const formatDuration = (durationMinutes: number): string => {
  const hours = Math.floor(durationMinutes / 60);
  const minutes = durationMinutes % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
};

/**
 * Snap resize operation to 5-minute intervals
 * @param originalStartTime - Original start time in HH:MM format
 * @param originalEndTime - Original end time in HH:MM format
 * @param newEndTime - New end time from resize operation
 * @returns Object with snapped start and end times
 */
export const snapResizeToFiveMinutes = (
  originalStartTime: string,
  originalEndTime: string,
  newEndTime: string | Date
): { startTime: string; endTime: string; duration: number } => {
  const startTime = originalStartTime; // Start time remains the same
  const endTime = snapToFiveMinutes(newEndTime);
  const duration = calculateDurationMinutes(startTime, endTime);
  
  // Ensure minimum duration of 5 minutes
  if (duration < 5) {
    const adjustedEndTime = addDurationAndSnap(startTime, 5);
    return {
      startTime,
      endTime: adjustedEndTime,
      duration: 5
    };
  }
  
  return {
    startTime,
    endTime,
    duration
  };
};

/**
 * Snap drag operation to 5-minute intervals while preserving duration
 * @param originalDuration - Original duration in minutes
 * @param newStartTime - New start time from drag operation
 * @returns Object with snapped start and end times
 */
export const snapDragToFiveMinutes = (
  originalDuration: number,
  newStartTime: string | Date
): { startTime: string; endTime: string; duration: number } => {
  const startTime = snapToFiveMinutes(newStartTime);
  const endTime = addDurationAndSnap(startTime, originalDuration);
  
  return {
    startTime,
    endTime,
    duration: originalDuration
  };
};

/**
 * Check for schedule conflicts after time snapping
 * @param schedules - Array of existing schedules
 * @param newSchedule - New schedule to check
 * @param excludeId - Schedule ID to exclude from conflict check (for updates)
 * @returns True if there's a conflict
 */
export const hasTimeConflict = (
  schedules: any[],
  newSchedule: {
    class_id: string;
    day_of_week: number;
    start_time: string;
    end_time: string;
    academic_week: number;
  },
  excludeId?: string
): boolean => {
  return schedules.some(schedule => {
    // Skip if same schedule (for updates)
    if (excludeId && schedule.id === excludeId) return false;
    
    // Check if same class, day, and week
    if (
      schedule.class_id === newSchedule.class_id &&
      schedule.day_of_week === newSchedule.day_of_week &&
      schedule.academic_week === newSchedule.academic_week
    ) {
      // Check time overlap
      const existingStart = schedule.start_time;
      const existingEnd = schedule.end_time;
      const newStart = newSchedule.start_time;
      const newEnd = newSchedule.end_time;
      
      // Convert to minutes for easier comparison
      const existingStartMin = timeToMinutes(existingStart);
      const existingEndMin = timeToMinutes(existingEnd);
      const newStartMin = timeToMinutes(newStart);
      const newEndMin = timeToMinutes(newEnd);
      
      // Check overlap: (start1 < end2) && (start2 < end1)
      return (existingStartMin < newEndMin) && (newStartMin < existingEndMin);
    }
    
    return false;
  });
};

/**
 * Convert time string to minutes since midnight
 * @param time - Time string in HH:MM format
 * @returns Minutes since midnight
 */
const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

/**
 * Debug logging for time operations
 * @param operation - Operation name
 * @param data - Data to log
 */
export const logTimeOperation = (operation: string, data: any): void => {
  console.log(`⏰ ${operation}:`, {
    ...data,
    timestamp: new Date().toISOString()
  });
};
