import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

/**
 * Central hook for managing real-time synchronization across the application
 * This hook sets up Supabase real-time subscriptions for all schedule-related tables
 * and ensures immediate UI updates when data changes
 */
export const useRealTimeSync = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    console.log('🚀 Initializing comprehensive real-time sync system...');

    // Create a single channel for all schedule-related tables
    const channel = supabase
      .channel('schedule-realtime-sync')
      
      // Listen to class_schedules table changes (triggers schedules_view update)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'class_schedules'
        },
        async (payload) => {
          console.log('📡 Real-time: class_schedules table changed (triggers schedules_view)', payload);

          // 🚀 ENHANCED: Comprehensive invalidation for immediate UI updates
          console.log('🔄 Starting comprehensive cache invalidation for class_schedules...');
          await Promise.all([
            queryClient.invalidateQueries({ queryKey: ['schedules'] }),
            queryClient.invalidateQueries({ queryKey: ['schedules-simple'] }),
            queryClient.invalidateQueries({ queryKey: ['schedules-paginated'] }),
            queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
            queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
            queryClient.invalidateQueries({ queryKey: ['schedules-week'] }),
            queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['jp-progress'] }),
            queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
          ]);

          // 🚀 ENHANCED: Force immediate refetch for real-time UI updates
          console.log('🔄 Force refetching critical queries for immediate UI update...');
          setTimeout(() => {
            queryClient.refetchQueries({ queryKey: ['schedules-complete'] });
            queryClient.refetchQueries({ queryKey: ['schedules-simple'] });
            queryClient.refetchQueries({ queryKey: ['jp-progress-simple'] });
            console.log('✅ Real-time UI refresh completed');
          }, 100);
        }
      )

      // Listen to schedule_subjects table changes
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'schedule_subjects'
        },
        async (payload) => {
          console.log('📡 Real-time: schedule_subjects table changed', payload);
          console.log('🔍 Payload details:', {
            eventType: payload.eventType,
            new: payload.new,
            old: payload.old,
            table: payload.table
          });

          // ✅ ENHANCED: Comprehensive invalidation for subjects matrix
          console.log('🔄 Starting comprehensive cache invalidation...');
          await Promise.all([
            queryClient.invalidateQueries({ queryKey: ['schedules'] }),
            queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] }),
            queryClient.invalidateQueries({ queryKey: ['subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['matrix-class-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] }),
            queryClient.invalidateQueries({ queryKey: ['class-subjects-by-category'] }),
            queryClient.invalidateQueries({ queryKey: ['kbm-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['unified-subjects'] })
          ]);
          console.log('✅ Cache invalidation completed');

          // Force immediate refetch for critical data
          setTimeout(() => {
            console.log('🔄 Starting force refetch...');
            queryClient.refetchQueries({ queryKey: ['schedule-subjects'] });
            queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });
            queryClient.refetchQueries({ queryKey: ['all-subjects-by-category'] });
            queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
            queryClient.refetchQueries({ queryKey: ['kbm-subjects'] });
            console.log('✅ Force refetch completed');
          }, 100);
        }
      )
      
      // Listen to subjects table changes
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'subjects'
        },
        async (payload) => {
          console.log('📡 Real-time: subjects table changed', payload);

          // ✅ ENHANCED: Comprehensive invalidation for subjects matrix
          await Promise.all([
            queryClient.invalidateQueries({ queryKey: ['subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['schedules'] }),
            queryClient.invalidateQueries({ queryKey: ['class-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] }),
            queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['matrix-class-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] }),
            queryClient.invalidateQueries({ queryKey: ['class-subjects-by-category'] })
          ]);

          // Force immediate refetch for critical data
          setTimeout(() => {
            queryClient.refetchQueries({ queryKey: ['subjects'] });
            queryClient.refetchQueries({ queryKey: ['all-subjects-by-category'] });
            queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });
            queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
          }, 100);
        }
      )


      

      
      // Listen to session_categories table changes
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'session_categories'
        },
        (payload) => {
          console.log('📡 Real-time: session_categories table changed', payload);
          
          // Invalidate category-related queries
          queryClient.invalidateQueries({ queryKey: ['session-categories'] });
          queryClient.invalidateQueries({ queryKey: ['subjects'] });
          queryClient.invalidateQueries({ queryKey: ['schedules'] });
          queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
        }
      )
      
      // Listen to classes table changes
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'classes'
        },
        (payload) => {
          console.log('📡 Real-time: classes table changed', payload);
          
          // Invalidate class-related queries
          queryClient.invalidateQueries({ queryKey: ['classes'] });
          queryClient.invalidateQueries({ queryKey: ['schedules'] });
          queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
          queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });
        }
      )
      
      // Listen to teachers table changes
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'teachers'
        },
        (payload) => {
          console.log('📡 Real-time: teachers table changed', payload);
          
          // Invalidate teacher-related queries
          queryClient.invalidateQueries({ queryKey: ['teachers'] });
          queryClient.invalidateQueries({ queryKey: ['schedules'] });
        }
      )
      
      // Listen to time_sessions table changes
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'time_sessions'
        },
        (payload) => {
          console.log('📡 Real-time: time_sessions table changed', payload);

          // Invalidate time session-related queries
          queryClient.invalidateQueries({ queryKey: ['time-sessions'] });
          queryClient.invalidateQueries({ queryKey: ['schedules'] });
        }
      )

      // ✅ NEW: Listen to schedule_class_subjects table changes
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'schedule_class_subjects'
        },
        async (payload) => {
          console.log('📡 Real-time: schedule_class_subjects table changed', payload);

          // ✅ ENHANCED: Comprehensive invalidation for class-subject assignments
          await Promise.all([
            queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] }),
            queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['matrix-class-subjects'] }),
            queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] }),
            queryClient.invalidateQueries({ queryKey: ['class-subjects-by-category'] }),
            queryClient.invalidateQueries({ queryKey: ['subjects'] })
          ]);

          // Force immediate refetch for critical data
          setTimeout(() => {
            queryClient.refetchQueries({ queryKey: ['schedule-class-subjects'] });
            queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });
            queryClient.refetchQueries({ queryKey: ['all-subjects-by-category'] });
            queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
          }, 100);
        }
      )
      
      .subscribe((status) => {
        console.log('📡 Real-time sync subscription status:', status);

        if (status === 'SUBSCRIBED') {
          console.log('✅ Real-time sync successfully established for subjects matrix');
          console.log('🎯 Listening to tables: subjects, schedule_subjects, schedule_class_subjects, session_categories, classes');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Real-time sync channel error');
        } else if (status === 'TIMED_OUT') {
          console.warn('⚠️ Real-time sync timed out');
        } else if (status === 'CLOSED') {
          console.log('🔒 Real-time sync channel closed');
        }
      });

    // Cleanup function
    return () => {
      console.log('🔄 Cleaning up real-time sync subscriptions...');
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  // Return status for debugging purposes
  return {
    isActive: true,
    message: 'Real-time sync is active'
  };
};

/**
 * Hook for components that need to ensure real-time updates
 * This is a lightweight wrapper that can be used in specific components
 */
export const useScheduleRealTime = () => {
  const queryClient = useQueryClient();

  // Force refresh all schedule-related data
  const forceRefresh = () => {
    console.log('🔄 Force refreshing all schedule data...');

    // 🚀 ENHANCED: Comprehensive invalidation for immediate UI updates
    queryClient.invalidateQueries({ queryKey: ['schedules'] });
    queryClient.invalidateQueries({ queryKey: ['schedules-simple'] });
    queryClient.invalidateQueries({ queryKey: ['schedules-paginated'] });
    queryClient.invalidateQueries({ queryKey: ['schedules-complete'] });
    queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] });
    queryClient.invalidateQueries({ queryKey: ['schedules-week'] });
    queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
    queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });
    queryClient.invalidateQueries({ queryKey: ['subjects'] });
    queryClient.invalidateQueries({ queryKey: ['session-categories'] });
    queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
    queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

    // 🚀 ENHANCED: Force immediate refetch for real-time UI updates
    queryClient.refetchQueries({ queryKey: ['schedules-complete'] });
    queryClient.refetchQueries({ queryKey: ['schedules-simple'] });
    queryClient.refetchQueries({ queryKey: ['jp-progress-simple'] });
  };

  return {
    forceRefresh
  };
};
