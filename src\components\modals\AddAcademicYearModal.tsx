
import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CalendarDays, Loader2, Save } from 'lucide-react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface AddAcademicYearModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AddAcademicYearModal: React.FC<AddAcademicYearModalProps> = ({
  isOpen,
  onClose
}) => {
  const [yearName, setYearName] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const { toast } = useToast();
  const { profile } = useAuth();
  const queryClient = useQueryClient();

  const createAcademicYear = useMutation({
    mutationFn: async () => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      const { data, error } = await supabase
        .from('academic_years')
        .insert({
          year_name: yearName,
          start_date: startDate,
          end_date: endDate,
          school_id: profile.school_id,
          is_active: false
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['academic-years'] });
      toast({
        title: "Berhasil",
        description: "Tahun ajaran baru berhasil ditambahkan",
      });
      handleClose();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal menambahkan tahun ajaran",
        variant: "destructive",
      });
      console.error('Error creating academic year:', error);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!yearName || !startDate || !endDate) {
      toast({
        title: "Error",
        description: "Semua field harus diisi",
        variant: "destructive",
      });
      return;
    }
    createAcademicYear.mutate();
  };

  const handleClose = () => {
    setYearName('');
    setStartDate('');
    setEndDate('');
    onClose();
  };

  // Auto-generate year name based on dates
  React.useEffect(() => {
    if (startDate && endDate) {
      const startYear = new Date(startDate).getFullYear();
      const endYear = new Date(endDate).getFullYear();
      setYearName(`${startYear}/${endYear}`);
    }
  }, [startDate, endDate]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-gradient-to-br from-gray-800/95 via-gray-900/90 to-slate-900/95 border-purple-400/30 backdrop-blur-xl shadow-2xl shadow-purple-400/20 max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl bg-gradient-to-r from-white via-purple-200 to-cyan-200 bg-clip-text text-transparent">
            <div className="p-2 bg-gradient-to-br from-purple-400/20 to-purple-600/10 rounded-lg border border-purple-400/30 glow-purple">
              <CalendarDays className="h-5 w-5 text-purple-400" />
            </div>
            Tambah Tahun Ajaran
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Buat tahun ajaran baru untuk sekolah
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="start-date" className="text-gray-300 font-medium">
                Tanggal Mulai
              </Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="bg-gray-900/80 border-purple-400/30 text-white focus:border-purple-400/50 focus:ring-purple-400/20 focus:ring-2 rounded-xl"
                required
              />
            </div>

            <div>
              <Label htmlFor="end-date" className="text-gray-300 font-medium">
                Tanggal Selesai
              </Label>
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="bg-gray-900/80 border-purple-400/30 text-white focus:border-purple-400/50 focus:ring-purple-400/20 focus:ring-2 rounded-xl"
                required
              />
            </div>

            <div>
              <Label htmlFor="year-name" className="text-gray-300 font-medium">
                Nama Tahun Ajaran
              </Label>
              <Input
                id="year-name"
                type="text"
                value={yearName}
                onChange={(e) => setYearName(e.target.value)}
                placeholder="2024/2025"
                className="bg-gray-900/80 border-purple-400/30 text-white focus:border-purple-400/50 focus:ring-purple-400/20 focus:ring-2 rounded-xl"
                required
              />
            </div>
          </div>

          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="border-purple-400/30 text-purple-300 hover:bg-gradient-to-r hover:from-purple-400/10 hover:to-cyan-400/10 hover:text-white rounded-xl backdrop-blur-sm hover:border-purple-400/50 transition-all duration-300"
            >
              Batal
            </Button>
            <Button
              type="submit"
              disabled={createAcademicYear.isPending}
              className="bg-gradient-to-r from-purple-400 to-cyan-500 hover:from-purple-500 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-purple-400/25 transition-all duration-300 glow-purple"
            >
              {createAcademicYear.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Simpan
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
