import React, { useState, useRef, useMemo, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import dayGridPlugin from '@fullcalendar/daygrid';
import idLocale from '@fullcalendar/core/locales/id';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Share2, Filter, School, ChevronLeft, ChevronRight } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// ✅ PUBLIC COMPONENT: No authentication required
const PublicExternalView = () => {
  const [selectedClassId, setSelectedClassId] = useState<string>('');
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [shareableLink, setShareableLink] = useState<string>('');
  const [schedules, setSchedules] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [academicWeeks, setAcademicWeeks] = useState<any[]>([]);
  const [timeSessions, setTimeSessions] = useState<any[]>([]);
  const [school, setSchool] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const calendarRef = useRef<FullCalendar>(null);
  const { toast } = useToast();

  // ✅ Parse URL parameters for shareable links
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const classParam = urlParams.get('class');
    const weekParam = urlParams.get('week');

    if (classParam) {
      setSelectedClassId(classParam);
    }
    if (weekParam) {
      const weekNumber = parseInt(weekParam, 10);
      if (weekNumber >= 1 && weekNumber <= 24) {
        setSelectedWeek(weekNumber);
      }
    }
  }, []);

  // ✅ Fetch public data without authentication
  useEffect(() => {
    const fetchPublicData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // ✅ FIXED: Use correct table structure
        const { data: schedulesData, error: schedulesError } = await supabase
          .from('class_schedules')
          .select(`
            id,
            academic_week,
            academic_year_id,
            class_id,
            day_of_week,
            start_time,
            end_time,
            subject_id,
            teacher_id,
            room,
            notes,
            created_at,
            schedule_subjects (id, name, code, color),
            classes (id, name, level, grade),
            teachers (id, full_name, nip)
          `)
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true });

        if (schedulesError) throw schedulesError;

        // Fetch classes
        const { data: classesData, error: classesError } = await supabase
          .from('classes')
          .select('*')
          .order('name', { ascending: true });

        if (classesError) throw classesError;

        // ✅ FIXED: Use correct academic weeks table
        const { data: weeksData, error: weeksError } = await supabase
          .rpc('get_academic_weeks')
          .order('week_number', { ascending: true });

        if (weeksError) {
          // Fallback: generate weeks 1-24
          const fallbackWeeks = Array.from({ length: 24 }, (_, i) => ({
            week_number: i + 1,
            start_date: new Date(2025, 0, 6 + (i * 7)).toISOString().split('T')[0],
            end_date: new Date(2025, 0, 12 + (i * 7)).toISOString().split('T')[0]
          }));
          setAcademicWeeks(fallbackWeeks);
        } else {
          setAcademicWeeks(weeksData || []);
        }

        // Fetch time sessions
        const { data: timeData, error: timeError } = await supabase
          .from('time_sessions')
          .select('*')
          .order('start_time', { ascending: true });

        if (timeError) throw timeError;

        // Fetch school info
        const { data: schoolData, error: schoolError } = await supabase
          .from('schools')
          .select('*')
          .limit(1)
          .single();

        if (schoolError && schoolError.code !== 'PGRST116') throw schoolError;

        setSchedules(schedulesData || []);
        setClasses(classesData || []);
        // Academic weeks already set above
        setTimeSessions(timeData || []);
        setSchool(schoolData);

        console.log('✅ Public data fetched successfully:', {
          schedules: schedulesData?.length || 0,
          classes: classesData?.length || 0,
          weeks: weeksData?.length || 0,
          timeSessions: timeData?.length || 0
        });

      } catch (err: any) {
        console.error('❌ Error fetching public data:', err);
        setError(err.message || 'Failed to load schedule data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPublicData();
  }, []);

  // ✅ Generate shareable link for public access
  useEffect(() => {
    const params = new URLSearchParams();
    if (selectedClassId) params.set('class', selectedClassId);
    params.set('week', selectedWeek.toString());
    
    const baseUrl = window.location.origin;
    setShareableLink(`${baseUrl}/public/schedule?${params.toString()}`);
  }, [selectedClassId, selectedWeek]);

  // ✅ FIXED: Filter schedules with correct logic
  const filteredSchedules = useMemo(() => {
    if (!schedules || schedules.length === 0) return [];

    return schedules.filter(schedule => {
      if (schedule.academic_week !== selectedWeek) return false;
      if (selectedClassId && schedule.class_id !== selectedClassId) return false;
      if (!schedule.start_time || !schedule.end_time) return false;
      if (!schedule.schedule_subjects?.name) return false; // Ensure subject exists
      return true;
    });
  }, [schedules, selectedWeek, selectedClassId]);

  // ✅ FIXED: Convert to calendar events with correct data structure
  const calendarEvents = useMemo(() => {
    if (!filteredSchedules || filteredSchedules.length === 0) return [];

    const currentWeek = academicWeeks.find(week => week.week_number === selectedWeek);
    if (!currentWeek) return [];

    return filteredSchedules.map(schedule => {
      const weekStartDate = new Date(currentWeek.start_date);
      const scheduleDate = new Date(weekStartDate);

      const dayOffset = schedule.day_of_week === 7 ? 0 : schedule.day_of_week;
      scheduleDate.setDate(weekStartDate.getDate() + dayOffset);

      const dateStr = scheduleDate.toISOString().split('T')[0];

      // ✅ FIXED: Handle correct data structure from class_schedules
      const subject = schedule.schedule_subjects;
      const teacher = schedule.teachers;

      return {
        id: schedule.id,
        title: subject?.name || 'Mata Pelajaran',
        start: `${dateStr}T${schedule.start_time}`,
        end: `${dateStr}T${schedule.end_time}`,
        backgroundColor: subject?.color || '#6b7280',
        borderColor: subject?.color || '#6b7280',
        textColor: '#ffffff',
        extendedProps: {
          teacher: teacher?.full_name,
          classroom: schedule.room
        }
      };
    }).filter(Boolean);
  }, [filteredSchedules, academicWeeks, selectedWeek]);

  // ✅ Calculate time range
  const timeRange = useMemo(() => {
    if (!timeSessions || timeSessions.length === 0) {
      return { slotMinTime: '06:00:00', slotMaxTime: '18:00:00' };
    }

    const startTimes = timeSessions.map(session => session.start_time);
    const endTimes = timeSessions.map(session => session.end_time);

    const earliestStart = startTimes.sort()[0];
    const latestEnd = endTimes.sort().reverse()[0];

    return {
      slotMinTime: earliestStart || '06:00:00',
      slotMaxTime: latestEnd || '18:00:00'
    };
  }, [timeSessions]);

  const copyShareableLink = async () => {
    try {
      await navigator.clipboard.writeText(shareableLink);
      toast({
        title: "✅ Link Berhasil Disalin",
        description: "Link jadwal telah disalin ke clipboard.",
      });
    } catch (error) {
      toast({
        title: "❌ Gagal Menyalin Link",
        description: "Terjadi kesalahan saat menyalin link.",
        variant: "destructive",
      });
    }
  };

  const goToPreviousWeek = () => {
    if (selectedWeek > 1) {
      setSelectedWeek(selectedWeek - 1);
    }
  };

  const goToNextWeek = () => {
    if (selectedWeek < academicWeeks.length) {
      setSelectedWeek(selectedWeek + 1);
    }
  };

  const currentWeek = academicWeeks.find(week => week.week_number === selectedWeek);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-400 mx-auto mb-4"></div>
          <div className="text-white text-lg">Memuat Jadwal...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-lg mb-4">❌ Gagal Memuat Jadwal</div>
          <div className="text-gray-400">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800/50 border-b border-gray-700/50 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Calendar className="h-8 w-8 text-lime-400" />
              <div>
                <h1 className="text-2xl font-bold text-white">
                  {school?.name || 'Jadwal Sekolah'}
                </h1>
                <p className="text-gray-400">Public Schedule View</p>
              </div>
            </div>
            <Button onClick={copyShareableLink} className="bg-lime-400 hover:bg-lime-500 text-gray-900">
              <Share2 className="h-4 w-4 mr-2" />
              Copy Link
            </Button>
          </div>

          {/* Filters and Navigation */}
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-400">Filter:</span>
              </div>
              
              <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                <SelectTrigger className="w-48 bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Pilih Kelas" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={cls.id} className="text-white hover:bg-gray-600">
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Week Navigation */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPreviousWeek}
                disabled={selectedWeek <= 1}
                className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <div className="text-center px-4">
                <div className="text-white font-semibold">Minggu {selectedWeek}</div>
                {currentWeek && (
                  <div className="text-xs text-gray-400">
                    {new Date(currentWeek.start_date).toLocaleDateString('id-ID')} - {new Date(currentWeek.end_date).toLocaleDateString('id-ID')}
                  </div>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={goToNextWeek}
                disabled={selectedWeek >= academicWeeks.length}
                className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar */}
      <div className="max-w-7xl mx-auto p-4">
        <Card className="bg-gray-800/50 border-gray-700/50">
          <CardContent className="p-6">
            <div style={{ minHeight: '600px' }}>
              <FullCalendar
                ref={calendarRef}
                plugins={[timeGridPlugin, dayGridPlugin]}
                initialView="timeGridWeek"
                headerToolbar={false}
                events={calendarEvents}
                editable={false}
                selectable={false}
                height="auto"
                locale={idLocale}
                slotMinTime={timeRange.slotMinTime}
                slotMaxTime={timeRange.slotMaxTime}
                slotDuration="00:30:00"
                slotLabelInterval="01:00:00"
                allDaySlot={false}
                weekends={true}
                dayHeaderFormat={{
                  weekday: 'short',
                  month: 'numeric',
                  day: 'numeric'
                }}
                eventContent={(eventInfo) => (
                  <div className="p-1 text-xs">
                    <div className="font-semibold truncate">{eventInfo.event.title}</div>
                    <div className="text-xs opacity-75 truncate">
                      {eventInfo.event.extendedProps.teacher}
                    </div>
                  </div>
                )}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PublicExternalView;
