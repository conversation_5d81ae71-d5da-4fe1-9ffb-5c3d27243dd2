
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, BookOpen, Users, Trash2 } from 'lucide-react';
import { useSubjects } from '@/hooks/useSubjects';
import { useClasses } from '@/hooks/useClasses';
import { useClassSubjects, useCreateClassSubject, useDeleteClassSubject } from '@/hooks/useClassSubjects';
import AddSubjectModal from '@/components/modals/AddSubjectModal';
import AssignSubjectModal from '@/components/modals/AssignSubjectModal';

const SubjectClassMatrixPage = () => {
  const [isAddSubjectModalOpen, setIsAddSubjectModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState<any>(null);
  const [selectedClass, setSelectedClass] = useState<any>(null);

  const { data: subjects, isLoading: subjectsLoading } = useSubjects();
  const { data: classes, isLoading: classesLoading } = useClasses();
  const { data: classSubjects, isLoading: classSubjectsLoading } = useClassSubjects();
  const createClassSubject = useCreateClassSubject();
  const deleteClassSubject = useDeleteClassSubject();

  const subjectColors = [
    'bg-purple-400/10 border-purple-400/20 text-purple-400',
    'bg-orange-400/10 border-orange-400/20 text-orange-400',
    'bg-blue-400/10 border-blue-400/20 text-blue-400',
    'bg-green-400/10 border-green-400/20 text-green-400',
    'bg-cyan-400/10 border-cyan-400/20 text-cyan-400',
    'bg-pink-400/10 border-pink-400/20 text-pink-400',
    'bg-yellow-400/10 border-yellow-400/20 text-yellow-400',
    'bg-red-400/10 border-red-400/20 text-red-400',
  ];

  const getSubjectColor = (index: number) => {
    return subjectColors[index % subjectColors.length];
  };

  const getClassSubject = (subjectId: string, classId: string) => {
    return classSubjects?.find(cs => cs.subject_id === subjectId && cs.class_id === classId);
  };

  const handleAddSubjectToClass = (subject: any, classItem: any) => {
    const existingAssignment = getClassSubject(subject.id, classItem.id);
    if (existingAssignment) return;

    createClassSubject.mutate({
      subject_id: subject.id,
      class_id: classItem.id,
      hours_per_week: 2
    });
  };

  const handleRemoveSubjectFromClass = (subjectId: string, classId: string) => {
    const assignment = getClassSubject(subjectId, classId);
    if (assignment) {
      deleteClassSubject.mutate(assignment.id);
    }
  };

  const sortedClasses = classes?.sort((a, b) => {
    const gradeOrder = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'];
    const aIndex = gradeOrder.indexOf(a.level);
    const bIndex = gradeOrder.indexOf(b.level);
    if (aIndex !== bIndex) return aIndex - bIndex;
    return a.name.localeCompare(b.name);
  }) || [];

  if (subjectsLoading || classesLoading || classSubjectsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-white">Memuat data...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Daftar Mata Pelajaran KBM per Kelas
            </h1>
            <p className="text-muted-foreground">Kelola penugasan mata pelajaran untuk setiap kelas</p>
          </div>
          
          <Button 
            onClick={() => setIsAddSubjectModalOpen(true)}
            className="bg-gradient-to-r from-lime-400 to-lime-600 hover:from-lime-500 hover:to-lime-700 text-gray-900 font-semibold shadow-lg shadow-lime-400/25 transition-all duration-300 transform hover:scale-105"
          >
            <Plus className="mr-2 h-5 w-5" />
            Tambah Mata Pelajaran
          </Button>
        </div>

        {/* Matrix Grid */}
        <Card className="bg-gray-800/40 backdrop-blur-sm border-gray-600/30">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <BookOpen className="mr-2 h-5 w-5 text-lime-400" />
              Matrix Mata Pelajaran & Kelas
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {sortedClasses.length === 0 ? (
              <div className="p-8 text-center">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Belum ada kelas</h3>
                <p className="text-gray-400">Silakan tambah kelas terlebih dahulu di menu Kelas</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <div className="min-w-max">
                  {/* Header with class names */}
                  <div className="grid grid-cols-[250px_repeat(auto-fit,minmax(150px,1fr))] gap-2 p-4 border-b border-gray-600/30">
                    <div className="text-gray-400 font-medium">Mata Pelajaran</div>
                    {sortedClasses.map((classItem) => (
                      <div key={classItem.id} className="text-center">
                        <div className="text-white font-medium text-sm">Kelas {classItem.level}</div>
                        <div className="text-gray-400 text-xs">{classItem.name}</div>
                      </div>
                    ))}
                  </div>

                  {/* Subject rows */}
                  <div className="space-y-2 p-4">
                    {subjects?.map((subject, index) => (
                      <div key={subject.id} className="grid grid-cols-[250px_repeat(auto-fit,minmax(150px,1fr))] gap-2 items-center">
                        {/* Subject name */}
                        <div className="flex items-center space-x-2">
                          <Badge className={`${getSubjectColor(index)} border text-xs px-2 py-1`}>
                            {subject.code}
                          </Badge>
                          <span className="text-white text-sm font-medium truncate">
                            {subject.name}
                          </span>
                        </div>

                        {/* Class assignments */}
                        {sortedClasses.map((classItem) => {
                          const assignment = getClassSubject(subject.id, classItem.id);
                          return (
                            <div key={classItem.id} className="flex justify-center">
                              {assignment ? (
                                <div className="group relative">
                                  <Badge className={`${getSubjectColor(index)} border text-xs px-2 py-1 cursor-pointer hover:opacity-80`}>
                                    {assignment.hours_per_week} JP/Minggu
                                  </Badge>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveSubjectFromClass(subject.id, classItem.id)}
                                    className="absolute -top-2 -right-2 h-4 w-4 p-0 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                  >
                                    <Trash2 className="h-2 w-2" />
                                  </Button>
                                </div>
                              ) : (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleAddSubjectToClass(subject, classItem)}
                                  className="h-8 w-8 p-0 text-blue-400 hover:bg-blue-400/20 border border-dashed border-blue-400/30 rounded"
                                  disabled={createClassSubject.isPending}
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    ))}

                    {(!subjects || subjects.length === 0) && (
                      <div className="col-span-full text-center py-8">
                        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-white mb-2">Belum ada mata pelajaran</h3>
                        <p className="text-gray-400 mb-4">Mulai dengan menambahkan mata pelajaran pertama</p>
                        <Button 
                          onClick={() => setIsAddSubjectModalOpen(true)}
                          className="bg-gradient-to-r from-lime-400 to-lime-600 hover:from-lime-500 hover:to-lime-700 text-gray-900 font-semibold"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Tambah Mata Pelajaran
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <AddSubjectModal
          open={isAddSubjectModalOpen}
          onOpenChange={setIsAddSubjectModalOpen}
        />

        <AssignSubjectModal
          open={isAssignModalOpen}
          onOpenChange={setIsAssignModalOpen}
        />
      </div>
    </div>
  );
};

export default SubjectClassMatrixPage;
