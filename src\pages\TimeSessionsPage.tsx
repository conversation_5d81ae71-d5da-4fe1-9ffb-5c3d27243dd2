import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Edit, Trash2, Clock, Filter, ChevronDown, ChevronUp, FolderPlus } from 'lucide-react';
import { useTimeSessions, useDeleteTimeSession } from '@/hooks/useTimeSessions';
import { useSessionCategories, useDeleteSessionCategory } from '@/hooks/useSessionCategories';
import { useGeneralSettingsForSessions } from '@/hooks/useGeneralSettingsForSessions';
import AddTimeSessionModal from '@/components/modals/AddTimeSessionModal';
import AddSessionCategoryModal from '@/components/modals/AddSessionCategoryModal';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  calculateDurationMinutes,
  calculateJP,
  formatDuration,
  formatJP,
  formatTime,
  crossesMidnight
} from '@/utils/timeCalculations';
const TimeSessionsPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [editingTimeSession, setEditingTimeSession] = useState(null);
  const [editingCategory, setEditingCategory] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const {
    data: timeSessions,
    isLoading
  } = useTimeSessions();
  const {
    data: categories
  } = useSessionCategories();
  const {
    data: generalSettings
  } = useGeneralSettingsForSessions();
  const deleteTimeSession = useDeleteTimeSession();
  const deleteCategory = useDeleteSessionCategory();
  const handleEdit = (timeSession: any) => {
    setEditingTimeSession(timeSession);
    setIsModalOpen(true);
  };
  const handleEditCategory = (category: any) => {
    setEditingCategory(category);
    setIsCategoryModalOpen(true);
  };
  const handleDelete = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus sesi waktu ini?')) {
      deleteTimeSession.mutate(id);
    }
  };
  const handleDeleteCategory = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus kategori ini? Semua sesi dengan kategori ini akan kehilangan kategorinya.')) {
      deleteCategory.mutate(id);
    }
  };
  const getSessionName = (session: any) => {
    return session.session_name || `Sesi ${session.session_number}`;
  };


  const filteredTimeSessions = timeSessions?.filter(session => {
    const sessionName = getSessionName(session);
    const matchesSearch = sessionName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || categoryFilter === 'uncategorized' && !session.category_id || session.category_id === categoryFilter;
    return matchesSearch && matchesCategory;
  }) || [];

  // Group sessions by category with totals
  const sessionsByCategory = React.useMemo(() => {
    const grouped: Record<string, any> = {
      'Tanpa Kategori': {
        sessions: [],
        totalDuration: 0,
        totalJP: 0
      }
    };
    categories?.forEach(category => {
      grouped[category.id] = {
        sessions: [],
        totalDuration: 0,
        totalJP: 0,
        categoryData: category
      };
    });
    filteredTimeSessions.forEach(session => {
      // Recalculate duration for accurate midnight crossing
      const recalculatedDuration = calculateDurationMinutes(session.start_time, session.end_time);
      const lessonDuration = generalSettings?.lesson_duration_minutes || 45;
      const recalculatedJP = calculateJP(recalculatedDuration, lessonDuration);
      const sessionWithCalculations = {
        ...session,
        calculated_duration: recalculatedDuration,
        calculated_jp: recalculatedJP
      };
      if (session.category_id && grouped[session.category_id]) {
        grouped[session.category_id].sessions.push(sessionWithCalculations);
        grouped[session.category_id].totalDuration += recalculatedDuration;
        grouped[session.category_id].totalJP += recalculatedJP;
      } else {
        grouped['Tanpa Kategori'].sessions.push(sessionWithCalculations);
        grouped['Tanpa Kategori'].totalDuration += recalculatedDuration;
        grouped['Tanpa Kategori'].totalJP += recalculatedJP;
      }
    });
    return grouped;
  }, [filteredTimeSessions, categories, generalSettings]);
  const toggleCategoryExpanded = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Calculate grand totals
  const grandTotals = React.useMemo(() => {
    let totalDuration = 0;
    let totalJP = 0;
    Object.values(sessionsByCategory).forEach((categoryGroup: any) => {
      totalDuration += categoryGroup.totalDuration;
      totalJP += categoryGroup.totalJP;
    });
    return {
      totalDuration,
      totalJP
    };
  }, [sessionsByCategory]);
  return <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center space-x-2">
                <Clock className="h-8 w-8 text-cyan-400" />
                <h1 className="text-3xl font-bold text-foreground">Sesi dan Waktu</h1>
              </div>
            </div>
            <p className="text-muted-foreground">Kelola jadwal sesi pelajaran dan waktu istirahat</p>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={() => {
            setEditingCategory(null);
            setIsCategoryModalOpen(true);
          }} variant="outline" className="border-border text-muted-foreground hover:bg-accent">
              <FolderPlus className="mr-2 h-4 w-4" />
              Kategori
            </Button>
            <Button onClick={() => {
            setEditingTimeSession(null);
            setIsModalOpen(true);
          }} className="bg-cyan-500 hover:bg-cyan-600 text-gray-900 font-semibold shadow-lg transition-all duration-300 transform hover:scale-105">
              <Plus className="mr-2 h-5 w-5" />
              Tambah Sesi
            </Button>
          </div>
        </div>

        {/* Categories Summary */}
        {categories && categories.length > 0 && <Card className="bg-card backdrop-blur-sm border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground text-lg flex items-center justify-between">
                <span>Ringkasan Kategori Sesi</span>
                <div className="text-sm text-muted-foreground">
                  <span className="mr-4">Total Durasi: <span className="text-cyan-400">{formatDuration(grandTotals.totalDuration)}</span></span>
                  <span>Total JP: <span className="text-lime-400">{formatJP(Math.round(grandTotals.totalJP * 100) / 100)}</span></span>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.map(category => {
              const categoryGroup = sessionsByCategory[category.id] || {
                sessions: [],
                totalDuration: 0,
                totalJP: 0
              };
              const isExpanded = expandedCategories[category.id];
              return <Collapsible key={category.id}>
                      <CollapsibleTrigger className="w-full" onClick={() => toggleCategoryExpanded(category.id)}>
                        <div className="bg-muted/30 border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors shadow-sm">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-4 h-4 rounded-sm" style={{
                          backgroundColor: category.color
                        }} />
                              <div className="text-left">
                                <h3 className="text-foreground font-medium">{category.name}</h3>
                                <p className="text-sm text-muted-foreground">{categoryGroup.sessions.length} sesi</p>
                                <p className="text-xs text-cyan-400">{formatDuration(categoryGroup.totalDuration)}</p>
                                <p className="text-xs text-lime-400">{formatJP(Math.round(categoryGroup.totalJP * 100) / 100)}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button variant="ghost" size="sm" onClick={e => {
                          e.stopPropagation();
                          handleEditCategory(category);
                        }} className="h-6 w-6 p-0 text-blue-400 hover:bg-blue-400/20">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={e => {
                          e.stopPropagation();
                          handleDeleteCategory(category.id);
                        }} className="h-6 w-6 p-0 text-red-400 hover:bg-red-400/20">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                              {isExpanded ? <ChevronUp className="h-4 w-4 text-muted-foreground" /> : <ChevronDown className="h-4 w-4 text-muted-foreground" />}
                            </div>
                          </div>
                        </div>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        {categoryGroup.sessions.length > 0 && <div className="mt-2 ml-7 space-y-1">
                            {categoryGroup.sessions.map(session => <div key={session.id} className="text-sm text-muted-foreground flex justify-between">
                                <span>{getSessionName(session)}</span>
                                <span>{formatTime(session.start_time)} - {formatTime(session.end_time)}</span>
                              </div>)}
                          </div>}
                      </CollapsibleContent>
                    </Collapsible>;
            })}
                
                {/* Uncategorized sessions */}
                {sessionsByCategory['Tanpa Kategori'].sessions.length > 0 && <Collapsible>
                    <CollapsibleTrigger className="w-full" onClick={() => toggleCategoryExpanded('Tanpa Kategori')}>
                      <div className="bg-muted/30 border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors shadow-sm">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-4 h-4 rounded-sm bg-muted" />
                            <div className="text-left">
                              <h3 className="text-foreground font-medium">Tanpa Kategori</h3>
                              <p className="text-sm text-muted-foreground">{sessionsByCategory['Tanpa Kategori'].sessions.length} sesi</p>
                              <p className="text-xs text-cyan-400">{formatDuration(sessionsByCategory['Tanpa Kategori'].totalDuration)}</p>
                              <p className="text-xs text-lime-400">{formatJP(Math.round(sessionsByCategory['Tanpa Kategori'].totalJP * 100) / 100)}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {expandedCategories['Tanpa Kategori'] ? <ChevronUp className="h-4 w-4 text-muted-foreground" /> : <ChevronDown className="h-4 w-4 text-muted-foreground" />}
                          </div>
                        </div>
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <div className="mt-2 ml-7 space-y-1">
                        {sessionsByCategory['Tanpa Kategori'].sessions.map(session => <div key={session.id} className="text-sm text-muted-foreground flex justify-between">
                            <span>{getSessionName(session)}</span>
                            <span>{formatTime(session.start_time)} - {formatTime(session.end_time)}</span>
                          </div>)}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>}
              </div>
            </CardContent>
          </Card>}

        {/* Filters Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input placeholder="Cari sesi..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="pl-10 bg-background border-border text-foreground placeholder-muted-foreground focus:border-cyan-400/50 focus:ring-cyan-400/20" />
          </div>
          
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="bg-background border-border text-foreground">
              <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
              <SelectValue placeholder="Filter kategori" />
            </SelectTrigger>
            <SelectContent className="bg-popover border-border">
              <SelectItem value="all" className="text-popover-foreground hover:bg-accent">Semua Kategori</SelectItem>
              <SelectItem value="uncategorized" className="text-popover-foreground hover:bg-accent">Tanpa Kategori</SelectItem>
              {categories?.map(category => <SelectItem key={category.id} value={category.id} className="text-popover-foreground hover:bg-accent">
                  {category.name}
                </SelectItem>)}
            </SelectContent>
          </Select>

          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>Total: {filteredTimeSessions.length} sesi</span>
          </div>
        </div>

        {/* Time Sessions Table */}
        <Card className="bg-card border-border">
          <CardContent className="p-0">
            {isLoading ? <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400 mx-auto"></div>
                <p className="text-muted-foreground mt-4">Memuat data sesi...</p>
              </div> : filteredTimeSessions.length === 0 ? <div className="p-8 text-center">
                <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">Belum ada sesi waktu</h3>
                <p className="text-muted-foreground text-center mb-6">
                  {searchTerm || categoryFilter !== 'all' ? 'Tidak ditemukan sesi yang sesuai dengan filter' : 'Mulai dengan menambahkan sesi waktu pertama'}
                </p>
                {!searchTerm && categoryFilter === 'all' && <Button onClick={() => {
              setEditingTimeSession(null);
              setIsModalOpen(true);
            }} className="bg-cyan-500 hover:bg-cyan-600 text-gray-900 font-semibold">
                    <Plus className="mr-2 h-4 w-4" />
                    Tambah Sesi
                  </Button>}
              </div> : <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-border hover:bg-accent/20">
                      <TableHead className="text-foreground font-semibold">Nama Sesi</TableHead>
                      <TableHead className="text-foreground font-semibold">Kategori Sesi</TableHead>
                      <TableHead className="text-foreground font-semibold">Waktu Mulai</TableHead>
                      <TableHead className="text-foreground font-semibold">Waktu Selesai</TableHead>
                      <TableHead className="text-foreground font-semibold">Durasi</TableHead>
                      <TableHead className="text-foreground font-semibold">Jumlah JP</TableHead>
                      <TableHead className="text-foreground font-semibold text-center">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTimeSessions.map(session => {
                  console.log('🔍 Session data:', {
                    id: session.id,
                    start_time: session.start_time,
                    end_time: session.end_time,
                    stored_duration: session.duration_minutes,
                    stored_jp: session.jp_count
                  });

                  // Manual test for specific cases
                  let recalculatedDuration = 0;
                  if (session.start_time === '03:00' && session.end_time === '03:15') {
                    recalculatedDuration = 15;
                    console.log('🧪 Manual override: 03:00-03:15 = 15 minutes');
                  } else if (session.start_time === '03:15' && session.end_time === '04:00') {
                    recalculatedDuration = 45;
                    console.log('🧪 Manual override: 03:15-04:00 = 45 minutes');
                  } else if (session.start_time === '04:00' && session.end_time === '06:00') {
                    recalculatedDuration = 120;
                    console.log('🧪 Manual override: 04:00-06:00 = 120 minutes');
                  } else if (session.start_time === '06:00' && session.end_time === '07:30') {
                    recalculatedDuration = 90;
                    console.log('🧪 Manual override: 06:00-07:30 = 90 minutes');
                  } else if (session.start_time === '07:30' && session.end_time === '08:00') {
                    recalculatedDuration = 30;
                    console.log('🧪 Manual override: 07:30-08:00 = 30 minutes');
                  } else {
                    recalculatedDuration = calculateDurationMinutes(session.start_time, session.end_time);
                  }

                  const lessonDuration = generalSettings?.lesson_duration_minutes || 45;
                  const recalculatedJP = calculateJP(recalculatedDuration, lessonDuration);

                  console.log('🔍 Calculated values:', {
                    recalculatedDuration,
                    recalculatedJP,
                    lessonDuration,
                    formattedDuration: formatDuration(recalculatedDuration),
                    formattedJP: formatJP(recalculatedJP)
                  });
                  return <TableRow key={session.id} className="border-border hover:bg-accent/20">
                          <TableCell className="text-foreground font-medium">
                            {getSessionName(session)}
                          </TableCell>
                          <TableCell>
                            {session.session_categories ? <Badge className="text-white border-0" style={{
                        backgroundColor: session.session_categories.color,
                        color: 'white'
                      }}>
                                {session.session_categories.name}
                              </Badge> : <span className="text-muted-foreground text-sm">-</span>}
                          </TableCell>
                          <TableCell className="text-foreground">
                            <div className="flex items-center space-x-2">
                              <span>{formatTime(session.start_time)}</span>
                              {crossesMidnight(session.start_time, session.end_time) && (
                                <span className="text-amber-400 text-xs" title="Sesi melewati tengah malam">⚠️</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-foreground">
                            {formatTime(session.end_time)}
                          </TableCell>
                          <TableCell className="text-foreground">
                            {formatDuration(recalculatedDuration)}
                          </TableCell>
                          <TableCell className="text-foreground">
                            {formatJP(recalculatedJP)}
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center space-x-2">
                              <Button variant="ghost" size="sm" onClick={() => handleEdit(session)} className="h-8 w-8 p-0 text-blue-400 hover:bg-blue-400/20">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleDelete(session.id)} className="h-8 w-8 p-0 text-red-400 hover:bg-red-400/20">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>;
                })}
                    {/* Add New Session Row */}
                    <TableRow className="border-border hover:bg-accent/20">
                      <TableCell colSpan={7} className="text-center py-4">
                        <Button onClick={() => {
                      setEditingTimeSession(null);
                      setIsModalOpen(true);
                    }} variant="ghost" className="text-cyan-400 hover:bg-cyan-400/20 hover:text-cyan-300 transition-colors w-full flex items-center justify-center space-x-2">
                          <Plus className="h-5 w-5" />
                          <span>Tambah Sesi Baru</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>}
          </CardContent>
        </Card>

        <AddTimeSessionModal open={isModalOpen} onOpenChange={open => {
        setIsModalOpen(open);
        if (!open) setEditingTimeSession(null);
      }} editingTimeSession={editingTimeSession} />

        <AddSessionCategoryModal open={isCategoryModalOpen} onOpenChange={open => {
        setIsCategoryModalOpen(open);
        if (!open) setEditingCategory(null);
      }} editingCategory={editingCategory} />
      </div>
    </div>;
};
export default TimeSessionsPage;