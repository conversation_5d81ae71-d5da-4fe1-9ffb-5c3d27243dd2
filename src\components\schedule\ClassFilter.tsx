
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useClasses } from '@/hooks/useClasses';

interface ClassFilterProps {
  selectedClassId: string | null;
  onClassChange: (classId: string | null) => void;
}

export const ClassFilter: React.FC<ClassFilterProps> = ({
  selectedClassId,
  onClassChange
}) => {
  const { data: classes = [] } = useClasses();

  const sortedClasses = [...classes].sort((a, b) => {
    if (a.level !== b.level) return a.level.localeCompare(b.level);
    if (a.grade !== b.grade) return a.grade - b.grade;
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="flex items-center space-x-3">
      <label className="text-sm font-medium text-foreground">Filter Kelas:</label>
      <Select value={selectedClassId || 'all'} onValueChange={(value) => onClassChange(value === 'all' ? null : value)}>
        <SelectTrigger className="w-48 bg-background border-border text-foreground">
          <SelectValue placeholder="Semua Kelas" />
        </SelectTrigger>
        <SelectContent className="bg-popover border-border">
          <SelectItem value="all" className="text-foreground hover:bg-accent">
            Semua Kelas
          </SelectItem>
          {sortedClasses.map(cls => (
            <SelectItem key={cls.id} value={cls.id} className="text-foreground hover:bg-accent">
              {cls.name} ({cls.level} {cls.grade})
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
