import React, { useState, useMemo } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Trash2,
  Calendar,
  Clock,
  AlertTriangle,
  X,
  CalendarDays,
  CalendarRange,
  CalendarX
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useDeleteSchedule } from '@/hooks/useDeleteSchedule';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { ModalWeekNavigation } from './ModalWeekNavigation';

interface DeleteScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedClassId?: string;
  currentWeek?: number;
  onScheduleDeleted?: () => void;
}

export const DeleteScheduleModal: React.FC<DeleteScheduleModalProps> = ({
  isOpen,
  onClose,
  selectedClassId,
  currentWeek = 1,
  onScheduleDeleted
}) => {
  const [activeTab, setActiveTab] = useState<'day' | 'week' | 'month'>('day');
  const [confirmDelete, setConfirmDelete] = useState(false);

  // Day delete states
  const [targetDay, setTargetDay] = useState<number>(1);
  const [targetDayWeek, setTargetDayWeek] = useState<number>(currentWeek);

  // Multiple days delete states
  const [selectedTargetDays, setSelectedTargetDays] = useState<Set<number>>(new Set([1]));
  const [isMultipleDaysMode, setIsMultipleDaysMode] = useState<boolean>(false);

  // Week delete states
  const [selectedTargetWeeks, setSelectedTargetWeeks] = useState<Set<number>>(new Set([currentWeek]));

  // Month delete states
  const [targetMonth, setTargetMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedTargetMonths, setSelectedTargetMonths] = useState<Set<number>>(new Set([new Date().getMonth() + 1]));
  const [isMultipleMonthsMode, setIsMultipleMonthsMode] = useState<boolean>(false);

  const { academicWeeks } = useAcademicWeeks();
  const { deleteDay, deleteMultipleDays, deleteWeeks, deleteMonth, getPreviewSchedules, isLoading } = useDeleteSchedule(selectedClassId);

  // Debug logging
  console.log('🔍 DeleteScheduleModal props:', {
    isOpen,
    selectedClassId,
    currentWeek
  });

  // Reset confirmation when modal opens
  React.useEffect(() => {
    if (isOpen) {
      setConfirmDelete(false);
      console.log('🔄 Modal opened, resetting confirmation');
    }
  }, [isOpen]);

  // Handle target week selection
  const handleTargetWeekToggle = (week: number) => {
    const newSelected = new Set(selectedTargetWeeks);
    if (newSelected.has(week)) {
      newSelected.delete(week);
    } else {
      newSelected.add(week);
    }
    setSelectedTargetWeeks(newSelected);
  };

  // Handle target day selection for multiple days
  const handleTargetDayToggle = (day: number) => {
    const newSelected = new Set(selectedTargetDays);
    if (newSelected.has(day)) {
      newSelected.delete(day);
    } else {
      newSelected.add(day);
    }
    setSelectedTargetDays(newSelected);
  };

  // Handle target month selection for multiple months
  const handleTargetMonthToggle = (month: number) => {
    const newSelected = new Set(selectedTargetMonths);
    if (newSelected.has(month)) {
      newSelected.delete(month);
    } else {
      newSelected.add(month);
    }
    setSelectedTargetMonths(newSelected);
  };

  // Convert Set to Array for processing
  const targetWeeksArray = Array.from(selectedTargetWeeks).sort((a, b) => a - b);
  const targetDaysArray = Array.from(selectedTargetDays).sort((a, b) => a - b);
  const targetMonthsArray = Array.from(selectedTargetMonths).sort((a, b) => a - b);

  const dayNames = [
    { value: 1, label: 'Senin' },
    { value: 2, label: 'Selasa' },
    { value: 3, label: 'Rabu' },
    { value: 4, label: 'Kamis' },
    { value: 5, label: 'Jumat' },
    { value: 6, label: 'Sabtu' },
    { value: 7, label: 'Minggu' }
  ];

  const monthNames = [
    { value: 1, label: 'Januari' },
    { value: 2, label: 'Februari' },
    { value: 3, label: 'Maret' },
    { value: 4, label: 'April' },
    { value: 5, label: 'Mei' },
    { value: 6, label: 'Juni' },
    { value: 7, label: 'Juli' },
    { value: 8, label: 'Agustus' },
    { value: 9, label: 'September' },
    { value: 10, label: 'Oktober' },
    { value: 11, label: 'November' },
    { value: 12, label: 'Desember' }
  ];

  // Get preview schedules
  const previewSchedules = useMemo(() => {
    console.log('🔍 DeleteScheduleModal: Computing preview schedules', {
      selectedClassId,
      activeTab,
      targetDay,
      targetDayWeek,
      targetWeeksArray,
      targetMonth
    });

    if (!selectedClassId) {
      console.log('❌ DeleteScheduleModal: No selectedClassId');
      return [];
    }

    let result: any;
    if (activeTab === 'day') {
      if (isMultipleDaysMode) {
        result = getPreviewSchedules(targetDaysArray, targetDayWeek, selectedClassId, 'multiple-days');
        console.log('📅 DeleteScheduleModal Multiple Days Preview:', {
          targetDaysArray,
          targetDayWeek,
          selectedClassId,
          resultCount: result.length,
          result: result.slice(0, 3) // Show first 3 items for debugging
        });
      } else {
        result = getPreviewSchedules(targetDay, targetDayWeek, selectedClassId, 'day');
        console.log('📅 DeleteScheduleModal Day Preview:', {
          targetDay,
          targetDayWeek,
          selectedClassId,
          resultCount: result.length,
          result: result.slice(0, 3) // Show first 3 items for debugging
        });
      }
    } else if (activeTab === 'week') {
      result = getPreviewSchedules(undefined, targetWeeksArray, selectedClassId, 'week');
      console.log('📅 DeleteScheduleModal Week Preview:', {
        targetWeeksArray,
        selectedClassId,
        resultCount: result.length,
        result: result.slice(0, 3) // Show first 3 items for debugging
      });
    } else {
      if (isMultipleMonthsMode) {
        // For multiple months, we need to combine results from all selected months
        result = [];
        for (const month of targetMonthsArray) {
          const monthResult = getPreviewSchedules(undefined, month, selectedClassId, 'month');
          result = [...result, ...monthResult];
        }
        console.log('📅 DeleteScheduleModal Multiple Months Preview:', {
          targetMonthsArray,
          selectedClassId,
          resultCount: result.length,
          result: result.slice(0, 3) // Show first 3 items for debugging
        });
      } else {
        result = getPreviewSchedules(undefined, targetMonth, selectedClassId, 'month');
        console.log('📅 DeleteScheduleModal Month Preview:', {
          targetMonth,
          selectedClassId,
          resultCount: result.length,
          result: result.slice(0, 3) // Show first 3 items for debugging
        });
      }
    }

    console.log('✅ Final preview result:', result.length);

    // 🔍 ENHANCED DEBUG: Log first schedule structure for debugging subject name
    if (result.length > 0) {
      console.log('📋 First schedule structure for subject name debugging:', {
        schedule: result[0],
        subject_name: result[0].subject_name,
        subjects_name: result[0].subjects?.name,
        availableFields: Object.keys(result[0])
      });
    }

    return result;
  }, [activeTab, targetDay, targetDayWeek, targetDaysArray, isMultipleDaysMode, targetWeeksArray, targetMonth, targetMonthsArray, isMultipleMonthsMode, selectedClassId, getPreviewSchedules]);

  const handleDelete = async () => {
    console.log('🗑️ Delete button clicked:', {
      selectedClassId,
      previewSchedulesLength: previewSchedules.length,
      confirmDelete,
      activeTab
    });

    if (!selectedClassId) {
      console.error('❌ No class selected');
      alert('Pilih kelas terlebih dahulu!');
      return;
    }

    if (previewSchedules.length === 0) {
      console.error('❌ No schedules to delete');
      alert('Tidak ada jadwal yang ditemukan untuk dihapus!');
      return;
    }

    if (!confirmDelete) {
      console.error('❌ Please confirm deletion');
      alert('Centang konfirmasi terlebih dahulu!');
      return;
    }

    try {
      console.log('🚀 Starting delete operation...');

      if (activeTab === 'day') {
        if (isMultipleDaysMode) {
          console.log('🗑️ Deleting multiple days schedules:', { days: targetDaysArray, week: targetDayWeek });
          await deleteMultipleDays.mutateAsync({
            days: targetDaysArray,
            week: targetDayWeek,
            classId: selectedClassId
          });
        } else {
          console.log('🗑️ Deleting day schedules:', { day: targetDay, week: targetDayWeek });
          await deleteDay.mutateAsync({
            day: targetDay,
            week: targetDayWeek,
            classId: selectedClassId
          });
        }
      } else if (activeTab === 'week') {
        console.log('🗑️ Deleting week schedules:', { weeks: targetWeeksArray });
        await deleteWeeks.mutateAsync({
          weeks: targetWeeksArray,
          classId: selectedClassId
        });
      } else {
        if (isMultipleMonthsMode) {
          console.log('🗑️ Deleting multiple months schedules:', { months: targetMonthsArray });
          // Delete each month sequentially
          for (const month of targetMonthsArray) {
            await deleteMonth.mutateAsync({
              month: month,
              classId: selectedClassId
            });
          }
        } else {
          console.log('🗑️ Deleting month schedules:', { month: targetMonth });
          await deleteMonth.mutateAsync({
            month: targetMonth,
            classId: selectedClassId
          });
        }
      }

      console.log('✅ Delete operation completed successfully');

      // Call callback to refresh data
      onScheduleDeleted?.();

      // ✅ CLOSE MODAL AND LET CACHE INVALIDATION WORK
      onClose();

    } catch (error) {
      console.error('❌ Delete error:', error);
      alert(`Gagal menghapus jadwal: ${error.message}`);
    }
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] bg-background border-border backdrop-blur-sm">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-bold text-foreground flex items-center gap-3">
            <div className="p-2 rounded-lg bg-red-500/20">
              <Trash2 className="h-6 w-6 text-red-500" />
            </div>
            Hapus Jadwal
          </DialogTitle>
        </DialogHeader>

        <div className="flex gap-6">
          {/* Left Sidebar - Warning */}
          <div className="w-80 space-y-4">
            {/* Delete Warning */}
            <Card className="bg-red-500/10 border-red-500/30">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="font-medium text-red-600 mb-2">
                      Peringatan!
                    </h4>
                    <div className="text-sm text-red-600 space-y-1">
                      <p>Jadwal yang dihapus tidak dapat dikembalikan.</p>
                      <p className="font-medium">
                        {previewSchedules.length} jadwal akan dihapus
                        {activeTab === 'day' && isMultipleDaysMode && targetDaysArray.length > 0 && (
                          <span className="block text-xs">
                            dari {targetDaysArray.length} hari
                          </span>
                        )}
                        {activeTab === 'week' && targetWeeksArray.length > 0 && (
                          <span className="block text-xs">
                            dari {targetWeeksArray.length} pekan
                          </span>
                        )}
                        {activeTab === 'month' && isMultipleMonthsMode && targetMonthsArray.length > 0 && (
                          <span className="block text-xs">
                            dari {targetMonthsArray.length} bulan
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Confirmation */}
            <Card className="bg-card border-border">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="confirm"
                    checked={confirmDelete}
                    onCheckedChange={(checked) => setConfirmDelete(checked === true)}
                  />
                  <label htmlFor="confirm" className="text-sm text-foreground">
                    Saya yakin ingin menghapus jadwal
                  </label>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="space-y-3">
              <Button
                onClick={handleDelete}
                disabled={isLoading || !confirmDelete}
                className="w-full bg-red-500 hover:bg-red-600 text-white"
              >
                {isLoading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Menghapus...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Hapus Jadwal
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={onClose}
                className="w-full border-border text-foreground hover:bg-accent"
              >
                Batal
              </Button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 space-y-6">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'day' | 'week' | 'month')}>
              <TabsList className="grid w-full grid-cols-3 bg-muted">
                <TabsTrigger
                  value="day"
                  className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground"
                >
                  <CalendarDays className="h-4 w-4" />
                  Hapus Hari
                </TabsTrigger>
                <TabsTrigger
                  value="week"
                  className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground"
                >
                  <CalendarRange className="h-4 w-4" />
                  Hapus Pekan
                </TabsTrigger>
                <TabsTrigger
                  value="month"
                  className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground"
                >
                  <CalendarX className="h-4 w-4" />
                  Hapus Bulan
                </TabsTrigger>
              </TabsList>

              <TabsContent value="day" className="space-y-6 mt-6">
                <div className="space-y-6">
                  {/* Target Day */}
                  <Card className="bg-card border-border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-foreground text-sm flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-red-500" />
                        Hari yang Akan Dihapus (Pilih Multiple)
                      </CardTitle>
                      <div className="text-xs text-muted-foreground mt-1">
                        Klik hari untuk memilih/membatalkan. Bisa pilih beberapa hari sekaligus.
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Multiple Days Selection */}
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="multiple-days-mode"
                            checked={isMultipleDaysMode}
                            onCheckedChange={(checked) => setIsMultipleDaysMode(checked === true)}
                          />
                          <label htmlFor="multiple-days-mode" className="text-sm text-foreground">
                            Mode Pilih Multiple Hari
                          </label>
                        </div>

                        {isMultipleDaysMode ? (
                          <>
                            {/* Multiple Days Grid */}
                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-7 gap-2">
                              {dayNames.map(day => (
                                <Button
                                  key={day.value}
                                  variant={selectedTargetDays.has(day.value) ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => handleTargetDayToggle(day.value)}
                                  className={`h-12 text-xs ${
                                    selectedTargetDays.has(day.value)
                                      ? 'bg-red-500 hover:bg-red-600 text-white border-red-500'
                                      : 'border-border text-foreground hover:bg-accent'
                                  }`}
                                >
                                  {day.label}
                                </Button>
                              ))}
                            </div>

                            {/* Selected Days Preview */}
                            {targetDaysArray.length > 0 && (
                              <div className="space-y-2">
                                <label className="text-sm font-medium text-foreground">
                                  Hari Terpilih ({targetDaysArray.length} hari):
                                </label>
                                <div className="flex flex-wrap gap-2">
                                  {targetDaysArray.map(day => (
                                    <Badge
                                      key={day}
                                      variant="secondary"
                                      className="bg-red-500/20 text-red-600 border-red-500/30 cursor-pointer hover:bg-red-500/30"
                                      onClick={() => handleTargetDayToggle(day)}
                                    >
                                      {dayNames.find(d => d.value === day)?.label} ✕
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                          </>
                        ) : (
                          /* Single Day Selection */
                          <div>
                            <label className="text-sm font-medium text-foreground mb-2 block">Hari</label>
                            <Select value={targetDay.toString()} onValueChange={(value) => setTargetDay(parseInt(value))}>
                              <SelectTrigger className="bg-background border-border text-foreground">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent className="bg-popover border-border">
                                {dayNames.map(day => (
                                  <SelectItem key={day.value} value={day.value.toString()}>
                                    {day.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>

                      {/* Week Selection */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-foreground">Pekan</label>
                        <div className="text-center mb-3">
                          <Badge variant="secondary" className="bg-red-500/20 text-red-600 border-red-500/30">
                            Pekan {targetDayWeek}
                          </Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            {academicWeeks.find(w => w.weekNumber === targetDayWeek)?.dateRange || 'Tanggal tidak tersedia'}
                          </div>
                        </div>
                        <div className="max-h-40 overflow-y-auto border border-border rounded-lg p-3 bg-muted/30 custom-scrollbar">
                          <ModalWeekNavigation
                            selectedWeek={targetDayWeek}
                            onWeekSelect={setTargetDayWeek}
                            isSelectionMode={false}
                            variant="delete"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="week" className="space-y-6 mt-6">
                <div className="space-y-6">
                  {/* Target Weeks with Navigation */}
                  <Card className="bg-card border-border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-foreground text-sm flex items-center gap-2">
                        <Trash2 className="h-4 w-4 text-red-500" />
                        Pekan yang Akan Dihapus (Pilih Multiple)
                      </CardTitle>
                      <div className="text-xs text-muted-foreground mt-1">
                        Klik angka pekan untuk memilih/membatalkan. Bisa pilih beberapa pekan sekaligus.
                      </div>
                    </CardHeader>
                    <CardContent>
                      {/* Custom Week Navigation */}
                      <div className="space-y-4">
                        <div className="max-h-48 overflow-y-auto border border-border rounded-lg p-3 bg-muted/30 custom-scrollbar">
                          <ModalWeekNavigation
                            selectedWeek={0}
                            onWeekSelect={handleTargetWeekToggle}
                            selectedTargetWeeks={selectedTargetWeeks}
                            sourceWeek={0}
                            isSelectionMode={true}
                            variant="delete"
                          />
                        </div>

                        {/* Selected Weeks Preview */}
                        {targetWeeksArray.length > 0 && (
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-foreground">
                              Pekan Terpilih ({targetWeeksArray.length} pekan):
                            </label>
                            <div className="flex flex-wrap gap-2">
                              {targetWeeksArray.map(week => (
                                <Badge
                                  key={week}
                                  variant="secondary"
                                  className="bg-red-500/20 text-red-600 border-red-500/30 cursor-pointer hover:bg-red-500/30"
                                  onClick={() => handleTargetWeekToggle(week)}
                                >
                                  Pekan {week} ✕
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="month" className="space-y-6 mt-6">
                <div className="space-y-6">
                  {/* Target Months */}
                  <Card className="bg-card border-border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-foreground text-sm flex items-center gap-2">
                        <CalendarX className="h-4 w-4 text-red-500" />
                        Bulan yang Akan Dihapus (Pilih Multiple)
                      </CardTitle>
                      <div className="text-xs text-muted-foreground mt-1">
                        Klik bulan untuk memilih/membatalkan. Bisa pilih beberapa bulan sekaligus.
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Multiple Months Selection */}
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="multiple-months-mode"
                            checked={isMultipleMonthsMode}
                            onCheckedChange={(checked) => setIsMultipleMonthsMode(checked === true)}
                          />
                          <label htmlFor="multiple-months-mode" className="text-sm text-foreground">
                            Mode Pilih Multiple Bulan
                          </label>
                        </div>

                        {isMultipleMonthsMode ? (
                          <>
                            {/* Multiple Months Grid */}
                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
                              {monthNames.map(month => (
                                <Button
                                  key={month.value}
                                  variant={selectedTargetMonths.has(month.value) ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => handleTargetMonthToggle(month.value)}
                                  className={`h-12 text-xs ${
                                    selectedTargetMonths.has(month.value)
                                      ? 'bg-red-500 hover:bg-red-600 text-white border-red-500'
                                      : 'border-border text-foreground hover:bg-accent'
                                  }`}
                                >
                                  {month.label}
                                </Button>
                              ))}
                            </div>

                            {/* Selected Months Preview */}
                            {targetMonthsArray.length > 0 && (
                              <div className="space-y-2">
                                <label className="text-sm font-medium text-foreground">
                                  Bulan Terpilih ({targetMonthsArray.length} bulan):
                                </label>
                                <div className="flex flex-wrap gap-2">
                                  {targetMonthsArray.map(month => (
                                    <Badge
                                      key={month}
                                      variant="secondary"
                                      className="bg-red-500/20 text-red-600 border-red-500/30 cursor-pointer hover:bg-red-500/30"
                                      onClick={() => handleTargetMonthToggle(month)}
                                    >
                                      {monthNames.find(m => m.value === month)?.label} ✕
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                          </>
                        ) : (
                          /* Single Month Selection */
                          <div>
                            <label className="text-sm font-medium text-foreground mb-2 block">Bulan</label>
                            <Select value={targetMonth.toString()} onValueChange={(value) => setTargetMonth(parseInt(value))}>
                              <SelectTrigger className="bg-background border-border text-foreground">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent className="bg-popover border-border">
                                {monthNames.map(month => (
                                  <SelectItem key={month.value} value={month.value.toString()}>
                                    {month.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Month Info */}
                  <Card className="bg-card border-border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-foreground text-sm flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        Informasi
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm text-foreground space-y-2">
                        {isMultipleMonthsMode && targetMonthsArray.length > 0 ? (
                          <>
                            <p>Semua jadwal pada bulan berikut akan dihapus:</p>
                            <div className="flex flex-wrap gap-1">
                              {targetMonthsArray.map(month => (
                                <strong key={month} className="text-red-600">
                                  {monthNames.find(m => m.value === month)?.label}
                                  {month !== targetMonthsArray[targetMonthsArray.length - 1] && ', '}
                                </strong>
                              ))}
                            </div>
                            <p className="text-red-600 font-medium">
                              Aksi ini akan menghapus seluruh jadwal dalam {targetMonthsArray.length} bulan!
                            </p>
                          </>
                        ) : (
                          <>
                            <p>Semua jadwal pada bulan <strong className="text-red-600">{monthNames.find(m => m.value === targetMonth)?.label}</strong> akan dihapus.</p>
                            <p className="text-red-600 font-medium">Aksi ini akan menghapus seluruh jadwal dalam 1 bulan!</p>
                          </>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>

            {/* Preview Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-foreground">Preview Jadwal yang Akan Dihapus</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-red-500/20 text-red-600">
                    {previewSchedules.length} jadwal
                  </Badge>
                  {activeTab === 'day' && isMultipleDaysMode && targetDaysArray.length > 0 && (
                    <Badge variant="secondary" className="bg-red-500/20 text-red-600">
                      dari {targetDaysArray.length} hari
                    </Badge>
                  )}
                  {activeTab === 'week' && targetWeeksArray.length > 0 && (
                    <Badge variant="secondary" className="bg-red-500/20 text-red-600">
                      dari {targetWeeksArray.length} pekan
                    </Badge>
                  )}
                  {activeTab === 'month' && isMultipleMonthsMode && targetMonthsArray.length > 0 && (
                    <Badge variant="secondary" className="bg-red-500/20 text-red-600">
                      dari {targetMonthsArray.length} bulan
                    </Badge>
                  )}
                </div>
              </div>

              {previewSchedules.length > 0 ? (
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {previewSchedules.map((schedule, index) => (
                    <div
                      key={schedule.id || index}
                      className="flex items-center justify-between p-3 bg-red-500/10 rounded-lg border-l-4 border-red-500"
                    >
                      <div className="flex-1">
                        <div className="font-medium text-foreground text-sm">
                          {/* 🚀 FIXED: Use correct field for subject name from schedules_view */}
                          {schedule.subject_name || schedule.subjects?.name || 'Mata Pelajaran'}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {dayNames.find(d => d.value === schedule.day_of_week)?.label} •
                          {schedule.start_time}-{schedule.end_time}
                          {schedule.room && ` • ${schedule.room}`}
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs border-red-500/30 text-red-600">
                        Pekan {schedule.academic_week}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Tidak ada jadwal untuk dihapus</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
