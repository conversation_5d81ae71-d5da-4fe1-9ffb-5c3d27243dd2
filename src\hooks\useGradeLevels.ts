
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface GradeLevel {
  id: string;
  education_level_id: string;
  name: string;
  grade_number: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const useGradeLevels = (educationLevelId?: string) => {
  return useQuery({
    queryKey: ['grade-levels', educationLevelId],
    queryFn: async () => {
      let query = supabase
        .from('grade_levels')
        .select('*')
        .eq('is_active', true)
        .order('grade_number');

      if (educationLevelId) {
        query = query.eq('education_level_id', educationLevelId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      return data as GradeLevel[];
    },
    enabled: !educationLevelId || !!educationLevelId,
  });
};

export const useCreateGradeLevel = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: Omit<GradeLevel, 'id' | 'created_at' | 'updated_at'>) => {
      const { data: result, error } = await supabase
        .from('grade_levels')
        .insert(data)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grade-levels'] });
      toast({
        title: "Berhasil",
        description: "Tingkat berhasil ditambahkan",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal menambahkan tingkat",
        variant: "destructive",
      });
      console.error('Error creating grade level:', error);
    },
  });
};

export const useUpdateGradeLevel = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...data }: Partial<GradeLevel> & { id: string }) => {
      const { data: result, error } = await supabase
        .from('grade_levels')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grade-levels'] });
      toast({
        title: "Berhasil",
        description: "Tingkat berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal memperbarui tingkat",
        variant: "destructive",
      });
      console.error('Error updating grade level:', error);
    },
  });
};

export const useDeleteGradeLevel = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('grade_levels')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['grade-levels'] });
      toast({
        title: "Berhasil",
        description: "Tingkat berhasil dihapus",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal menghapus tingkat",
        variant: "destructive",
      });
      console.error('Error deleting grade level:', error);
    },
  });
};
