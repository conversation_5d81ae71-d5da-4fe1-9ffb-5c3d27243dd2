import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { hasTimeConflict } from '@/utils/timeUtils';
import { useScheduleRealTime } from '@/hooks/useRealTimeSync';

export interface CopyDayParams {
  sourceDay: number; // 1=Senin, 2=Selasa, dst
  targetDay: number;
  sourceWeek: number;
  targetWeek: number;
  classId: string;
  overwriteConflicts?: boolean;
}

export interface CopyMultipleDaysParams {
  sourceDay: number;
  targetDays: number[]; // Array of target days
  sourceWeek: number;
  targetWeek: number;
  classId: string;
  overwriteConflicts?: boolean;
}

export interface CopyWeekParams {
  sourceWeek: number;
  targetWeek: number;
  classId: string;
  overwriteConflicts?: boolean;
}

export interface CopyMultipleWeeksParams {
  sourceWeek: number;
  targetWeeks: number[]; // Array of target weeks
  classId: string;
  overwriteConflicts?: boolean;
}

export interface ScheduleConflict {
  existingSchedule: any;
  newSchedule: any;
  conflictTime: string;
}

export const useCopySchedule = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { forceRefresh } = useScheduleRealTime();

  // ✅ FIXED: Use SAME data source as main calendar (schedules_view)
  const { data: schedules = [], isLoading: schedulesLoading, error: schedulesError } = useQuery({
    queryKey: ['schedules-for-copy'], // Use same key as main calendar
    queryFn: async () => {
      console.log('🔄 Fetching schedules for copy operation from schedules_view (SAME AS CALENDAR)...');
      console.log('🕐 Query timestamp:', new Date().toISOString());

      // ✅ FIXED: Use SAME query as main calendar (schedules_view)
      let allData: any[] = [];
      let from = 0;
      const batchSize = 500;
      let hasMore = true;
      let totalFetched = 0;

      while (hasMore) {
        console.log(`📦 Copy batch: ${from} to ${from + batchSize - 1} (Progress: ${totalFetched} rows)`);

        const { data: batchData, error: batchError } = await supabase
          .from('schedules_view') // ✅ SAME TABLE AS CALENDAR
          .select('*')
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true })
          .range(from, from + batchSize - 1);

        if (batchError) {
          console.error('❌ Error fetching copy schedules batch:', batchError);
          throw batchError;
        }

        if (!batchData || batchData.length === 0) {
          hasMore = false;
          break;
        }

        allData = [...allData, ...batchData];
        totalFetched += batchData.length;

        if (batchData.length < batchSize) {
          hasMore = false;
        } else {
          from += batchSize;
        }

        // Safety limit
        if (totalFetched >= 50000) {
          console.log('⚠️ Reached safety limit of 50k records for copy');
          hasMore = false;
        }
      }

      console.log('✅ Schedules for copy fetched:', allData.length, 'records');
      console.log('📊 Raw data sample:', allData.slice(0, 3));

      // Debug specific data for the class we're testing
      const testClassData = allData.filter(s => s.class_id === '556ea61c-8dad-4441-8001-a70f3d448ffa');
      const selasaWeek25 = testClassData.filter(s => s.academic_week === 25 && s.day_of_week === 2);

      console.log('🔍 Test class data (FROM SCHEDULES_VIEW):', {
        classId: '556ea61c-8dad-4441-8001-a70f3d448ffa',
        totalForClass: testClassData.length,
        week25Data: testClassData.filter(s => s.academic_week === 25),
        selasaWeek25Count: selasaWeek25.length,
        selasaWeek25Detail: selasaWeek25.map(s => ({
          id: s.id,
          subject_name: s.subject_name,
          start_time: s.start_time,
          end_time: s.end_time,
          class_name: s.class_name
        })),
        allWeeks: [...new Set(testClassData.map(s => s.academic_week))].sort(),
        sampleData: testClassData.slice(0, 5).map(s => ({
          id: s.id,
          day: s.day_of_week,
          week: s.academic_week,
          subject_name: s.subject_name,
          time: `${s.start_time}-${s.end_time}`
        }))
      });

      return allData;
    },
    staleTime: 0, // Always consider data stale (same as calendar)
    gcTime: 0, // Don't cache (same as calendar)
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    retry: 3,
    enabled: true,
    refetchInterval: false,
    refetchOnReconnect: true
  });

  // Debug logging for schedules data
  console.log('🔍 useCopySchedule - Schedules data:', {
    totalSchedules: schedules.length,
    isLoading: schedulesLoading,
    hasError: !!schedulesError,
    sampleSchedule: schedules[0]
  });

  // Helper function to get user's school info
  const getUserSchoolInfo = async () => {
    const { data: profile } = await supabase
      .from('profiles')
      .select('school_id')
      .eq('id', (await supabase.auth.getUser()).data.user?.id)
      .single();

    const { data: activeYear } = await supabase
      .from('academic_years')
      .select('id')
      .eq('school_id', profile?.school_id)
      .eq('is_active', true)
      .single();

    return { schoolId: profile?.school_id, academicYearId: activeYear?.id };
  };

  // Check for conflicts before copying
  const checkCopyConflicts = (
    sourceSchedules: any[],
    targetDay: number,
    targetWeek: number,
    classId: string
  ): ScheduleConflict[] => {
    const conflicts: ScheduleConflict[] = [];
    const existingTargetSchedules = schedules.filter(schedule => 
      schedule.class_id === classId &&
      schedule.day_of_week === targetDay &&
      schedule.academic_week === targetWeek
    );

    sourceSchedules.forEach(sourceSchedule => {
      const conflictData = {
        class_id: classId,
        day_of_week: targetDay,
        start_time: sourceSchedule.start_time,
        end_time: sourceSchedule.end_time,
        academic_week: targetWeek
      };

      const conflictingSchedule = existingTargetSchedules.find(existing => 
        hasTimeConflict([existing], conflictData)
      );

      if (conflictingSchedule) {
        conflicts.push({
          existingSchedule: conflictingSchedule,
          newSchedule: sourceSchedule,
          conflictTime: `${sourceSchedule.start_time}-${sourceSchedule.end_time}`
        });
      }
    });

    return conflicts;
  };

  // Copy schedules from one day to another
  const copyDay = useMutation({
    mutationFn: async (params: CopyDayParams) => {
      console.log('📋 Copy Day Operation:', params);

      const { schoolId, academicYearId } = await getUserSchoolInfo();

      // Get source schedules
      const sourceSchedules = schedules.filter(schedule => 
        schedule.class_id === params.classId &&
        schedule.day_of_week === params.sourceDay &&
        schedule.academic_week === params.sourceWeek
      );

      if (sourceSchedules.length === 0) {
        throw new Error('Tidak ada jadwal untuk disalin dari hari yang dipilih');
      }

      console.log(`📅 Found ${sourceSchedules.length} schedules to copy`);

      // Check for conflicts
      const conflicts = checkCopyConflicts(
        sourceSchedules,
        params.targetDay,
        params.targetWeek,
        params.classId
      );

      if (conflicts.length > 0 && !params.overwriteConflicts) {
        throw new Error(`Ditemukan ${conflicts.length} konflik jadwal. Aktifkan "Timpa Konflik" untuk melanjutkan.`);
      }

      // Delete existing schedules if overwriting conflicts
      if (params.overwriteConflicts && conflicts.length > 0) {
        const conflictIds = conflicts.map(c => c.existingSchedule.id);
        const { error: deleteError } = await supabase
          .from('class_schedules')  // ✅ FIXED: Use correct table
          .delete()
          .in('id', conflictIds);

        if (deleteError) {
          console.error('Error deleting conflicting schedules:', deleteError);
          throw deleteError;
        }

        console.log(`🗑️ Deleted ${conflictIds.length} conflicting schedules`);
      }

      // Prepare new schedules with all required fields
      const newSchedules = sourceSchedules.map(schedule => ({
        subject_id: schedule.subject_id,
        teacher_id: schedule.teacher_id,
        class_id: params.classId,
        time_session_id: schedule.time_session_id,
        day_of_week: params.targetDay,
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        academic_week: params.targetWeek,
        schedule_date: null, // Will be calculated by backend
        room: schedule.room,
        notes: schedule.notes,
        tujuan_pembelajaran: schedule.tujuan_pembelajaran || null,
        materi_pembelajaran: schedule.materi_pembelajaran || null,
        school_id: schoolId,
        academic_year_id: academicYearId,
      }));

      console.log('📋 Prepared schedules for copy:', {
        sourceCount: sourceSchedules.length,
        targetDay: params.targetDay,
        targetWeek: params.targetWeek,
        classId: params.classId,
        newSchedules: newSchedules.length
      });

      // Insert new schedules - USE CLASS_SCHEDULES TABLE (ACTUAL TABLE)
      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .insert(newSchedules)
        .select();

      if (error) {
        console.error('Error copying schedules:', error);
        throw error;
      }

      console.log(`✅ Successfully copied ${data.length} schedules`);
      return { copiedCount: data.length, conflicts: conflicts.length };
    },
    onSuccess: async (result) => {
      console.log('✅ Copy day operation successful');

      // ✅ AGGRESSIVE CACHE INVALIDATION: Force immediate UI updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedules'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-for-copy'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
        queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
      ]);

      // Force immediate refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['schedules'] }),
        queryClient.refetchQueries({ queryKey: ['schedules-complete'] })
      ]);

      // ✅ FORCE UI REFRESH: Trigger immediate UI update with longer delay for day copy
      setTimeout(() => {
        forceRefresh();
        console.log('🚀 Force refresh triggered after copy day operation');

        // ✅ ADDITIONAL: Force calendar refetch for day copy
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('forceCalendarRefresh'));
          console.log('📅 Calendar refetch event dispatched for day copy');
        }, 100);
      }, 500); // Longer delay for day copy

      toast({
        title: "✅ Berhasil",
        description: `${result.copiedCount} jadwal berhasil disalin${result.conflicts > 0 ? ` (${result.conflicts} konflik ditimpa)` : ''}`,
      });
    },
    onError: (error: any) => {
      console.error('Copy day error:', error);
      toast({
        title: "❌ Gagal",
        description: error.message || "Gagal menyalin jadwal",
        variant: "destructive",
      });
    },
  });

  // Copy schedules from one week to another
  const copyWeek = useMutation({
    mutationFn: async (params: CopyWeekParams) => {
      console.log('📋 Copy Week Operation:', params);

      const { schoolId, academicYearId } = await getUserSchoolInfo();

      // Get all source schedules for the week
      const sourceSchedules = schedules.filter(schedule => 
        schedule.class_id === params.classId &&
        schedule.academic_week === params.sourceWeek
      );

      if (sourceSchedules.length === 0) {
        throw new Error('Tidak ada jadwal untuk disalin dari pekan yang dipilih');
      }

      console.log(`📅 Found ${sourceSchedules.length} schedules to copy from week ${params.sourceWeek}`);

      // Group by day and check conflicts
      const schedulesByDay = sourceSchedules.reduce((acc, schedule) => {
        const day = schedule.day_of_week;
        if (!acc[day]) acc[day] = [];
        acc[day].push(schedule);
        return acc;
      }, {} as Record<number, any[]>);

      let totalConflicts = 0;
      const conflictsByDay: Record<number, ScheduleConflict[]> = {};

      Object.entries(schedulesByDay).forEach(([day, daySchedules]) => {
        const dayConflicts = checkCopyConflicts(
          daySchedules,
          parseInt(day),
          params.targetWeek,
          params.classId
        );
        conflictsByDay[parseInt(day)] = dayConflicts;
        totalConflicts += dayConflicts.length;
      });

      if (totalConflicts > 0 && !params.overwriteConflicts) {
        throw new Error(`Ditemukan ${totalConflicts} konflik jadwal. Aktifkan "Timpa Konflik" untuk melanjutkan.`);
      }

      // Delete existing schedules if overwriting conflicts
      if (params.overwriteConflicts && totalConflicts > 0) {
        const allConflictIds = Object.values(conflictsByDay)
          .flat()
          .map(c => c.existingSchedule.id);

        const { error: deleteError } = await supabase
          .from('class_schedules')  // ✅ FIXED: Use correct table
          .delete()
          .in('id', allConflictIds);

        if (deleteError) {
          console.error('Error deleting conflicting schedules:', deleteError);
          throw deleteError;
        }

        console.log(`🗑️ Deleted ${allConflictIds.length} conflicting schedules`);
      }

      // Prepare new schedules with all required fields
      const newSchedules = sourceSchedules.map(schedule => ({
        subject_id: schedule.subject_id,
        teacher_id: schedule.teacher_id,
        class_id: params.classId,
        time_session_id: schedule.time_session_id,
        day_of_week: schedule.day_of_week, // Keep same day
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        academic_week: params.targetWeek, // Change to target week
        schedule_date: null, // Will be calculated by backend
        room: schedule.room,
        notes: schedule.notes,
        tujuan_pembelajaran: schedule.tujuan_pembelajaran || null,
        materi_pembelajaran: schedule.materi_pembelajaran || null,
        school_id: schoolId,
        academic_year_id: academicYearId,
      }));

      console.log('📋 Prepared week schedules for copy:', {
        sourceCount: sourceSchedules.length,
        targetWeek: params.targetWeek,
        classId: params.classId,
        newSchedules: newSchedules.length
      });

      // Insert new schedules - USE CLASS_SCHEDULES TABLE (ACTUAL TABLE)
      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .insert(newSchedules)
        .select();

      if (error) {
        console.error('Error copying week schedules:', error);
        throw error;
      }

      console.log(`✅ Successfully copied ${data.length} schedules for week`);
      return { copiedCount: data.length, conflicts: totalConflicts };
    },
    onSuccess: async (result) => {
      console.log('✅ Copy week operation successful');

      // ✅ AGGRESSIVE CACHE INVALIDATION: Force immediate UI updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedules'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-for-copy'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
        queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
      ]);

      // Force immediate refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['schedules'] }),
        queryClient.refetchQueries({ queryKey: ['schedules-complete'] })
      ]);

      // ✅ FORCE UI REFRESH: Trigger immediate UI update
      setTimeout(() => {
        forceRefresh();
        console.log('🚀 Force refresh triggered after copy week operation');
      }, 200);

      toast({
        title: "✅ Berhasil",
        description: `${result.copiedCount} jadwal berhasil disalin${result.conflicts > 0 ? ` (${result.conflicts} konflik ditimpa)` : ''}`,
      });
    },
    onError: (error: any) => {
      console.error('Copy week error:', error);
      toast({
        title: "❌ Gagal",
        description: error.message || "Gagal menyalin jadwal pekan",
        variant: "destructive",
      });
    },
  });

  // Copy schedules from one week to multiple weeks
  const copyMultipleWeeks = useMutation({
    mutationFn: async (params: CopyMultipleWeeksParams) => {
      console.log('📋 Copy Multiple Weeks Operation:', params);

      const { schoolId, academicYearId } = await getUserSchoolInfo();

      // Get all source schedules for the week
      const sourceSchedules = schedules.filter(schedule =>
        schedule.class_id === params.classId &&
        schedule.academic_week === params.sourceWeek
      );

      if (sourceSchedules.length === 0) {
        throw new Error('Tidak ada jadwal untuk disalin dari pekan yang dipilih');
      }

      console.log(`📅 Found ${sourceSchedules.length} schedules to copy from week ${params.sourceWeek} to ${params.targetWeeks.length} target weeks`);

      let totalConflicts = 0;
      const conflictsByWeek: Record<number, Record<number, ScheduleConflict[]>> = {};
      const allNewSchedules: any[] = [];

      // Check conflicts for each target week
      for (const targetWeek of params.targetWeeks) {
        conflictsByWeek[targetWeek] = {};

        // Group by day and check conflicts
        const schedulesByDay = sourceSchedules.reduce((acc, schedule) => {
          const day = schedule.day_of_week;
          if (!acc[day]) acc[day] = [];
          acc[day].push(schedule);
          return acc;
        }, {} as Record<number, any[]>);

        Object.entries(schedulesByDay).forEach(([day, daySchedules]) => {
          const dayConflicts = checkCopyConflicts(
            daySchedules,
            parseInt(day),
            targetWeek,
            params.classId
          );
          conflictsByWeek[targetWeek][parseInt(day)] = dayConflicts;
          totalConflicts += dayConflicts.length;
        });

        // Prepare new schedules for this target week
        const weekSchedules = sourceSchedules.map(schedule => ({
          subject_id: schedule.subject_id,
          teacher_id: schedule.teacher_id,
          class_id: params.classId,
          time_session_id: schedule.time_session_id,
          day_of_week: schedule.day_of_week,
          start_time: schedule.start_time,
          end_time: schedule.end_time,
          academic_week: targetWeek, // Different target week
          schedule_date: null,
          room: schedule.room,
          notes: schedule.notes,
          tujuan_pembelajaran: schedule.tujuan_pembelajaran || null,
          materi_pembelajaran: schedule.materi_pembelajaran || null,
          school_id: schoolId,
          academic_year_id: academicYearId,
        }));

        allNewSchedules.push(...weekSchedules);
      }

      if (totalConflicts > 0 && !params.overwriteConflicts) {
        throw new Error(`Ditemukan ${totalConflicts} konflik jadwal di ${params.targetWeeks.length} pekan. Aktifkan "Timpa Konflik" untuk melanjutkan.`);
      }

      // Delete existing schedules if overwriting conflicts
      if (params.overwriteConflicts && totalConflicts > 0) {
        const allConflictIds: string[] = [];

        Object.values(conflictsByWeek).forEach(weekConflicts => {
          Object.values(weekConflicts).forEach(dayConflicts => {
            dayConflicts.forEach(conflict => {
              allConflictIds.push(conflict.existingSchedule.id);
            });
          });
        });

        if (allConflictIds.length > 0) {
          const { error: deleteError } = await supabase
            .from('class_schedules')  // ✅ FIXED: Use correct table
            .delete()
            .in('id', allConflictIds);

          if (deleteError) {
            console.error('Error deleting conflicting schedules:', deleteError);
            throw deleteError;
          }

          console.log(`🗑️ Deleted ${allConflictIds.length} conflicting schedules across ${params.targetWeeks.length} weeks`);
        }
      }

      // Insert all new schedules in batches for better performance
      const batchSize = 100;
      let totalInserted = 0;

      for (let i = 0; i < allNewSchedules.length; i += batchSize) {
        const batch = allNewSchedules.slice(i, i + batchSize);

        const { data, error } = await supabase
          .from('class_schedules')  // ✅ FIXED: Use actual table that exists
          .insert(batch)
          .select();

        if (error) {
          console.error('Error copying schedules batch:', error);
          throw error;
        }

        totalInserted += data.length;
        console.log(`✅ Inserted batch ${Math.floor(i/batchSize) + 1}: ${data.length} schedules`);
      }

      console.log(`✅ Successfully copied ${totalInserted} schedules to ${params.targetWeeks.length} weeks`);
      return {
        copiedCount: totalInserted,
        conflicts: totalConflicts,
        targetWeeksCount: params.targetWeeks.length,
        schedulesPerWeek: sourceSchedules.length
      };
    },
    onSuccess: async (result) => {
      console.log('✅ Copy multiple weeks operation successful');

      // ✅ AGGRESSIVE CACHE INVALIDATION: Force immediate UI updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedules'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-for-copy'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
        queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
      ]);

      // Force immediate refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['schedules'] }),
        queryClient.refetchQueries({ queryKey: ['schedules-complete'] })
      ]);

      // ✅ FORCE UI REFRESH: Trigger immediate UI update
      setTimeout(() => {
        forceRefresh();
        console.log('🚀 Force refresh triggered after copy multiple weeks operation');
      }, 200);

      toast({
        title: "✅ Berhasil",
        description: `${result.copiedCount} jadwal berhasil disalin ke ${result.targetWeeksCount} pekan${result.conflicts > 0 ? ` (${result.conflicts} konflik ditimpa)` : ''}`,
      });
    },
    onError: (error: any) => {
      console.error('Copy multiple weeks error:', error);
      toast({
        title: "❌ Gagal",
        description: error.message || "Gagal menyalin jadwal ke beberapa pekan",
        variant: "destructive",
      });
    },
  });

  // Get preview of schedules to be copied
  const getPreviewSchedules = (
    sourceDay?: number,
    sourceWeek?: number,
    classId?: string,
    isWeekCopy = false
  ) => {
    console.log('🔍 getPreviewSchedules called with:', {
      sourceDay,
      sourceWeek,
      classId,
      isWeekCopy,
      totalSchedules: schedules.length,
      schedulesLoading,
      schedulesError: schedulesError?.message,
      timestamp: new Date().toISOString()
    });

    // Log first 10 schedules to see what we have
    console.log('📋 First 10 schedules in memory:', schedules.slice(0, 10).map(s => ({
      id: s.id,
      class_id: s.class_id,
      day_of_week: s.day_of_week,
      academic_week: s.academic_week,
      subject_id: s.subject_id,
      start_time: s.start_time
    })));

    // Specifically look for week 25 data
    const week25Data = schedules.filter(s => s.academic_week === 25);
    console.log('🎯 Week 25 data in memory:', {
      count: week25Data.length,
      data: week25Data.map(s => ({
        id: s.id,
        class_id: s.class_id,
        day_of_week: s.day_of_week,
        subject_id: s.subject_id
      }))
    });

    if (!classId || !sourceWeek) {
      console.log('❌ Missing classId or sourceWeek');
      return [];
    }

    if (schedulesLoading) {
      console.log('⏳ Schedules still loading...');
      return [];
    }

    if (schedulesError) {
      console.log('❌ Schedules error:', schedulesError);
      return [];
    }

    let result;
    if (isWeekCopy) {
      result = schedules.filter(schedule =>
        schedule.class_id === classId &&
        schedule.academic_week === sourceWeek
      );
      console.log('📅 Week copy filter result:', {
        classId,
        sourceWeek,
        resultCount: result.length,
        matchingSchedules: result.map(s => ({ day: s.day_of_week, subject_id: s.subject_id }))
      });
    } else if (sourceDay) {
      // Debug the filtering process step by step
      const classMatches = schedules.filter(s => s.class_id === classId);
      const dayMatches = classMatches.filter(s => s.day_of_week === sourceDay);
      const weekMatches = dayMatches.filter(s => s.academic_week === sourceWeek);

      console.log('📅 Day copy filter debug:', {
        classId,
        sourceDay,
        sourceWeek,
        totalSchedules: schedules.length,
        classMatches: classMatches.length,
        dayMatches: dayMatches.length,
        weekMatches: weekMatches.length,
        classMatchesSample: classMatches.slice(0, 3).map(s => ({
          day: s.day_of_week,
          week: s.academic_week,
          subject_id: s.subject_id
        })),
        dayMatchesSample: dayMatches.slice(0, 3).map(s => ({
          week: s.academic_week,
          subject_id: s.subject_id
        }))
      });

      result = weekMatches;
      console.log('📅 Day copy filter result:', {
        classId,
        sourceDay,
        sourceWeek,
        resultCount: result.length,
        matchingSchedules: result.map(s => ({ subject_id: s.subject_id, time: `${s.start_time}-${s.end_time}` }))
      });
    } else {
      result = [];
    }

    return result;
  };

  // Copy schedules from one day to multiple days
  const copyMultipleDays = useMutation({
    mutationFn: async (params: CopyMultipleDaysParams) => {
      console.log('📋 Copy Multiple Days Operation:', params);

      const { schoolId, academicYearId } = await getUserSchoolInfo();

      // Get source schedules
      const sourceSchedules = schedules.filter(schedule =>
        schedule.class_id === params.classId &&
        schedule.day_of_week === params.sourceDay &&
        schedule.academic_week === params.sourceWeek
      );

      if (sourceSchedules.length === 0) {
        throw new Error('Tidak ada jadwal untuk disalin dari hari yang dipilih');
      }

      console.log(`📅 Found ${sourceSchedules.length} schedules to copy to ${params.targetDays.length} days`);

      // Check for conflicts across all target days
      let totalConflicts = 0;
      const conflictsByDay: Record<number, any[]> = {};

      params.targetDays.forEach(targetDay => {
        const conflicts = checkCopyConflicts(
          sourceSchedules,
          targetDay,
          params.targetWeek,
          params.classId
        );

        if (conflicts.length > 0) {
          conflictsByDay[targetDay] = conflicts;
          totalConflicts += conflicts.length;
        }
      });

      if (totalConflicts > 0 && !params.overwriteConflicts) {
        throw new Error(`Ditemukan ${totalConflicts} konflik jadwal di ${params.targetDays.length} hari. Aktifkan "Timpa Konflik" untuk melanjutkan.`);
      }

      // Delete existing schedules if overwriting conflicts
      if (params.overwriteConflicts && totalConflicts > 0) {
        const allConflictIds: string[] = [];

        Object.values(conflictsByDay).forEach(dayConflicts => {
          dayConflicts.forEach(conflict => {
            allConflictIds.push(conflict.existingSchedule.id);
          });
        });

        if (allConflictIds.length > 0) {
          const { error: deleteError } = await supabase
            .from('class_schedules')  // ✅ FIXED: Use correct table
            .delete()
            .in('id', allConflictIds);

          if (deleteError) {
            console.error('Error deleting conflicting schedules:', deleteError);
            throw deleteError;
          }

          console.log(`🗑️ Deleted ${allConflictIds.length} conflicting schedules`);
        }
      }

      // Prepare new schedules for all target days
      const allNewSchedules: any[] = [];

      params.targetDays.forEach(targetDay => {
        const daySchedules = sourceSchedules.map(schedule => ({
          subject_id: schedule.subject_id,
          teacher_id: schedule.teacher_id || null,
          class_id: params.classId,
          time_session_id: schedule.time_session_id || null,
          day_of_week: targetDay,
          start_time: schedule.start_time,
          end_time: schedule.end_time,
          academic_week: params.targetWeek,
          schedule_date: null, // Will be calculated by backend
          room: schedule.room || '',
          notes: schedule.notes || '',
          tujuan_pembelajaran: schedule.tujuan_pembelajaran || null,
          materi_pembelajaran: schedule.materi_pembelajaran || null,
          school_id: schoolId,
          academic_year_id: academicYearId,
          hours_per_week: 0,
          hours_per_year: 0,
        }));

        allNewSchedules.push(...daySchedules);
      });

      console.log(`📅 Prepared ${allNewSchedules.length} schedules for ${params.targetDays.length} days`, {
        sourceSchedules: sourceSchedules.length,
        targetDays: params.targetDays,
        targetWeek: params.targetWeek,
        classId: params.classId,
        sampleSchedule: allNewSchedules[0]
      });

      // Insert new schedules - USE CLASS_SCHEDULES TABLE (ACTUAL TABLE)
      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .insert(allNewSchedules)
        .select();

      if (error) {
        console.error('Error copying schedules to multiple days:', error);
        throw error;
      }

      console.log(`✅ Successfully copied ${data.length} schedules to ${params.targetDays.length} days`);
      return {
        copiedCount: data.length,
        conflicts: totalConflicts,
        targetDaysCount: params.targetDays.length,
        schedulesPerDay: sourceSchedules.length
      };
    },
    onSuccess: async (result) => {
      console.log('✅ Copy multiple days operation successful');

      // ✅ AGGRESSIVE CACHE INVALIDATION: Force immediate UI updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedules'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-for-copy'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
        queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
      ]);

      // Force immediate refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['schedules'] }),
        queryClient.refetchQueries({ queryKey: ['schedules-complete'] })
      ]);

      // ✅ FORCE UI REFRESH: Trigger immediate UI update with longer delay for day copy
      setTimeout(() => {
        forceRefresh();
        console.log('🚀 Force refresh triggered after copy multiple days operation');

        // ✅ ADDITIONAL: Force calendar refetch for day copy
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('forceCalendarRefresh'));
          console.log('📅 Calendar refetch event dispatched for day copy');
        }, 100);
      }, 500); // Longer delay for day copy

      toast({
        title: "✅ Berhasil",
        description: `${result.copiedCount} jadwal berhasil disalin ke ${result.targetDaysCount} hari${result.conflicts > 0 ? ` (${result.conflicts} konflik ditimpa)` : ''}`,
      });
    },
    onError: (error: any) => {
      console.error('Copy multiple days error:', error);
      toast({
        title: "❌ Gagal",
        description: error.message || "Gagal menyalin jadwal ke beberapa hari",
        variant: "destructive",
      });
    },
  });

  return {
    copyDay,
    copyWeek,
    copyMultipleWeeks,
    copyMultipleDays,
    getPreviewSchedules,
    checkCopyConflicts,
    isLoading: copyDay.isPending || copyWeek.isPending || copyMultipleWeeks.isPending || copyMultipleDays.isPending || schedulesLoading
  };
};
