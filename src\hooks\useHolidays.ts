
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useAcademicYears } from './useAcademicYears';
import { Holiday } from '@/types/event';
import { toast } from '@/hooks/use-toast';

export const useHolidays = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const queryClient = useQueryClient();
  
  const activeAcademicYear = academicYears?.find(year => year.is_active);

  const { data: holidays = [], isLoading } = useQuery({
    queryKey: ['holidays', profile?.school_id, activeAcademicYear?.id],
    queryFn: async () => {
      if (!profile?.school_id || !activeAcademicYear?.id) return [];
      
      const { data, error } = await supabase
        .from('holidays')
        .select('*')
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeAcademicYear.id)
        .order('start_date');

      if (error) {
        console.error('Error fetching holidays:', error);
        return [];
      }

      return data || [];
    },
    enabled: !!profile?.school_id && !!activeAcademicYear?.id,
  });

  const createHolidayMutation = useMutation({
    mutationFn: async (holiday: Omit<Holiday, 'id' | 'school_id' | 'academic_year_id' | 'created_at' | 'updated_at'>) => {
      if (!profile?.school_id || !activeAcademicYear?.id) {
        throw new Error('Missing school or academic year data');
      }

      const { data, error } = await supabase
        .from('holidays')
        .insert([{
          ...holiday,
          school_id: profile.school_id,
          academic_year_id: activeAcademicYear.id,
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['holidays'] });
      toast({
        title: "Berhasil",
        description: "Event telah ditambahkan",
      });
    },
    onError: (error) => {
      console.error('Error creating holiday:', error);
      toast({
        title: "Error",
        description: "Gagal menambahkan event",
        variant: "destructive",
      });
    },
  });

  const updateHolidayMutation = useMutation({
    mutationFn: async ({ id, ...updates }: Partial<Holiday> & { id: string }) => {
      const { data, error } = await supabase
        .from('holidays')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['holidays'] });
      toast({
        title: "Berhasil",
        description: "Event telah diperbarui",
      });
    },
    onError: (error) => {
      console.error('Error updating holiday:', error);
      toast({
        title: "Error",
        description: "Gagal memperbarui event",
        variant: "destructive",
      });
    },
  });

  const deleteHolidayMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('holidays')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['holidays'] });
      toast({
        title: "Berhasil",
        description: "Event telah dihapus",
      });
    },
    onError: (error) => {
      console.error('Error deleting holiday:', error);
      toast({
        title: "Error",
        description: "Gagal menghapus event",
        variant: "destructive",
      });
    },
  });

  const createMultipleHolidaysMutation = useMutation({
    mutationFn: async (holidays: Omit<Holiday, 'id' | 'school_id' | 'academic_year_id' | 'created_at' | 'updated_at'>[]) => {
      if (!profile?.school_id || !activeAcademicYear?.id) {
        throw new Error('Missing school or academic year data');
      }

      const holidaysWithMeta = holidays.map(holiday => ({
        ...holiday,
        school_id: profile.school_id,
        academic_year_id: activeAcademicYear.id,
      }));

      const { data, error } = await supabase
        .from('holidays')
        .insert(holidaysWithMeta)
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['holidays'] });
      toast({
        title: "Berhasil",
        description: `${data.length} libur nasional telah ditambahkan`,
      });
    },
    onError: (error) => {
      console.error('Error creating multiple holidays:', error);
      toast({
        title: "Error",
        description: "Gagal menambahkan libur nasional",
        variant: "destructive",
      });
    },
  });

  return {
    holidays,
    isLoading,
    createHoliday: createHolidayMutation.mutate,
    updateHoliday: updateHolidayMutation.mutate,
    deleteHoliday: deleteHolidayMutation.mutate,
    createMultipleHolidays: createMultipleHolidaysMutation.mutate,
    isCreating: createHolidayMutation.isPending,
    isUpdating: updateHolidayMutation.isPending,
    isDeleting: deleteHolidayMutation.isPending,
    isCreatingMultiple: createMultipleHolidaysMutation.isPending,
  };
};
