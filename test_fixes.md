# 🧪 **TEST PLAN - PERBAIKAN SISTEM**

## 📋 **CHECKLIST TESTING**

### **1. Modal Edit <PERSON><PERSON><PERSON>** ✅
**Test Steps:**
1. <PERSON><PERSON> "Mata Pelajaran" 
2. <PERSON><PERSON> pada cell assignment di matrix (contoh: Bahasa Indonesia - Kelas X A)
3. <PERSON><PERSON> "Edit <PERSON><PERSON><PERSON>" harus terbuka
4. <PERSON><PERSON>lai <PERSON>/<PERSON>hun (contoh: dari 72 ke 80)
5. <PERSON><PERSON> "Simpan"
6. **Expected Result**: 
   - ✅ Data tersimpan ke database
   - ✅ Modal tertutup
   - ✅ Matrix terupdate dengan nilai baru
   - ✅ Toast notification "Berhasil"

**Test Delete:**
1. Buka modal edit assignment
2. <PERSON><PERSON> "Ha<PERSON> dari <PERSON>"
3. Konfirmasi delete
4. **Expected Result**:
   - ✅ Assignment dihapus dari database
   - ✅ Matrix terupdate (cell kosong)
   - ✅ Toast notification "Berhasil"

### **2. Modal Edit Penugasan Ekstrakurikuler** ✅
**Test Steps:**
1. <PERSON><PERSON> "Mata Pelajaran" → Tab "Ekstrakurikuler"
2. K<PERSON> pada cell assignment ekstrakurikuler
3. <PERSON>dal "Edit Penugasan Ekstrakurikuler" harus terbuka
4. Ubah nilai JP/Tahun
5. Klik "Simpan"
6. **Expected Result**:
   - ✅ Data tersimpan ke `extracurricular_classes` table
   - ✅ Modal tertutup
   - ✅ Matrix terupdate

### **3. Drag & Drop - Warna dan Nama Subject** ✅
**Test Steps:**
1. Buka halaman "Jadwal"
2. Pilih kelas dari dropdown
3. Drag subject dari sidebar ke slot waktu di calendar
4. **Expected Result**:
   - ✅ Schedule dibuat dengan nama subject yang benar
   - ✅ Schedule dibuat dengan warna subject yang benar
   - ✅ Toast notification menampilkan nama subject yang benar
   - ✅ Event di calendar menampilkan warna yang sesuai

### **4. Kategori EKSKUL - JP/TAHUN Target & Progress** ✅
**Test Steps:**
1. Buka halaman "Mata Pelajaran" → Tab "Ekstrakurikuler"
2. Tambah ekstrakurikuler baru dengan JP/Tahun tertentu
3. Assign ke beberapa kelas
4. Buka halaman "Jadwal"
5. Pilih kelas yang sudah di-assign ekstrakurikuler
6. Lihat sidebar - kategori EKSKUL harus muncul
7. Drag ekstrakurikuler ke calendar dan buat beberapa schedule
8. **Expected Result**:
   - ✅ Target JP/Tahun diambil dari `extracurricular_classes.hours_per_year`
   - ✅ Progress JP dihitung dari schedule yang sudah dibuat
   - ✅ Progress bar menampilkan persentase yang benar
   - ✅ Warna dan nama ekstrakurikuler ditampilkan dengan benar

## 🔍 **DATABASE VERIFICATION**

### **Check Modal Edit Assignment:**
```sql
-- Verify class_schedules update
SELECT id, subject_id, class_id, hours_per_year, updated_at 
FROM class_schedules 
WHERE class_id = 'your_class_id' AND subject_id = 'your_subject_id';

-- Verify extracurricular_classes update  
SELECT id, extracurricular_id, class_id, hours_per_year, updated_at
FROM extracurricular_classes
WHERE class_id = 'your_class_id' AND extracurricular_id = 'your_ekskul_id';
```

### **Check Drag & Drop:**
```sql
-- Verify schedule creation
SELECT id, subject_id, class_id, start_time, end_time, day_of_week, created_at
FROM class_schedules
WHERE class_id = 'your_class_id' AND day_of_week IS NOT NULL
ORDER BY created_at DESC LIMIT 5;
```

### **Check EKSKUL JP Progress:**
```sql
-- Verify EKSKUL targets
SELECT ec.id, ec.hours_per_year, e.name, e.color
FROM extracurricular_classes ec
JOIN extracurriculars e ON ec.extracurricular_id = e.id
WHERE ec.class_id = 'your_class_id';

-- Verify EKSKUL schedules
SELECT cs.id, cs.subject_id, cs.start_time, cs.end_time, e.name
FROM class_schedules cs
JOIN extracurriculars e ON cs.subject_id = e.id
WHERE cs.class_id = 'your_class_id' AND cs.day_of_week IS NOT NULL;
```

## 🎯 **EXPECTED CONSOLE LOGS**

### **Modal Edit Assignment:**
```
🔄 Saving assignment with hours per year: 80
🔍 Assignment ID: abc123
🎯 Is extracurricular: false
📚 Updating class subject assignment...
✅ Assignment update successful
```

### **Drag & Drop:**
```
🔍 Analyzing subject data for correct ID and styling:
  id: "subject123"
  name: "Bahasa Indonesia"
  color: "#10B981"
  type: "schedule_subject"
  source: "sidebar"

🚀 Creating schedule with enhanced data and metadata:
  subject_id: "subject123"
  start_time: "08:00"
  end_time: "08:45"
  
✅ Schedule created successfully via drag and drop
```

### **EKSKUL JP Progress:**
```
✅ JP Progress calculated (simple) - COMBINED:
  total: 15
  kbm: 12
  ekskul: 3
  data: [...]
```

## 🚨 **TROUBLESHOOTING**

### **If Modal Save Fails:**
1. Check browser console for errors
2. Verify hook imports are correct
3. Check database permissions
4. Verify table structure matches types.ts

### **If Drag & Drop Color Wrong:**
1. Check subject data in sidebar
2. Verify color field in database
3. Check ExpandableCategorySidebar data structure

### **If EKSKUL JP Not Showing:**
1. Verify extracurricular_classes table has data
2. Check useJPProgressSimple hook logs
3. Verify session_categories for EKSKUL exist
4. Check class assignment in matrix

## ✅ **SUCCESS CRITERIA**

All tests pass when:
- ✅ Modal edit assignment saves to correct database table
- ✅ Drag & drop creates schedule with correct subject name and color
- ✅ EKSKUL JP progress shows correct target and realization
- ✅ No console errors during operations
- ✅ Real-time updates work without page refresh
