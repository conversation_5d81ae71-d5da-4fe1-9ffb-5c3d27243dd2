import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import PublicScheduleCalendar from '@/components/schedule/PublicScheduleCalendar';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Calendar, Users, MapPin, Calculator } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { JPCalculationModal } from '@/components/schedule/JPCalculationModal';
import { useSchedules } from '@/hooks/useSchedules';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useTimeSessions } from '@/hooks/useTimeSessions';

interface TokenData {
  classId: string;
  schoolId: string;
}

const PublicExternalViewSimpleCopy: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const [tokenData, setTokenData] = useState<TokenData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [classData, setClassData] = useState<any>(null);
  const [schoolData, setSchoolData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [showJPModal, setShowJPModal] = useState(false);

  // ✅ HOOKS untuk data jadwal
  const { academicWeeks } = useAcademicWeeks();
  const { timeSessions } = useTimeSessions();

  // ✅ STATE untuk schedules data
  const [schedules, setSchedules] = useState<any[]>([]);
  const [schedulesLoading, setSchedulesLoading] = useState(false);

  // ✅ FETCH SCHEDULES untuk class tertentu
  useEffect(() => {
    const fetchSchedules = async () => {
      if (!tokenData?.classId) return;

      try {
        setSchedulesLoading(true);
        console.log('🔄 Fetching schedules for public view:', tokenData.classId);

        // ✅ FETCH DENGAN BATCHING UNTUK MENGHINDARI LIMIT
        let allData: any[] = [];
        let from = 0;
        const batchSize = 1000;
        let hasMore = true;
        let totalFetched = 0;

        while (hasMore) {
          console.log(`📦 PUBLIC: Fetching batch ${from} to ${from + batchSize - 1} (Progress: ${totalFetched} rows)`);

          const { data: batchData, error: batchError } = await supabase
            .from('schedules_view')
            .select(`
              id,
              academic_week,
              day_of_week,
              start_time,
              end_time,
              subject_name,
              subject_color,
              class_name,
              class_id,
              subject_id,
              teacher_name,
              session_category_name,
              session_category_color,
              room,
              notes,
              tujuan_pembelajaran,
              materi_pembelajaran
            `)
            .eq('class_id', tokenData.classId)
            .not('day_of_week', 'is', null)
            .order('academic_week', { ascending: true })
            .order('day_of_week', { ascending: true })
            .order('start_time', { ascending: true })
            .range(from, from + batchSize - 1);

          if (batchError) {
            console.error('❌ PUBLIC: Batch error:', batchError);
            break;
          }

          if (!batchData || batchData.length === 0) {
            console.log('✅ PUBLIC: No more data to fetch');
            break;
          }

          allData = [...allData, ...batchData];
          totalFetched += batchData.length;

          // Check if we got less than batch size (end of data)
          if (batchData.length < batchSize) {
            hasMore = false;
          } else {
            from += batchSize;
          }

          // Safety limit
          if (totalFetched >= 10000) {
            console.log('⚠️ PUBLIC: Reached safety limit of 10k records');
            hasMore = false;
          }
        }

        const data = allData;
        const error = null;

        if (error) {
          console.error('❌ Error fetching schedules:', error);
          return;
        }

        console.log('✅ Schedules fetched for public view:', {
          count: data?.length || 0,
          classId: tokenData.classId,
          sampleData: data?.slice(0, 3),
          weekDistribution: data?.reduce((acc: any, schedule: any) => {
            const week = schedule.academic_week;
            acc[week] = (acc[week] || 0) + 1;
            return acc;
          }, {})
        });

        setSchedules(data || []);
      } catch (error) {
        console.error('❌ Error in fetchSchedules:', error);
      } finally {
        setSchedulesLoading(false);
      }
    };

    fetchSchedules();
  }, [tokenData?.classId]);

  // ✅ DEBUG: Log data untuk troubleshooting
  console.log('🔥 PUBLIC VIEW DEBUG:', {
    schedulesCount: schedules?.length || 0,
    academicWeeksCount: academicWeeks?.length || 0,
    timeSessionsCount: timeSessions?.length || 0,
    selectedWeek,
    tokenData,
    classData,
    schedulesLoading
  });

  // ✅ DECODE TOKEN
  useEffect(() => {
    if (!token) {
      setError('Token tidak valid');
      return;
    }

    try {
      const decoded = atob(token);
      const [classId, schoolId] = decoded.split(':');
      
      if (!classId || !schoolId) {
        setError('Token format tidak valid');
        return;
      }

      setTokenData({ classId, schoolId });
      console.log('✅ Token decoded successfully:', { classId, schoolId });
    } catch (decodeError) {
      console.error('❌ Token decode error:', decodeError);
      setError('Token tidak valid atau rusak');
    }
  }, [token]);

  // ✅ FETCH CLASS AND SCHOOL DATA
  useEffect(() => {
    const fetchData = async () => {
      if (!tokenData) return;

      try {
        setIsLoading(true);

        // Fetch class data
        const { data: classInfo, error: classError } = await supabase
          .from('classes')
          .select('*')
          .eq('id', tokenData.classId)
          .single();

        if (classError) {
          setError('Kelas tidak ditemukan');
          return;
        }

        setClassData(classInfo);

        // Fetch school data
        const { data: schoolInfo, error: schoolError } = await supabase
          .from('schools')
          .select('*')
          .eq('id', tokenData.schoolId)
          .single();

        if (!schoolError) {
          setSchoolData(schoolInfo);
        }

      } catch (error) {
        console.error('❌ Error fetching data:', error);
        setError('Gagal memuat data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [tokenData]);

  // ✅ LOADING STATE
  if (!tokenData || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Memuat jadwal...</p>
        </div>
      </div>
    );
  }

  // ✅ ERROR STATE
  if (error || !classData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                {error || 'Kelas tidak ditemukan'}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* ✅ HEADER */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {schoolData?.logo_url && (
                <img 
                  src={schoolData.logo_url} 
                  alt="Logo Sekolah" 
                  className="h-12 w-12 object-contain"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {schoolData?.name || 'SMA IT HSI'}
                </h1>
                <p className="text-gray-600">
                  Jadwal Kelas {classData.name} - Tampilan Publik
                </p>
              </div>
            </div>
            
            <div className="text-right text-sm text-gray-500">
              <p className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Tahun Akademik 2025/2026
              </p>
              <p className="flex items-center mt-1">
                <Users className="h-4 w-4 mr-1" />
                Kelas {classData.name}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* ✅ MAIN CONTENT - LANGSUNG GUNAKAN SCHEDULE CALENDAR COMPONENT */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <Card>
          <CardContent className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Jadwal Kelas {classData.name}
              </h3>
              <p className="text-sm text-gray-600">
                Tampilan publik - hanya untuk melihat jadwal
              </p>
            </div>

            {/* ✅ TOMBOL HITUNG JP - SATU-SATUNYA TOMBOL YANG DIIZINKAN */}
            <div className="mb-4 flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowJPModal(true)}
                className="bg-green-50 border border-green-500 text-green-600 hover:bg-green-100 hover:text-green-700 transition-all duration-300"
              >
                <Calculator className="h-4 w-4 mr-2" />
                Hitung JP
              </Button>
            </div>

            {/* ✅ PUBLIC SCHEDULE CALENDAR - READ ONLY */}
            <PublicScheduleCalendar
              schedules={schedules || []}
              academicWeeks={academicWeeks || []}
              timeSessions={timeSessions || []}
              selectedWeek={selectedWeek}
              onWeekChange={setSelectedWeek}
              selectedClassId={tokenData.classId}
              className="w-full"
            />
          </CardContent>
        </Card>
      </div>

      {/* ✅ FOOTER */}
      <div className="bg-white border-t border-gray-200 mt-8">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="text-center text-sm text-gray-500">
            <p className="flex items-center justify-center">
              <MapPin className="h-4 w-4 mr-1" />
              {schoolData?.address || 'Purworejo, Jawa Tengah'}
            </p>
            <p className="mt-1">📅 Jadwal ini adalah tampilan publik - hanya untuk melihat</p>
          </div>
        </div>
      </div>

      {/* ✅ JP CALCULATION MODAL */}
      {showJPModal && (
        <JPCalculationModal
          isOpen={showJPModal}
          onClose={() => setShowJPModal(false)}
          selectedClassId={tokenData.classId}
          selectedWeek={selectedWeek}
        />
      )}
    </div>
  );
};

export default PublicExternalViewSimpleCopy;
