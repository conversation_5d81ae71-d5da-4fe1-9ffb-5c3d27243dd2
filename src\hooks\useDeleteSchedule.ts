import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAcademicWeeks } from './useAcademicWeeks';
import { useSchedulesComplete } from './useSchedulesPaginated';

interface DeleteDayParams {
  day: number;
  week: number;
  classId: string;
}

interface DeleteMultipleDaysParams {
  days: number[];
  week: number;
  classId: string;
}

interface DeleteWeeksParams {
  weeks: number[];
  classId: string;
}

interface DeleteMonthParams {
  month: number;
  classId: string;
}

export const useDeleteSchedule = (classId?: string) => {
  const queryClient = useQueryClient();

  // 🚀 FIXED: Use paginated hook to get ALL data including weeks 14-24
  const { data: allSchedules = [], isLoading: schedulesLoading, error: schedulesError } = useSchedulesComplete();

  console.log('🔍 useDeleteSchedule (PAGINATED):', {
    classId,
    allSchedulesCount: allSchedules?.length || 0,
    isLoading: schedulesLoading,
    error: schedulesError
  });

  // Delete schedules for a specific day
  const deleteDay = useMutation({
    mutationFn: async ({ day, week, classId }: DeleteDayParams) => {
      console.log('🗑️ Deleting day schedules:', { day, week, classId });

      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .delete()
        .eq('day_of_week', day)
        .eq('academic_week', week)
        .eq('class_id', classId);

      if (error) {
        console.error('❌ Error deleting day schedules:', error);
        throw error;
      }

      console.log('✅ Day schedules deleted successfully:', data);
      return data;
    },
    onSuccess: async (_, variables) => {
      console.log('✅ Delete day successful');

      // ✅ AGGRESSIVE CACHE INVALIDATION: Force immediate UI updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedules'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-for-copy'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
        queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
      ]);

      // Force immediate refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['schedules'] }),
        queryClient.refetchQueries({ queryKey: ['schedules-complete'] })
      ]);

      toast.success(`Jadwal hari berhasil dihapus`);
    },
    onError: (error) => {
      console.error('Delete day error:', error);
      toast.error('Gagal menghapus jadwal hari');
    }
  });

  // Delete schedules for multiple days in a specific week
  const deleteMultipleDays = useMutation({
    mutationFn: async ({ days, week, classId }: DeleteMultipleDaysParams) => {
      console.log('🗑️ Deleting multiple days schedules:', { days, week, classId });

      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .delete()
        .in('day_of_week', days)
        .eq('academic_week', week)
        .eq('class_id', classId);

      if (error) {
        console.error('❌ Error deleting multiple days schedules:', error);
        throw error;
      }

      console.log('✅ Multiple days schedules deleted successfully:', data);
      return data;
    },
    onSuccess: async (_, variables) => {
      console.log('✅ Delete multiple days successful');

      // ✅ AGGRESSIVE CACHE INVALIDATION: Force immediate UI updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedules'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-for-copy'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
        queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
      ]);

      // Force immediate refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['schedules'] }),
        queryClient.refetchQueries({ queryKey: ['schedules-complete'] })
      ]);

      toast.success(`Jadwal ${variables.days.length} hari berhasil dihapus`);
    },
    onError: (error) => {
      console.error('Delete multiple days error:', error);
      toast.error('Gagal menghapus jadwal multiple hari');
    }
  });

  // Delete schedules for multiple weeks
  const deleteWeeks = useMutation({
    mutationFn: async ({ weeks, classId }: DeleteWeeksParams) => {
      console.log('🗑️ Deleting week schedules:', { weeks, classId });

      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .delete()
        .in('academic_week', weeks)
        .eq('class_id', classId);

      if (error) {
        console.error('❌ Error deleting week schedules:', error);
        throw error;
      }

      console.log('✅ Week schedules deleted successfully:', data);
      return data;
    },
    onSuccess: async (_, variables) => {
      console.log('✅ Delete weeks successful');

      // ✅ AGGRESSIVE CACHE INVALIDATION: Force immediate UI updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedules'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-for-copy'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
        queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
      ]);

      // Force immediate refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['schedules'] }),
        queryClient.refetchQueries({ queryKey: ['schedules-complete'] })
      ]);

      toast.success(`Jadwal ${variables.weeks.length} pekan berhasil dihapus`);
    },
    onError: (error) => {
      console.error('Delete weeks error:', error);
      toast.error('Gagal menghapus jadwal pekan');
    }
  });

  // Delete schedules for a specific month
  const deleteMonth = useMutation({
    mutationFn: async ({ month, classId }: DeleteMonthParams) => {
      console.log('🗑️ Deleting month schedules:', { month, classId });

      // ✅ FIXED: Get actual weeks from existing schedules data instead of hardcoded mapping
      // Find all unique academic weeks that exist in the specified month
      const currentYear = new Date().getFullYear();
      const monthStart = new Date(currentYear, month - 1, 1);
      const monthEnd = new Date(currentYear, month, 0);

      console.log('📅 Month range:', { month, monthStart, monthEnd });

      // Get all unique weeks from existing schedules for this class
      const classSchedules = allSchedules.filter(schedule => schedule.class_id === classId);
      const allWeeksInClass = [...new Set(classSchedules.map(s => s.academic_week))].sort((a, b) => a - b);

      console.log('📊 All weeks in class:', allWeeksInClass);

      // For month deletion, we'll use a simple mapping based on academic calendar
      // This is more reliable than trying to calculate dates
      const monthToWeeksMap: Record<number, number[]> = {
        1: [21, 22, 23, 24],      // January - weeks 21-24
        2: [25, 26, 27, 28],      // February - weeks 25-28
        3: [29, 30, 31, 32],      // March - weeks 29-32
        4: [33, 34, 35, 36],      // April - weeks 33-36
        5: [37, 38, 39, 40],      // May - weeks 37-40
        6: [41, 42, 43, 44],      // June - weeks 41-44
        7: [1, 2, 3, 4],          // July - weeks 1-4 (start of academic year)
        8: [5, 6, 7, 8],          // August - weeks 5-8
        9: [9, 10, 11, 12],       // September - weeks 9-12
        10: [13, 14, 15, 16],     // October - weeks 13-16
        11: [17, 18, 19, 20],     // November - weeks 17-20
        12: [45, 46, 47, 48]      // December - weeks 45-48
      };

      const mappedWeeks = monthToWeeksMap[month] || [];

      // Only use weeks that actually exist in the class schedules
      const weekNumbers = mappedWeeks.filter(week => allWeeksInClass.includes(week));

      console.log('📅 Mapped weeks for month', month, ':', mappedWeeks);
      console.log('📅 Existing weeks to delete:', weekNumbers);

      if (weekNumbers.length === 0) {
        throw new Error(`No schedules found for month ${month} in the selected class`);
      }

      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .delete()
        .in('academic_week', weekNumbers)
        .eq('class_id', classId);

      if (error) {
        console.error('❌ Error deleting month schedules:', error);
        throw error;
      }

      console.log('✅ Month schedules deleted successfully:', data);
      return data;
    },
    onSuccess: async (_, variables) => {
      console.log('✅ Delete month successful');

      // ✅ AGGRESSIVE CACHE INVALIDATION: Force immediate UI updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedules'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-complete'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-for-copy'] }),
        queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] }),
        queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] })
      ]);

      // Force immediate refetch
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['schedules'] }),
        queryClient.refetchQueries({ queryKey: ['schedules-complete'] })
      ]);

      toast.success(`Jadwal bulan berhasil dihapus`);
    },
    onError: (error) => {
      console.error('Delete month error:', error);
      toast.error('Gagal menghapus jadwal bulan');
    }
  });

  // Get preview schedules for deletion
  const getPreviewSchedules = (
    dayOrUndefined: number | number[] | undefined,
    weekOrWeeksOrMonth: number | number[],
    targetClassId: string,
    type: 'day' | 'multiple-days' | 'week' | 'month'
  ) => {
    console.log('🔍 getPreviewSchedules called:', {
      dayOrUndefined,
      weekOrWeeksOrMonth,
      targetClassId,
      type,
      allSchedulesCount: allSchedules?.length || 0
    });

    if (!allSchedules || !targetClassId) {
      console.log('❌ No schedules or classId available');
      return [];
    }

    // Log sample data structure for debugging
    if (allSchedules.length > 0) {
      console.log('📋 Sample schedule data structure:', {
        firstSchedule: allSchedules[0],
        fieldsAvailable: Object.keys(allSchedules[0])
      });

      // Check class_id distribution
      const classDistribution = allSchedules.reduce((acc: any, schedule: any) => {
        const classId = schedule.class_id;
        if (!acc[classId]) acc[classId] = 0;
        acc[classId]++;
        return acc;
      }, {});
      console.log('📊 Class distribution in schedules:', classDistribution);
      console.log('🎯 Target class ID:', targetClassId);
    }

    let filteredSchedules = [];

    // Filter based on type
    if (type === 'day' && typeof dayOrUndefined === 'number' && typeof weekOrWeeksOrMonth === 'number') {
      filteredSchedules = allSchedules.filter(schedule =>
        schedule.day_of_week === dayOrUndefined &&
        schedule.academic_week === weekOrWeeksOrMonth &&
        schedule.class_id === targetClassId
      );
      console.log('📅 Day filter result:', filteredSchedules.length);
    } else if (type === 'multiple-days' && Array.isArray(dayOrUndefined) && typeof weekOrWeeksOrMonth === 'number') {
      filteredSchedules = allSchedules.filter(schedule =>
        dayOrUndefined.includes(schedule.day_of_week) &&
        schedule.academic_week === weekOrWeeksOrMonth &&
        schedule.class_id === targetClassId
      );
      console.log('📅 Multiple days filter result:', filteredSchedules.length);
    } else if (type === 'week' && Array.isArray(weekOrWeeksOrMonth)) {
      filteredSchedules = allSchedules.filter(schedule =>
        weekOrWeeksOrMonth.includes(schedule.academic_week) &&
        schedule.class_id === targetClassId
      );
      console.log('📅 Week filter result:', filteredSchedules.length);
    } else if (type === 'month' && typeof weekOrWeeksOrMonth === 'number') {
      // For month, we need to get weeks in that month
      // For now, let's assume weeks 1-4 for month 1, 5-8 for month 2, etc.
      const startWeek = (weekOrWeeksOrMonth - 1) * 4 + 1;
      const endWeek = weekOrWeeksOrMonth * 4;
      filteredSchedules = allSchedules.filter(schedule =>
        schedule.academic_week >= startWeek &&
        schedule.academic_week <= endWeek &&
        schedule.class_id === targetClassId
      );
      console.log('📅 Month filter result:', filteredSchedules.length);
    }

    console.log('✅ Final filtered schedules:', filteredSchedules.length);
    return filteredSchedules;
  };

  const isLoading = deleteDay.isPending || deleteMultipleDays.isPending || deleteWeeks.isPending || deleteMonth.isPending;

  return {
    deleteDay,
    deleteMultipleDays,
    deleteWeeks,
    deleteMonth,
    getPreviewSchedules,
    isLoading
  };
};
