-- 🗄️ DATABASE MIGRATION SCRIPT UNTUK PRODUCTION
-- Jalankan script ini di Supabase production yang disediakan hosting

-- ============================================================================
-- 1. ENABLE EXTENSIONS
-- ============================================================================
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- 2. CREATE TABLES (sesuai urutan dependency)
-- ============================================================================

-- Academic Years
CREATE TABLE IF NOT EXISTS academic_years (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT false,
    school_id UUID, -- Add school_id for multi-tenant support
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Academic Weeks
CREATE TABLE IF NOT EXISTS academic_weeks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    week_number INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Schools
CREATE TABLE IF NOT EXISTS schools (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Session Categories
CREATE TABLE IF NOT EXISTS session_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    color VARCHAR(7) DEFAULT '#6B7280',
    description TEXT,
    school_id UUID, -- Add school_id for multi-tenant support
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Classes
CREATE TABLE IF NOT EXISTS classes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    level VARCHAR(50),
    grade INTEGER,
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    school_id UUID, -- Add school_id for multi-tenant support
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teachers
CREATE TABLE IF NOT EXISTS teachers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    full_name VARCHAR(255) NOT NULL,
    nip VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    school_id UUID, -- Add school_id for multi-tenant support
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subjects
CREATE TABLE IF NOT EXISTS subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20),
    color VARCHAR(7) DEFAULT '#6B7280',
    curriculum_categories_id UUID REFERENCES session_categories(id),
    school_id UUID, -- Add school_id for multi-tenant support
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Schedule Subjects (Mata Pelajaran per Kelas)
CREATE TABLE IF NOT EXISTS schedule_subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20),
    color VARCHAR(7) DEFAULT '#6B7280',
    session_category_id UUID REFERENCES session_categories(id),
    school_id UUID, -- Add school_id for multi-tenant support
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    jp_per_year INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ✅ FIXED: Rename 'schedules' table to 'class_schedules' to match application code
CREATE TABLE IF NOT EXISTS class_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    academic_week INTEGER NOT NULL,
    class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
    subject_id UUID REFERENCES schedule_subjects(id) ON DELETE CASCADE, -- ✅ FIXED: Proper foreign key relationship
    teacher_id UUID REFERENCES teachers(id),
    day_of_week INTEGER CHECK (day_of_week >= 1 AND day_of_week <= 7),
    start_time TIME,
    end_time TIME,
    schedule_date DATE, -- Add schedule_date for specific date scheduling
    room VARCHAR(100),
    notes TEXT,
    learning_objectives TEXT,
    learning_materials TEXT,
    teacher_notes TEXT,
    hours_per_week INTEGER DEFAULT 0, -- For backward compatibility
    hours_per_year INTEGER DEFAULT 0, -- Target JP for subjects
    school_id UUID, -- Add school_id for multi-tenant support
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 3. CREATE INDEXES untuk Performance (Optimized for 500k+ rows)
-- ============================================================================
CREATE INDEX IF NOT EXISTS idx_class_schedules_class_week ON class_schedules(class_id, academic_week);
CREATE INDEX IF NOT EXISTS idx_class_schedules_day_time ON class_schedules(day_of_week, start_time);
CREATE INDEX IF NOT EXISTS idx_class_schedules_academic_year ON class_schedules(academic_year_id);
CREATE INDEX IF NOT EXISTS idx_class_schedules_subject_id ON class_schedules(subject_id);
CREATE INDEX IF NOT EXISTS idx_class_schedules_teacher_id ON class_schedules(teacher_id);
CREATE INDEX IF NOT EXISTS idx_academic_weeks_year ON academic_weeks(academic_year_id, week_number);

-- Additional performance indexes for large datasets
CREATE INDEX IF NOT EXISTS idx_class_schedules_composite ON class_schedules(class_id, academic_week, day_of_week, start_time);
CREATE INDEX IF NOT EXISTS idx_class_schedules_time_range ON class_schedules(start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_class_schedules_school_id ON class_schedules(school_id);

-- ============================================================================
-- 4. CREATE VIEW untuk Calendar (Updated to use class_schedules)
-- ============================================================================
CREATE OR REPLACE VIEW schedules_view AS
SELECT 
    cs.id,
    cs.academic_week,
    cs.academic_year_id,
    cs.class_id,
    cs.day_of_week,
    cs.start_time,
    cs.end_time,
    cs.schedule_date,
    cs.room,
    cs.notes,
    cs.learning_objectives,
    cs.learning_materials,
    cs.teacher_notes,
    cs.subject_id,
    cs.hours_per_week,
    cs.hours_per_year,
    cs.school_id,
    ss.name as subject_name,
    ss.code as subject_code,
    ss.color as subject_color,
    c.name as class_name,
    t.full_name as teacher_name,
    t.id as teacher_id,
    sc.name as session_category_name,
    sc.color as session_category_color,
    sc.id as session_category_id,
    -- Calculate JP realisasi based on duration
    CASE 
        WHEN cs.start_time IS NOT NULL AND cs.end_time IS NOT NULL THEN
            EXTRACT(EPOCH FROM (cs.end_time - cs.start_time)) / 60.0 / 45.0 -- Convert to JP (45 minutes = 1 JP)
        ELSE 0
    END as jp_realisasi,
    cs.created_at
FROM class_schedules cs
LEFT JOIN schedule_subjects ss ON cs.subject_id = ss.id
LEFT JOIN classes c ON cs.class_id = c.id
LEFT JOIN teachers t ON cs.teacher_id = t.id
LEFT JOIN session_categories sc ON ss.session_category_id = sc.id;

-- ============================================================================
-- 5. CREATE ADDITIONAL TABLES FOR COMPLETE FUNCTIONALITY
-- ============================================================================

-- Schedule Class Subjects (KBM subject assignments to classes)
CREATE TABLE IF NOT EXISTS schedule_class_subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
    schedule_subject_id UUID REFERENCES schedule_subjects(id) ON DELETE CASCADE,
    hours_per_year INTEGER DEFAULT 0,
    school_id UUID,
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(class_id, schedule_subject_id, academic_year_id)
);

-- Extracurriculars
CREATE TABLE IF NOT EXISTS extracurriculars (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    color VARCHAR(7) DEFAULT '#6B7280',
    session_category_id UUID REFERENCES session_categories(id),
    school_id UUID,
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Extracurricular Classes (EKSKUL assignments to classes)
CREATE TABLE IF NOT EXISTS extracurricular_classes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
    extracurricular_id UUID REFERENCES extracurriculars(id) ON DELETE CASCADE,
    hours_per_year INTEGER DEFAULT 0,
    school_id UUID,
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(class_id, extracurricular_id, academic_year_id)
);

-- Profiles table for user management
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    school_id UUID REFERENCES schools(id),
    full_name VARCHAR(255),
    role VARCHAR(50) DEFAULT 'teacher',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 6. ENABLE ROW LEVEL SECURITY (RLS)
-- ============================================================================
ALTER TABLE academic_years ENABLE ROW LEVEL SECURITY;
ALTER TABLE academic_weeks ENABLE ROW LEVEL SECURITY;
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE session_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE class_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_class_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE extracurriculars ENABLE ROW LEVEL SECURITY;
ALTER TABLE extracurricular_classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 7. CREATE RLS POLICIES (Allow all for now, customize later)
-- ============================================================================
CREATE POLICY "Allow all operations" ON academic_years FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON academic_weeks FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON schools FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON session_categories FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON classes FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON teachers FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON subjects FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON schedule_subjects FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON class_schedules FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON schedule_class_subjects FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON extracurriculars FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON extracurricular_classes FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON profiles FOR ALL USING (true);

-- ============================================================================
-- 8. INSERT DEFAULT DATA
-- ============================================================================

-- Insert default session categories
INSERT INTO session_categories (id, name, color, description) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'KBM', '#3B82F6', 'Kegiatan Belajar Mengajar'),
    ('550e8400-e29b-41d4-a716-446655440002', 'EKSKUL', '#10B981', 'Ekstrakurikuler'),
    ('550e8400-e29b-41d4-a716-446655440003', 'UPACARA', '#F59E0B', 'Upacara dan Kegiatan Khusus')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 9. ENABLE REALTIME (Run this in Supabase Dashboard)
-- ============================================================================
-- Go to Database > Replication in Supabase Dashboard
-- Enable realtime for tables: class_schedules, schedule_subjects, schedule_class_subjects

-- ============================================================================
-- SELESAI! 
-- Sekarang import data dari development database Anda
-- ============================================================================