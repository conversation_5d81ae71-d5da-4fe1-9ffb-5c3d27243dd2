// =====================================================
// NEW ADD SUBJECT MODAL - USING SCHEDULE_SUBJECTS TABLE
// =====================================================

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useCreateScheduleSubject, useCreateScheduleClassSubject } from '@/hooks/useScheduleSubjects';
import { useClasses } from '@/hooks/useClasses';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { toast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { CreateScheduleSubjectData, ScheduleSubjectFormData, ScheduleClassSelectionMode } from '@/types/scheduleCategory';

// Predefined colors for subjects
const SUBJECT_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
  '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
];

interface NewAddSubjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCategoryId?: string;
  selectedClassId?: string;
}

export const NewAddSubjectModal: React.FC<NewAddSubjectModalProps> = ({
  isOpen,
  onClose,
  selectedCategoryId,
  selectedClassId
}) => {
  const queryClient = useQueryClient();
  
  // Hooks
  const { data: classes = [] } = useClasses();
  const { data: sessionCategories = [] } = useSessionCategories();
  const createSubjectMutation = useCreateScheduleSubject(false); // Disable auto toast
  const createClassSubjectMutation = useCreateScheduleClassSubject(false); // Disable auto toast

  // Form state
  const [formData, setFormData] = useState<ScheduleSubjectFormData>({
    name: '',
    code: '',
    color: SUBJECT_COLORS[0],
    total_hours_per_year: 0,
    standard_duration: 45,
    schedule_category_id: selectedCategoryId || '',
    selected_classes: selectedClassId ? [selectedClassId] : []
  });

  // Class selection mode state
  const [classSelectionMode, setClassSelectionMode] = useState<ScheduleClassSelectionMode>(ScheduleClassSelectionMode.SINGLE);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        name: '',
        code: '',
        color: SUBJECT_COLORS[0],
        total_hours_per_year: 0,
        standard_duration: 45,
        schedule_category_id: selectedCategoryId || '',
        selected_classes: selectedClassId ? [selectedClassId] : []
      });
      setClassSelectionMode(ScheduleClassSelectionMode.SINGLE);
    }
  }, [isOpen, selectedCategoryId, selectedClassId]);

  // Handle form submission
  const handleSubmit = async () => {
    console.log('=== NEW MODAL SUBMIT PROCESS ===');
    console.log('Form data:', formData);
    
    // Validation
    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Nama pelajaran harus diisi",
        variant: "destructive"
      });
      return;
    }

    if (formData.name.trim().length < 2) {
      toast({
        title: "Error",
        description: "Nama pelajaran minimal 2 karakter",
        variant: "destructive"
      });
      return;
    }

    if (!formData.schedule_category_id) {
      toast({
        title: "Error",
        description: "Kategori harus dipilih",
        variant: "destructive"
      });
      return;
    }

    // Determine target classes
    let targetClasses: string[] = [];
    if (classSelectionMode === ScheduleClassSelectionMode.ALL) {
      targetClasses = classes.map(cls => cls.id);
    } else if (classSelectionMode === ScheduleClassSelectionMode.MULTIPLE) {
      targetClasses = formData.selected_classes;
    } else {
      targetClasses = formData.selected_classes.slice(0, 1);
    }

    if (targetClasses.length === 0) {
      toast({
        title: "Error",
        description: "Minimal pilih satu kelas",
        variant: "destructive"
      });
      return;
    }

    console.log('Target classes:', targetClasses);

    try {
      // Step 1: Create the schedule subject
      console.log('=== CREATING SCHEDULE SUBJECT ===');
      const subjectData: CreateScheduleSubjectData = {
        name: formData.name.trim(),
        code: formData.code?.trim() || undefined,
        color: formData.color,
        total_hours_per_year: formData.total_hours_per_year,
        standard_duration: formData.standard_duration,
        schedule_category_id: formData.schedule_category_id,
        selected_classes: targetClasses
      };

      console.log('Subject data:', subjectData);
      const newSubject = await createSubjectMutation.mutateAsync(subjectData);
      console.log('Schedule subject created:', newSubject);

      // Step 2: Create class relations
      console.log('=== CREATING CLASS RELATIONS ===');
      for (let i = 0; i < targetClasses.length; i++) {
        const classId = targetClasses[i];
        
        const classSubjectData = {
          class_id: classId,
          schedule_subject_id: newSubject.id,
          hours_per_week: Math.round(formData.total_hours_per_year / 36) || 0,
          hours_per_year: formData.total_hours_per_year
        };

        console.log(`Creating class relation ${i + 1}/${targetClasses.length}:`, classSubjectData);
        await createClassSubjectMutation.mutateAsync(classSubjectData);
      }

      // Step 3: Refresh queries
      console.log('=== REFRESHING QUERIES ===');
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });

      // Success
      toast({
        title: "Berhasil",
        description: `Mata pelajaran berhasil ditambahkan ke ${targetClasses.length} kelas`
      });

      console.log('=== NEW MODAL SUBMIT COMPLETED ===');
      onClose();
    } catch (error) {
      console.error('=== ERROR IN NEW MODAL SUBMIT ===');
      console.error('Error details:', error);
      
      toast({
        title: "Error",
        description: `Gagal menambahkan mata pelajaran: ${error?.message || 'Unknown error'}`,
        variant: "destructive"
      });
    }
  };

  // Handle class selection
  const handleClassSelection = (classId: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        selected_classes: [...prev.selected_classes, classId]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        selected_classes: prev.selected_classes.filter(id => id !== classId)
      }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white">+ Buat Pelajaran Baru</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Nama Pelajaran */}
          <div>
            <Label htmlFor="name" className="text-white">Nama Pelajaran</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="bg-gray-800 border-gray-600 text-white"
              placeholder="Contoh: Fisika"
            />
          </div>

          {/* Kode (Optional) */}
          <div>
            <Label htmlFor="code" className="text-white">Kode (Opsional)</Label>
            <Input
              id="code"
              value={formData.code}
              onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
              className="bg-gray-800 border-gray-600 text-white"
              placeholder="Contoh: FIS (kosongkan untuk auto-generate)"
            />
          </div>

          {/* Kategori */}
          <div>
            <Label className="text-white">Kategori</Label>
            <Select
              value={formData.schedule_category_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, schedule_category_id: value }))}
            >
              <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                <SelectValue placeholder="Pilih kategori" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {sessionCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id} className="text-white hover:bg-gray-700">
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Warna */}
          <div>
            <Label className="text-white">Warna</Label>
            <div className="grid grid-cols-10 gap-2 mt-2">
              {SUBJECT_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    formData.color === color ? 'border-white scale-110' : 'border-gray-600'
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setFormData(prev => ({ ...prev, color }))}
                />
              ))}
            </div>
          </div>

          {/* JP/Tahun */}
          <div>
            <Label htmlFor="hours" className="text-white">JP/Tahun</Label>
            <Input
              id="hours"
              type="number"
              value={formData.total_hours_per_year}
              onChange={(e) => setFormData(prev => ({ ...prev, total_hours_per_year: parseInt(e.target.value) || 0 }))}
              className="bg-gray-800 border-gray-600 text-white"
              placeholder="144"
              min="0"
            />
          </div>

          {/* Mode Pemilihan Kelas */}
          <div>
            <Label className="text-white">Mode Pemilihan Kelas</Label>
            <RadioGroup
              value={classSelectionMode}
              onValueChange={(value) => {
                setClassSelectionMode(value as ScheduleClassSelectionMode);
                setFormData(prev => ({
                  ...prev,
                  selected_classes: value === ScheduleClassSelectionMode.ALL ? classes.map(c => c.id) : []
                }));
              }}
              className="mt-2 space-y-2"
            >
              <div className={`flex items-start space-x-3 p-3 rounded-lg border transition-all ${
                classSelectionMode === ScheduleClassSelectionMode.SINGLE
                  ? 'border-blue-400 bg-blue-500/20 shadow-md'
                  : 'border-gray-500 bg-gray-700/50 hover:bg-gray-700/70'
              }`}>
                <RadioGroupItem value={ScheduleClassSelectionMode.SINGLE} id="single" className="mt-1 border-white data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500" />
                <div className="flex-1">
                  <Label htmlFor="single" className="text-white font-medium cursor-pointer">Satu Kelas</Label>
                  <p className="text-sm text-gray-300 mt-1">Pelajaran hanya akan ditambahkan ke satu kelas yang dipilih</p>
                </div>
              </div>
              <div className={`flex items-start space-x-3 p-3 rounded-lg border transition-all ${
                classSelectionMode === ScheduleClassSelectionMode.MULTIPLE
                  ? 'border-blue-400 bg-blue-500/20 shadow-md'
                  : 'border-gray-500 bg-gray-700/50 hover:bg-gray-700/70'
              }`}>
                <RadioGroupItem value={ScheduleClassSelectionMode.MULTIPLE} id="multiple" className="mt-1 border-white data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500" />
                <div className="flex-1">
                  <Label htmlFor="multiple" className="text-white font-medium cursor-pointer">Beberapa Kelas</Label>
                  <p className="text-sm text-gray-300 mt-1">Pilih beberapa kelas tertentu untuk ditambahkan pelajaran</p>
                </div>
              </div>
              <div className={`flex items-start space-x-3 p-3 rounded-lg border transition-all ${
                classSelectionMode === ScheduleClassSelectionMode.ALL
                  ? 'border-blue-400 bg-blue-500/20 shadow-md'
                  : 'border-gray-500 bg-gray-700/50 hover:bg-gray-700/70'
              }`}>
                <RadioGroupItem value={ScheduleClassSelectionMode.ALL} id="all" className="mt-1 border-white data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500" />
                <div className="flex-1">
                  <Label htmlFor="all" className="text-white font-medium cursor-pointer">Semua Kelas</Label>
                  <p className="text-sm text-gray-300 mt-1">Pelajaran akan ditambahkan ke semua {classes.length} kelas sekaligus</p>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Pilihan Kelas */}
          {classSelectionMode !== ScheduleClassSelectionMode.ALL && (
            <div>
              <Label className="text-white">Pilih Kelas</Label>
              <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-500 mt-2">
                <p className="text-sm text-gray-200 mb-3">Pilih kelas untuk pelajaran ini</p>
                <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                  {classes.map((cls) => (
                    <div key={cls.id} className={`flex items-center space-x-3 p-2 rounded border transition-all ${
                      formData.selected_classes.includes(cls.id)
                        ? 'bg-blue-500/20 border-blue-400'
                        : 'bg-gray-600/30 border-gray-500 hover:bg-gray-600/50'
                    }`}>
                      <Checkbox
                        id={cls.id}
                        checked={formData.selected_classes.includes(cls.id)}
                        onCheckedChange={(checked) => handleClassSelection(cls.id, checked as boolean)}
                        className="border-white data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                      />
                      <Label htmlFor={cls.id} className="text-white text-sm cursor-pointer flex-1">
                        {cls.name} ({cls.level} {cls.grade})
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={createSubjectMutation.isPending || createClassSubjectMutation.isPending}
              className="bg-lime-500 hover:bg-lime-600"
            >
              {createSubjectMutation.isPending || createClassSubjectMutation.isPending ? 'Menyimpan...' : 'Simpan'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
