import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const useSchedules = () => {
  return useQuery({
    queryKey: ['schedules'],
    queryFn: async () => {
      console.log('🔄 Fetching schedules data with proper joins...');

      // First try to use schedules_view if it exists, otherwise use manual joins
      let data, error;

      try {
        // Try schedules_view first
        console.log('🔄 Attempting to fetch from schedules_view...');

        // 🔧 PRODUCTION READY: Optimized pagination for 500k+ rows
        console.log('📊 Fetching all schedules with enterprise-grade pagination...');

        let allData: any[] = [];
        let from = 0;
        const batchSize = 10000; // Optimized batch size for production
        let hasMore = true;
        let totalFetched = 0;

        while (hasMore) {
          console.log(`📦 Fetching batch: ${from} to ${from + batchSize - 1} (Progress: ${totalFetched} rows)`);

          const { data: batchData, error: batchError } = await supabase
            .from('schedules_view')
            .select(`
              id,
              academic_week,
              academic_year_id,
              class_id,
              day_of_week,
              start_time,
              end_time,
              subject_id,
              subject_name,
              subject_color,
              class_name,
              teacher_name,
              session_category_name,
              session_category_color,
              created_at
            `)
            .not('day_of_week', 'is', null) // Only scheduled subjects
            .order('academic_week', { ascending: true })
            .order('day_of_week', { ascending: true })
            .order('start_time', { ascending: true })
            .range(from, from + batchSize - 1);

          if (batchError) {
            console.error('❌ Batch error:', batchError);
            throw batchError;
          }

          if (batchData && batchData.length > 0) {
            allData = [...allData, ...batchData];
            totalFetched += batchData.length;
            console.log(`✅ Batch fetched: ${batchData.length} records (Total: ${totalFetched})`);
          }

          // Check if we have more data
          hasMore = batchData && batchData.length === batchSize;
          from += batchSize;

          // Production safety check for 500k rows
          if (from > 500000) {
            console.warn('⚠️ Production safety limit reached at 500k rows, stopping pagination');
            break;
          }

          // Progress logging for large datasets
          if (totalFetched % 50000 === 0 && totalFetched > 0) {
            console.log(`📈 Production Progress: ${totalFetched} rows loaded successfully`);
          }
        }

        const viewResult = { data: allData, error: null };

        console.log('📊 schedules_view result:', {
          data: viewResult.data?.length || 0,
          error: viewResult.error,
          status: viewResult.status,
          statusText: viewResult.statusText
        });

        if (viewResult.error) {
          // View error or doesn't exist, use manual joins
          console.log('📋 schedules_view error, using manual joins...', viewResult.error);

          // ✅ PRODUCTION FALLBACK: Enterprise-grade fallback with optimized pagination
          console.log('🔄 Using production fallback with enterprise pagination...');

          let fallbackData: any[] = [];
          let fallbackFrom = 0;
          const fallbackBatchSize = 10000; // Optimized for production
          let fallbackHasMore = true;
          let fallbackTotalFetched = 0;

          while (fallbackHasMore) {
            console.log(`📦 Fallback batch: ${fallbackFrom} to ${fallbackFrom + fallbackBatchSize - 1} (Progress: ${fallbackTotalFetched})`);

            const { data: fallbackBatch, error: fallbackBatchError } = await supabase
              .from('class_schedules')  // ✅ FIXED: Use actual table that exists
              .select(`
                id,
                academic_week,
                academic_year_id,
                class_id,
                day_of_week,
                start_time,
                end_time,
                subject_id,
                teacher_id,
                room,
                notes,
                created_at,
                schedule_subjects (id, name, code, color),
                classes (id, name, level, grade),
                teachers (id, full_name, nip)
              `)
              .not('day_of_week', 'is', null) // Only scheduled subjects
              .order('academic_week', { ascending: true })
              .order('day_of_week', { ascending: true })
              .order('start_time', { ascending: true })
              .range(fallbackFrom, fallbackFrom + fallbackBatchSize - 1);

            if (fallbackBatchError) {
              console.error('❌ Fallback batch error:', fallbackBatchError);
              throw fallbackBatchError;
            }

            if (fallbackBatch && fallbackBatch.length > 0) {
              fallbackData = [...fallbackData, ...fallbackBatch];
              fallbackTotalFetched += fallbackBatch.length;
              console.log(`✅ Fallback batch: ${fallbackBatch.length} records (Total: ${fallbackTotalFetched})`);
            }

            fallbackHasMore = fallbackBatch && fallbackBatch.length === fallbackBatchSize;
            fallbackFrom += fallbackBatchSize;

            // Production safety limit for fallback
            if (fallbackFrom > 500000) {
              console.warn('⚠️ Fallback production safety limit reached at 500k rows');
              break;
            }

            // Progress logging for fallback
            if (fallbackTotalFetched % 50000 === 0 && fallbackTotalFetched > 0) {
              console.log(`📈 Fallback Progress: ${fallbackTotalFetched} rows loaded`);
            }
          }

          const manualResult = { data: fallbackData, error: null };

          data = manualResult.data;
          error = manualResult.error;
        } else {
          // ✅ FIXED: schedules_view now includes extracurriculars!
          console.log('✅ Using schedules_view with extracurriculars support');
          data = viewResult.data;
          error = viewResult.error;
        }
      } catch (e) {
        console.error('❌ Error in schedules query:', e);
        throw e;
      }

      if (error) {
        console.error('❌ Error fetching schedules:', error);
        throw error;
      }

      console.log('✅ Schedules data fetched:', data?.length, 'records');
      console.log('🔍 Sample schedule data:', data?.[0]);

      // 🚨 CRITICAL DEBUG: Analisis distribusi minggu akademik untuk SEMUA minggu 1-24
      if (data && data.length > 0) {
        const weekDistribution = data.reduce((acc: any, schedule: any) => {
          const week = schedule.academic_week;
          if (!acc[week]) acc[week] = 0;
          acc[week]++;
          return acc;
        }, {});

        const missingWeeks = [];
        const availableWeeks = [];
        for (let i = 1; i <= 24; i++) {
          if (!weekDistribution[i]) {
            missingWeeks.push(i);
          } else {
            availableWeeks.push(i);
          }
        }

        console.log('🔍 CRITICAL - Week Distribution Analysis:', {
          totalSchedules: data.length,
          weekDistribution,
          missingWeeks,
          availableWeeks,
          weeks1to13: availableWeeks.filter(w => w <= 13),
          weeks14to24: availableWeeks.filter(w => w >= 14),
          hasAllWeeks: missingWeeks.length === 0,
          dataIntegrity: {
            expectedWeeks: 24,
            actualWeeks: availableWeeks.length,
            missingCount: missingWeeks.length
          }
        });

        // Sample data from different week ranges
        const sampleWeek1 = data.filter(s => s.academic_week === 1).slice(0, 2);
        const sampleWeek14 = data.filter(s => s.academic_week === 14).slice(0, 2);
        const sampleWeek24 = data.filter(s => s.academic_week === 24).slice(0, 2);

        console.log('📊 Sample Data Verification:', {
          week1Sample: sampleWeek1,
          week14Sample: sampleWeek14,
          week24Sample: sampleWeek24
        });
      }

      // ✅ ENHANCED DIAGNOSTIC: Detailed data structure analysis
      if (data && data.length > 0) {
        console.log('🔍 DIAGNOSTIC - First 3 schedules structure:', data.slice(0, 3).map(schedule => ({
          id: schedule.id,
          subject_id: schedule.subject_id,
          subject_name: schedule.subject_name,
          academic_week: schedule.academic_week,
          class_id: schedule.class_id,
          day_of_week: schedule.day_of_week,
          hasSubjects: !!schedule.subjects,
          hasScheduleSubjects: !!schedule.schedule_subjects,
          hasExtracurriculars: !!schedule.extracurriculars,
          dataSource: schedule.subject_name ? 'schedules_view' : 'manual_joins',
          subjectDetails: {
            subjects: schedule.subjects ? { name: schedule.subjects.name, color: schedule.subjects.color } : null,
            schedule_subjects: schedule.schedule_subjects ? { name: schedule.schedule_subjects.name, color: schedule.schedule_subjects.color } : null,
            extracurriculars: schedule.extracurriculars ? { name: schedule.extracurriculars.name, color: schedule.extracurriculars.color } : null
          }
        })));

        // ✅ DIAGNOSTIC: Show all unique academic weeks and classes
        const uniqueWeeks = [...new Set(data.map(s => s.academic_week))];
        const uniqueClasses = [...new Set(data.map(s => s.class_id))];
        console.log('🔍 DIAGNOSTIC - Available data:', {
          totalSchedules: data.length,
          uniqueAcademicWeeks: uniqueWeeks,
          uniqueClassIds: uniqueClasses,
          sampleScheduleIds: data.slice(0, 5).map(s => s.id)
        });
      } else {
        console.log('❌ DIAGNOSTIC - No schedules data found!');
      }

      // ✅ FIXED: Log specific subject data for debugging
      if (data && data.length > 0) {
        data.slice(0, 3).forEach((schedule, index) => {
          console.log(`📋 Schedule ${index + 1}:`, {
            id: schedule.id,
            subject_id: schedule.subject_id,
            subject_name: schedule.subject_name,
            subject_color: schedule.subject_color,
            session_category_name: schedule.session_category_name,
            dataSource: schedule.subject_name ? 'schedules_view' : 'manual_joins'
          });
        });
      }

      return data;
    },
    // ✅ FORCE FRESH DATA: Ensure immediate updates after copy/delete operations
    staleTime: 0, // Always consider data stale for immediate updates
    gcTime: 0, // Don't cache data to ensure fresh data
    refetchOnWindowFocus: true, // Force refetch to get latest data
    refetchOnMount: true, // Always refetch on component mount
    refetchInterval: false, // Disable polling since we use real-time subscriptions
    refetchOnReconnect: true, // Refetch on reconnect for data consistency
    retry: 3, // Retry failed requests
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
  });
};

export const useCreateSchedule = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (scheduleData: any) => {
      // Get current user's school_id and active academic year
      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      const { data: activeYear } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile?.school_id)
        .eq('is_active', true)
        .single();

      // Remove time_session_id if it's a string (not a UUID)
      const timeSessionId = scheduleData.time_session_id && 
        typeof scheduleData.time_session_id === 'string' && 
        !scheduleData.time_session_id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i) ? 
        null : scheduleData.time_session_id;

      // Prepare schedule data with new fields - allow null teacher_id
      const insertData = {
        subject_id: scheduleData.subject_id,
        teacher_id: scheduleData.teacher_id || null, // Explicitly allow null
        class_id: scheduleData.class_id,
        time_session_id: timeSessionId,
        day_of_week: scheduleData.day_of_week,
        start_time: scheduleData.start_time,
        end_time: scheduleData.end_time || scheduleData.start_time,  // Default to start_time if no end_time
        academic_week: scheduleData.academic_week,
        schedule_date: scheduleData.schedule_date,
        room: scheduleData.room || '',
        notes: scheduleData.notes || '',
        tujuan_pembelajaran: scheduleData.tujuan_pembelajaran || null,
        materi_pembelajaran: scheduleData.materi_pembelajaran || null,
        school_id: profile?.school_id,
        academic_year_id: activeYear?.id,
      };

      // Remove color field as it's not in database schema
      // Color will be derived from subject data

      console.log('🚀 Creating schedule with data:', insertData);
      console.log('📋 Profile data:', profile);
      console.log('📅 Active year data:', activeYear);

      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('❌ Schedule creation error:', error);
        console.error('❌ Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }
      return data;
    },
    onSuccess: (data) => {
      console.log('✅ Schedule created successfully:', data);

      // 🚀 ENHANCED: Comprehensive cache invalidation for immediate UI updates
      console.log('🔄 Invalidating all schedule-related queries after CREATE...');

      // Invalidate all schedule queries
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-simple'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-paginated'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-complete'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-week'] });

      // Invalidate subject-related queries
      queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });

      // Invalidate JP progress queries
      queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
      queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

      // 🚀 ENHANCED: Force immediate refetch for real-time UI updates
      console.log('🔄 Force refetching critical queries after CREATE...');
      queryClient.refetchQueries({ queryKey: ['schedules'] });
      queryClient.refetchQueries({ queryKey: ['schedules-simple'] });
      queryClient.refetchQueries({ queryKey: ['schedules-complete'] });
      queryClient.refetchQueries({ queryKey: ['jp-progress-simple'] });

      toast({
        title: "Berhasil",
        description: "Jadwal berhasil ditambahkan",
      });
    },
    onError: (error) => {
      console.error('Full error:', error);
      toast({
        title: "Gagal",
        description: "Gagal menambahkan jadwal: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...scheduleData }: any) => {
      console.log('🔄 Updating schedule:', id, scheduleData);

      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .update(scheduleData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating schedule:', error);
        throw error;
      }

      console.log('✅ Schedule updated successfully:', data);
      return data;
    },
    onSuccess: (data) => {
      console.log('✅ Schedule update mutation successful:', data);

      // 🚀 ENHANCED: Comprehensive cache invalidation for immediate UI updates
      console.log('🔄 Invalidating all schedule-related queries after UPDATE...');

      // Invalidate all schedule queries
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-simple'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-paginated'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-complete'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-week'] });

      // Invalidate subject-related queries
      queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });

      // Invalidate JP progress queries
      queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
      queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

      // 🚀 ENHANCED: Force immediate refetch for real-time UI updates
      console.log('🔄 Force refetching critical queries after UPDATE...');
      queryClient.refetchQueries({ queryKey: ['schedules'] });
      queryClient.refetchQueries({ queryKey: ['schedules-simple'] });
      queryClient.refetchQueries({ queryKey: ['schedules-complete'] });
      queryClient.refetchQueries({ queryKey: ['jp-progress-simple'] });
    },
    onError: (error) => {
      console.error('❌ Schedule update mutation failed:', error);
    },
  });
};

export const useDeleteSchedule = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      console.log('🗑️ Deleting schedule:', id);

      // 🔍 ENHANCED DEBUG: Check user session and permissions
      const { data: session, error: sessionError } = await supabase.auth.getSession();
      console.log('👤 Current user session:', {
        user: session?.session?.user?.id,
        email: session?.session?.user?.email,
        role: session?.session?.user?.role,
        sessionError
      });

      // 🔍 ENHANCED DEBUG: Check if schedule exists first
      const { data: existingSchedule, error: checkError } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .select('id, class_id, subject_id, academic_week, day_of_week')
        .eq('id', id)
        .single();

      if (checkError) {
        console.error('❌ Error checking schedule existence:', checkError);
        throw new Error(`Schedule not found: ${checkError.message}`);
      }

      if (!existingSchedule) {
        console.error('❌ Schedule not found:', id);
        throw new Error('Schedule not found');
      }

      console.log('📋 Schedule to delete:', existingSchedule);

      // 🚀 ENHANCED: Try delete with better error handling
      const { data, error } = await supabase
        .from('class_schedules')  // ✅ FIXED: Use actual table that exists
        .delete()
        .eq('id', id)
        .select(); // Return deleted data for confirmation

      if (error) {
        console.error('❌ Error deleting schedule:', {
          error,
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });

        // 🔍 ENHANCED: Provide specific error messages
        if (error.code === '42501') {
          // Try alternative approach for permission issues
          console.log('🔄 Trying alternative delete approach...');

          const { data: altData, error: altError } = await supabase
            .rpc('delete_schedule_by_id', { schedule_id: id });

          if (altError) {
            console.error('❌ Alternative delete also failed:', altError);
            throw new Error('Tidak memiliki izin untuk menghapus jadwal ini. Hubungi administrator.');
          }

          console.log('✅ Alternative delete successful:', altData);
          return altData;
        } else if (error.code === '23503') {
          throw new Error('Jadwal tidak dapat dihapus karena masih terkait dengan data lain.');
        } else {
          throw new Error(`Gagal menghapus jadwal: ${error.message}`);
        }
      }

      console.log('✅ Schedule deleted successfully:', data);
      return data;
    },
    onSuccess: () => {
      console.log('✅ Schedule delete mutation successful');

      // 🚀 ENHANCED: Comprehensive cache invalidation for immediate UI updates
      console.log('🔄 Invalidating all schedule-related queries...');

      // Invalidate all schedule queries
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-simple'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-paginated'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-complete'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-calendar'] });
      queryClient.invalidateQueries({ queryKey: ['schedules-week'] });

      // Invalidate subject-related queries
      queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });

      // Invalidate JP progress queries
      queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
      queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

      // 🚀 ENHANCED: Force immediate refetch for real-time UI updates
      console.log('🔄 Force refetching critical queries...');
      queryClient.refetchQueries({ queryKey: ['schedules'] });
      queryClient.refetchQueries({ queryKey: ['schedules-simple'] });
      queryClient.refetchQueries({ queryKey: ['schedules-complete'] });
      queryClient.refetchQueries({ queryKey: ['jp-progress-simple'] });

      toast({
        title: "Berhasil",
        description: "Jadwal berhasil dihapus",
      });
    },
    onError: (error) => {
      console.error('❌ Schedule delete mutation failed:', error);
      toast({
        title: "Gagal",
        description: "Gagal menghapus jadwal: " + error.message,
        variant: "destructive",
      });
    },
  });
};
