import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';

interface WeeklyScrollableNavigationProps {
  selectedWeek: number;
  onWeekSelect: (week: number) => void;
  selectedTargetWeeks?: Set<number>;
  sourceWeek?: number;
  isSelectionMode?: boolean;
}

export const WeeklyScrollableNavigation: React.FC<WeeklyScrollableNavigationProps> = ({
  selectedWeek,
  onWeekSelect,
  selectedTargetWeeks,
  sourceWeek,
  isSelectionMode = false
}) => {
  const { academicWeeks } = useAcademicWeeks();
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to selected week when it changes
  React.useEffect(() => {
    if (scrollAreaRef.current) {
      const selectedButton = scrollAreaRef.current.querySelector(`[data-week="${selectedWeek}"]`) as HTMLElement;
      if (selectedButton) {
        selectedButton.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  }, [selectedWeek]);

  return (
    <div className="bg-background border-t border-border">
      <div className="p-2 w-full bg-card backdrop-blur-xl border border-border rounded-xl shadow-lg relative overflow-hidden">

        <div className="relative z-15 p-3 py-[5px] my-0">
          {/* Scrollable Week Numbers Container with Custom Scrollbar */}
          <div ref={scrollAreaRef} className="w-full overflow-x-auto pb-2 custom-scrollbar">
            <style>
              {`
                .custom-scrollbar::-webkit-scrollbar {
                  height: 6px;
                }
                .custom-scrollbar::-webkit-scrollbar-track {
                  background: hsl(var(--muted));
                  border-radius: 3px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb {
                  background: hsl(var(--muted-foreground) / 0.3);
                  border-radius: 3px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                  background: hsl(var(--muted-foreground) / 0.5);
                }
                .dark .custom-scrollbar::-webkit-scrollbar-track {
                  background: rgba(255, 255, 255, 0.1);
                }
                .dark .custom-scrollbar::-webkit-scrollbar-thumb {
                  background: rgba(255, 255, 255, 0.3);
                }
                .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                  background: rgba(255, 255, 255, 0.5);
                }
              `}
            </style>
            {/* Inner container with flex and full width distribution */}
            <div className="flex gap-2" style={{
              width: `${academicWeeks.length * 60}px`
            }}>
              {academicWeeks.map(week => {
                const isSelected = selectedWeek === week.weekNumber;
                const isTargetSelected = selectedTargetWeeks?.has(week.weekNumber);
                const isSourceWeek = sourceWeek === week.weekNumber;
                const isDisabled = isSelectionMode && isSourceWeek;

                return (
                  <Button
                    key={week.weekNumber}
                    data-week={week.weekNumber}
                    variant={isSelected && !isSelectionMode ? "default" : "outline"}
                    size="sm"
                    onClick={() => !isDisabled && onWeekSelect(week.weekNumber)}
                    disabled={isDisabled}
                    className={`h-6 flex items-center justify-center space-x-2 text-xs font-thin rounded-full px-3 transition-all duration-200 ${
                      isDisabled
                        ? 'bg-muted/30 border-muted text-muted-foreground cursor-not-allowed'
                        : isTargetSelected && isSelectionMode
                        ? 'bg-green-500/80 border-green-400 text-white shadow-lg scale-105 ring-1 ring-green-400/50'
                        : isSelected && !isSelectionMode
                        ? 'bg-primary border-primary text-primary-foreground'
                        : 'bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground hover:scale-105'
                    }`}
                  >
                    {week.weekNumber}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
