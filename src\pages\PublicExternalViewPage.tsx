import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Calendar, Users, Clock, MapPin, BarChart3, Calculator, CalendarDays, Filter } from 'lucide-react';
import PublicScheduleCalendar from '@/components/schedule/PublicScheduleCalendar';
import { JPCalculationModal } from '@/components/schedule/JPCalculationModal';
import { useSchedules } from '@/hooks/useSchedules';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useTimeSessions } from '@/hooks/useTimeSessions';

interface PublicExternalViewData {
  school: {
    name: string;
    logo_url?: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  class: {
    id: string;
    name: string;
    level: string;
  };
  classes: Array<{
    id: string;
    name: string;
    level: string;
    grade: number;
  }>;
  schedules: any[];
  academicWeeks: any[];
  timeSessions: any[];
}

const PublicExternalViewPage: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const [data, setData] = useState<PublicExternalViewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [showJPModal, setShowJPModal] = useState(false);
  const [selectedClassId, setSelectedClassId] = useState<string | null>(null);

  // Navigate to public overview page
  const handleOverviewClick = () => {
    // Open the public overview page in a new tab (secure, no sidebar)
    window.open(`/external/${token}/overview`, '_blank');
  };

  // Navigate to public events page
  const handleEventsClick = () => {
    // Open the public events page in a new tab (secure, no sidebar)
    window.open(`/external/${token}/events`, '_blank');
  };

  useEffect(() => {
    const fetchPublicData = async () => {
      if (!token) {
        setError('Token tidak valid');
        setLoading(false);
        return;
      }

      try {
        console.log('🔍 Fetching public external view data for token:', token);

        // ✅ STEP 1: Decode token to get class_id and school_id
        // For now, we'll use a simple format: base64(class_id:school_id)
        let classId: string;
        let schoolId: string;

        try {
          const decoded = atob(token);
          const [decodedClassId, decodedSchoolId] = decoded.split(':');
          classId = decodedClassId;
          schoolId = decodedSchoolId;
          console.log('✅ Token decoded:', { classId, schoolId });
        } catch (decodeError) {
          console.error('❌ Token decode error:', decodeError);
          setError('Token tidak valid atau rusak');
          setLoading(false);
          return;
        }

        // ✅ STEP 2: Fetch school data
        const { data: schoolData, error: schoolError } = await supabase
          .from('schools')
          .select('name, logo_url, address, phone, email')
          .eq('id', schoolId)
          .single();

        if (schoolError) {
          console.error('❌ School fetch error:', schoolError);
          setError('Data sekolah tidak ditemukan');
          setLoading(false);
          return;
        }

        // ✅ STEP 3: Fetch class data
        const { data: classData, error: classError } = await supabase
          .from('classes')
          .select('id, name, level')
          .eq('id', classId)
          .eq('school_id', schoolId)
          .single();

        if (classError) {
          console.error('❌ Class fetch error:', classError);
          setError('Data kelas tidak ditemukan');
          setLoading(false);
          return;
        }

        // ✅ STEP 3.5: Fetch all classes for filter
        const { data: allClassesData, error: allClassesError } = await supabase
          .from('classes')
          .select('id, name, level, grade')
          .eq('school_id', schoolId)
          .order('name');

        if (allClassesError) {
          console.error('❌ All classes fetch error:', allClassesError);
        }

        // ✅ STEP 4: Fetch schedules data for all classes (not just the token class)
        const { data: schedulesData, error: schedulesError } = await supabase
          .from('schedules_view')
          .select('*')
          .eq('school_id', schoolId)
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true });

        if (schedulesError) {
          console.error('❌ Schedules fetch error:', schedulesError);
          setError('Data jadwal tidak ditemukan');
          setLoading(false);
          return;
        }

        // ✅ STEP 5: Generate academic weeks using EXACT SAME logic as main schedule
        console.log('📅 Generating academic weeks...');

        // Try to get active academic year first
        const { data: activeAcademicYear } = await supabase
          .from('academic_years')
          .select('*')
          .eq('school_id', schoolId)
          .eq('is_active', true)
          .single();

        let academicWeeks;

        if (activeAcademicYear) {
          console.log('✅ Using active academic year:', activeAcademicYear.year_name);

          // Use exact same logic as useAcademicWeeks hook
          const startDate = new Date(activeAcademicYear.start_date);
          const endDate = new Date(activeAcademicYear.end_date);
          const weeks = [];

          // Use date-fns logic (simplified)
          let currentWeek = new Date(startDate);
          // Set to Monday of the week
          currentWeek.setDate(currentWeek.getDate() - currentWeek.getDay() + 1);
          let weekNumber = 1;

          while (currentWeek <= endDate && weekNumber <= 52) {
            const weekEnd = new Date(currentWeek);
            weekEnd.setDate(currentWeek.getDate() + 6); // Sunday

            weeks.push({
              weekNumber,
              startDate: new Date(currentWeek),
              endDate: weekEnd,
              label: `Minggu ${weekNumber}`,
              dateRange: `${currentWeek.getDate()}/${currentWeek.getMonth() + 1} - ${weekEnd.getDate()}/${weekEnd.getMonth() + 1}`,
              isCurrentWeek: false
            });

            currentWeek.setDate(currentWeek.getDate() + 7); // Next week
            weekNumber++;
          }

          academicWeeks = weeks;
          console.log('✅ Generated academic weeks from active year:', weeks.length);
        } else {
          console.log('⚠️ No active academic year, using fallback weeks');

          // Fallback: Create weeks starting from Jan 2025 (same as main schedule fallback)
          const baseDate = new Date(2025, 0, 6); // Jan 6, 2025 (Monday)

          academicWeeks = Array.from({ length: 52 }, (_, i) => {
            const weekNumber = i + 1;
            const startDate = new Date(baseDate);
            startDate.setDate(baseDate.getDate() + (i * 7));

            const endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 6);

            return {
              weekNumber,
              startDate,
              endDate,
              label: `Minggu ${weekNumber}`,
              dateRange: `${startDate.getDate()}/${startDate.getMonth() + 1} - ${endDate.getDate()}/${endDate.getMonth() + 1}`,
              isCurrentWeek: false
            };
          });
        }

        // ✅ STEP 6: Fetch time sessions
        const { data: timeSessionsData, error: timeError } = await supabase
          .from('time_sessions')
          .select('*')
          .eq('school_id', schoolId)
          .order('start_time', { ascending: true });

        if (timeError) {
          console.error('❌ Time sessions fetch error:', timeError);
          // Continue without time sessions
        }

        // ✅ STEP 7: Set all data
        setData({
          school: schoolData,
          class: classData,
          classes: allClassesData || [],
          schedules: schedulesData || [],
          academicWeeks,
          timeSessions: timeSessionsData || []
        });

        // Set default selected class to the token class
        setSelectedClassId(classId);

        console.log('✅ Public external view data loaded:', {
          school: schoolData.name,
          class: classData.name,
          schedulesCount: schedulesData?.length || 0,
          academicWeeksCount: academicWeeks.length,
          timeSessionsCount: timeSessionsData?.length || 0
        });

      } catch (fetchError) {
        console.error('❌ General fetch error:', fetchError);
        setError('Terjadi kesalahan saat memuat data');
      } finally {
        setLoading(false);
      }
    };

    fetchPublicData();
  }, [token]);

  // Filter schedules based on selected class
  const filteredSchedules = useMemo(() => {
    if (!data?.schedules) return [];

    return data.schedules.filter(schedule => {
      // Filter by class
      if (selectedClassId && schedule.class_id !== selectedClassId) {
        return false;
      }
      return true;
    });
  }, [data?.schedules, selectedClassId]);

  // Get current class name for display
  const currentClassName = useMemo(() => {
    if (!selectedClassId || !data?.classes) return 'Semua Kelas';
    const selectedClass = data.classes.find(cls => cls.id === selectedClassId);
    return selectedClass ? selectedClass.name : 'Kelas Tidak Ditemukan';
  }, [selectedClassId, data?.classes]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Memuat jadwal...</p>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                {error || 'Data tidak ditemukan'}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* ✅ HEADER: School and Class Info */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {data.school.logo_url && (
                <img
                  src={data.school.logo_url}
                  alt="Logo Sekolah"
                  className="h-12 w-12 object-contain"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {data.school.name}
                </h1>
                <p className="text-gray-600">
                  Jadwal {currentClassName}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              {/* Hitung JP Button */}
              <Button
                onClick={() => setShowJPModal(true)}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300"
              >
                <Calculator className="h-4 w-4" />
                Hitung JP
              </Button>
              
              {/* Overview Button */}
              <Button
                onClick={handleOverviewClick}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300"
              >
                <BarChart3 className="h-4 w-4" />
                Lihat Overview
              </Button>

              {/* Events Button */}
              <Button
                onClick={handleEventsClick}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 bg-lime-50 border-lime-200 text-lime-700 hover:bg-lime-100 hover:border-lime-300"
              >
                <CalendarDays className="h-4 w-4" />
                Lihat Event
              </Button>
              
              <div className="text-right text-sm text-gray-500">
                <p className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  Tahun Akademik 2025/2026
                </p>
                <p className="flex items-center mt-1">
                  <Users className="h-4 w-4 mr-1" />
                  Kelas {data.class.name}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ✅ MAIN CONTENT: Filter and Calendar */}
      <div className="max-w-7xl mx-auto px-4 py-6 space-y-6">
        {/* Filter Section */}
        <Card className="bg-card border-border shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Filter className="h-5 w-5 text-primary" />
              Filter Kelas
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Class Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Pilih Kelas:</label>
                <Select
                  value={selectedClassId || 'all'}
                  onValueChange={(value) => setSelectedClassId(value === 'all' ? null : value)}
                >
                  <SelectTrigger className="w-full bg-background border-border text-foreground">
                    <SelectValue placeholder="Semua Kelas" />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border-border">
                    <SelectItem value="all" className="text-foreground hover:bg-accent">
                      Semua Kelas
                    </SelectItem>
                    {data.classes.map(cls => (
                      <SelectItem key={cls.id} value={cls.id} className="text-foreground hover:bg-accent">
                        {cls.name} ({cls.level} {cls.grade})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Info */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Informasi:</label>
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-700">
                    Menampilkan jadwal untuk: <strong>{currentClassName}</strong>
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Total jadwal: {filteredSchedules.length} item
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Calendar */}
        <Card>
          <CardContent className="p-6">
            <PublicScheduleCalendar
              schedules={filteredSchedules}
              academicWeeks={data.academicWeeks}
              timeSessions={data.timeSessions}
              selectedWeek={selectedWeek}
              onWeekChange={setSelectedWeek}
              selectedClassId={selectedClassId || data.class.id}
              className="w-full"
            />
          </CardContent>
        </Card>
      </div>

      {/* ✅ FOOTER: School Info */}
      <div className="bg-white border-t border-gray-200 mt-8">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="text-center text-sm text-gray-500">
            <p className="flex items-center justify-center">
              <MapPin className="h-4 w-4 mr-1" />
              {data.school.address}
            </p>
            {(data.school.phone || data.school.email) && (
              <p className="mt-1">
                {data.school.phone && (
                  <span className="mr-4">📞 {data.school.phone}</span>
                )}
                {data.school.email && (
                  <span>📧 {data.school.email}</span>
                )}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* ✅ JP CALCULATION MODAL */}
      {showJPModal && (
        <JPCalculationModal
          isOpen={showJPModal}
          onClose={() => setShowJPModal(false)}
          selectedClassId={selectedClassId || data.class.id}
          selectedWeek={selectedWeek}
        />
      )}
    </div>
  );
};

export default PublicExternalViewPage;
