import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export const MergeScheduleDebug: React.FC = () => {
  const { profile } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const runDatabaseTest = async () => {
    setIsLoading(true);
    const results: any = {};

    try {
      // Test 1: Check schedules_view
      console.log('🔍 Testing schedules_view...');
      const { data: viewData, error: viewError } = await supabase
        .from('schedules_view')
        .select('*')
        .limit(5);

      results.schedules_view = {
        success: !viewError,
        error: viewError?.message,
        count: viewData?.length || 0,
        sample: viewData?.[0]
      };

      // Test 2: Check class_schedules table (ACTUAL TABLE)
      console.log('🔍 Testing class_schedules table...');
      const { data: classSchedulesData, error: classSchedulesError } = await supabase
        .from('class_schedules')
        .select(`
          *,
          schedule_subjects (id, name, session_category_id)
        `)
        .limit(5);

      results.class_schedules_table = {
        success: !classSchedulesError,
        error: classSchedulesError?.message,
        count: classSchedulesData?.length || 0,
        sample: classSchedulesData?.[0]
      };

      // Test 3: Check schedules table (if exists)
      console.log('🔍 Testing schedules table...');
      const { data: schedulesData, error: schedulesError } = await supabase
        .from('schedules')
        .select(`
          *,
          schedule_subjects (id, name, session_category_id)
        `)
        .limit(5);

      results.schedules_table = {
        success: !schedulesError,
        error: schedulesError?.message,
        count: schedulesData?.length || 0,
        sample: schedulesData?.[0]
      };

      // Test 4: Check classes
      console.log('🔍 Testing classes...');
      const { data: classesData, error: classesError } = await supabase
        .from('classes')
        .select('id, name')
        .limit(5);

      results.classes = {
        success: !classesError,
        error: classesError?.message,
        count: classesData?.length || 0,
        data: classesData
      };

      // Test 5: Check specific class schedules for merge test
      if (classesData && classesData.length >= 1) {
        // Find class 12.A specifically
        const class12A = classesData.find(c => c.name === '12.A');

        if (class12A) {
          console.log('🔍 Testing class 12.A schedules...');
          const { data: class12ASchedules, error: class12AError } = await supabase
            .from('schedules_view')
            .select('*')
            .eq('class_id', class12A.id)
            .not('day_of_week', 'is', null)
            .limit(5);

          results.class_12A_schedules = {
            success: !class12AError,
            error: class12AError?.message,
            count: class12ASchedules?.length || 0,
            classId: class12A.id,
            className: class12A.name,
            sample: class12ASchedules?.[0],
            allSamples: class12ASchedules
          };
        }

        // Find class 11.A specifically
        const class11A = classesData.find(c => c.name === '11.A');

        if (class11A) {
          console.log('🔍 Testing class 11.A schedules...');
          const { data: class11ASchedules, error: class11AError } = await supabase
            .from('schedules_view')
            .select('*')
            .eq('class_id', class11A.id)
            .not('day_of_week', 'is', null)
            .limit(5);

          results.class_11A_schedules = {
            success: !class11AError,
            error: class11AError?.message,
            count: class11ASchedules?.length || 0,
            classId: class11A.id,
            className: class11A.name,
            sample: class11ASchedules?.[0],
            allSamples: class11ASchedules
          };
        }
      }

      // Test 6: Check session categories
      console.log('🔍 Testing session_categories...');
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('session_categories')
        .select('*')
        .limit(10);

      results.session_categories = {
        success: !categoriesError,
        error: categoriesError?.message,
        count: categoriesData?.length || 0,
        data: categoriesData
      };

      // Test 7: Check user profile
      results.profile = {
        user_id: profile?.id,
        school_id: profile?.school_id,
        role: profile?.role
      };

      setDebugInfo(results);
      console.log('🔍 Debug results:', results);

    } catch (error) {
      console.error('❌ Debug test error:', error);
      results.error = error;
      setDebugInfo(results);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="bg-gray-800/20 backdrop-blur-sm border-gray-700/30">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          🔧 Merge Schedule Debug
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={runDatabaseTest}
          disabled={isLoading}
          className="bg-blue-500 hover:bg-blue-600"
        >
          {isLoading ? 'Testing...' : 'Run Database Test'}
        </Button>

        {debugInfo && (
          <div className="space-y-4">
            <div className="bg-gray-900/50 p-4 rounded-lg">
              <h3 className="text-white font-semibold mb-2">Test Results:</h3>
              <pre className="text-xs text-gray-300 overflow-auto max-h-96">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-900/50 p-3 rounded">
                <h4 className="text-white font-medium">schedules_view</h4>
                <p className={`text-sm ${debugInfo.schedules_view?.success ? 'text-green-400' : 'text-red-400'}`}>
                  {debugInfo.schedules_view?.success ? '✅ Success' : '❌ Failed'}
                </p>
                <p className="text-gray-400 text-xs">Count: {debugInfo.schedules_view?.count}</p>
              </div>

              <div className="bg-gray-900/50 p-3 rounded">
                <h4 className="text-white font-medium">class_schedules</h4>
                <p className={`text-sm ${debugInfo.class_schedules_table?.success ? 'text-green-400' : 'text-red-400'}`}>
                  {debugInfo.class_schedules_table?.success ? '✅ Success' : '❌ Failed'}
                </p>
                <p className="text-gray-400 text-xs">Count: {debugInfo.class_schedules_table?.count}</p>
              </div>

              <div className="bg-gray-900/50 p-3 rounded">
                <h4 className="text-white font-medium">schedules table</h4>
                <p className={`text-sm ${debugInfo.schedules_table?.success ? 'text-green-400' : 'text-red-400'}`}>
                  {debugInfo.schedules_table?.success ? '✅ Success' : '❌ Failed'}
                </p>
                <p className="text-gray-400 text-xs">Count: {debugInfo.schedules_table?.count}</p>
              </div>

              <div className="bg-gray-900/50 p-3 rounded">
                <h4 className="text-white font-medium">classes</h4>
                <p className={`text-sm ${debugInfo.classes?.success ? 'text-green-400' : 'text-red-400'}`}>
                  {debugInfo.classes?.success ? '✅ Success' : '❌ Failed'}
                </p>
                <p className="text-gray-400 text-xs">Count: {debugInfo.classes?.count}</p>
              </div>

              <div className="bg-gray-900/50 p-3 rounded">
                <h4 className="text-white font-medium">categories</h4>
                <p className={`text-sm ${debugInfo.session_categories?.success ? 'text-green-400' : 'text-red-400'}`}>
                  {debugInfo.session_categories?.success ? '✅ Success' : '❌ Failed'}
                </p>
                <p className="text-gray-400 text-xs">Count: {debugInfo.session_categories?.count}</p>
              </div>

              {debugInfo.class_12A_schedules && (
                <div className="bg-gray-900/50 p-3 rounded">
                  <h4 className="text-white font-medium">Class 12.A</h4>
                  <p className={`text-sm ${debugInfo.class_12A_schedules?.success ? 'text-green-400' : 'text-red-400'}`}>
                    {debugInfo.class_12A_schedules?.success ? '✅ Success' : '❌ Failed'}
                  </p>
                  <p className="text-gray-400 text-xs">
                    {debugInfo.class_12A_schedules?.count} schedules found
                  </p>
                  {debugInfo.class_12A_schedules?.sample && (
                    <p className="text-gray-400 text-xs">
                      Sample: {debugInfo.class_12A_schedules.sample.subject_name}
                    </p>
                  )}
                </div>
              )}

              {debugInfo.class_11A_schedules && (
                <div className="bg-gray-900/50 p-3 rounded">
                  <h4 className="text-white font-medium">Class 11.A</h4>
                  <p className={`text-sm ${debugInfo.class_11A_schedules?.success ? 'text-green-400' : 'text-red-400'}`}>
                    {debugInfo.class_11A_schedules?.success ? '✅ Success' : '❌ Failed'}
                  </p>
                  <p className="text-gray-400 text-xs">
                    {debugInfo.class_11A_schedules?.count} schedules found
                  </p>
                  {debugInfo.class_11A_schedules?.sample && (
                    <p className="text-gray-400 text-xs">
                      Sample: {debugInfo.class_11A_schedules.sample.subject_name}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
