/**
 * Utility functions for time calculations in the scheduling system
 */

/**
 * Calculate duration in minutes between start and end time
 * Handles midnight crossing scenarios
 */
export const calculateDurationMinutes = (startTime: string, endTime: string): number => {
  console.log('🔍 calculateDurationMinutes input:', {
    startTime,
    endTime,
    startType: typeof startTime,
    endType: typeof endTime,
    startLength: startTime?.length,
    endLength: endTime?.length
  });

  if (!startTime || !endTime || startTime.trim() === '' || endTime.trim() === '') {
    console.log('❌ Empty or invalid time values');
    return 0;
  }

  try {
    // Clean and validate input
    const cleanStart = startTime.trim();
    const cleanEnd = endTime.trim();

    console.log('🧹 Cleaned times:', { cleanStart, cleanEnd });

    // Parse time manually to be more robust - handle both HH:MM and HH:MM:SS formats
    const parseTime = (timeStr: string) => {
      console.log('🔍 Parsing time:', timeStr);

      // Handle different time formats
      let cleanTimeStr = timeStr;

      // If it has seconds (HH:MM:SS), remove them
      if (timeStr.includes(':') && timeStr.split(':').length === 3) {
        const parts = timeStr.split(':');
        cleanTimeStr = `${parts[0]}:${parts[1]}`;
        console.log('🔍 Removed seconds, new format:', cleanTimeStr);
      }

      const parts = cleanTimeStr.split(':');
      console.log('🔍 Time parts:', parts);

      if (parts.length !== 2) {
        console.log('❌ Invalid time format - not 2 parts');
        return null;
      }

      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);

      console.log('🔍 Parsed hours and minutes:', { hours, minutes });

      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        console.log('❌ Invalid time values');
        return null;
      }

      return { hours, minutes };
    };

    const startParsed = parseTime(cleanStart);
    const endParsed = parseTime(cleanEnd);

    console.log('🔢 Parsed times:', { startParsed, endParsed });

    if (!startParsed || !endParsed) {
      console.log('❌ Failed to parse times');
      return 0;
    }

    // Convert to minutes since midnight
    const startMinutes = startParsed.hours * 60 + startParsed.minutes;
    let endMinutes = endParsed.hours * 60 + endParsed.minutes;

    console.log('⏰ Minutes since midnight:', { startMinutes, endMinutes });

    // Handle midnight crossing
    if (endMinutes <= startMinutes) {
      endMinutes += 24 * 60; // Add 24 hours
      console.log('🌙 Midnight crossing detected, adjusted endMinutes:', endMinutes);
    }

    const durationMinutes = endMinutes - startMinutes;

    console.log('⏱️ Duration calculation:', {
      startMinutes,
      endMinutes,
      durationMinutes
    });

    // Ensure duration is positive and reasonable (max 24 hours)
    const result = Math.max(0, Math.min(durationMinutes, 1440));
    console.log('✅ Final result:', result);
    return result;
  } catch (error) {
    console.error('Error calculating duration:', error);
    return 0;
  }
};

/**
 * Calculate JP (Jam Pelajaran) count based on duration and lesson duration
 */
export const calculateJP = (durationMinutes: number, lessonDurationMinutes: number = 45): number => {
  if (!durationMinutes || durationMinutes <= 0 || !lessonDurationMinutes || lessonDurationMinutes <= 0) {
    return 0;
  }
  return parseFloat((durationMinutes / lessonDurationMinutes).toFixed(2));
};

/**
 * Format duration in minutes to human readable format
 */
export const formatDuration = (minutes: number): string => {
  if (!minutes || minutes === 0 || isNaN(minutes)) return '0 menit';
  
  if (minutes < 60) {
    return `${minutes} menit`;
  } else {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return `${hours} jam`;
    } else {
      return `${hours} jam ${remainingMinutes} menit`;
    }
  }
};

/**
 * Format JP count to human readable format
 */
export const formatJP = (jp: number): string => {
  if (!jp || jp === 0 || isNaN(jp)) return '0 JP';
  // Show up to 2 decimal places, but remove trailing zeros
  const formatted = parseFloat(jp.toFixed(2));
  return `${formatted} JP`;
};

/**
 * Check if a session crosses midnight
 */
export const crossesMidnight = (startTime: string, endTime: string): boolean => {
  if (!startTime || !endTime) return false;
  return endTime <= startTime;
};

/**
 * Format time string to HH:MM format
 */
export const formatTime = (time: string): string => {
  if (!time) return '';
  return time.slice(0, 5);
};

/**
 * Validate time format (HH:MM)
 */
export const isValidTimeFormat = (time: string): boolean => {
  if (!time) return false;

  try {
    const parts = time.trim().split(':');
    if (parts.length !== 2) return false;

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    return !isNaN(hours) && !isNaN(minutes) &&
           hours >= 0 && hours <= 23 &&
           minutes >= 0 && minutes <= 59;
  } catch (error) {
    return false;
  }
};

/**
 * Calculate total duration and JP for multiple sessions
 */
export const calculateTotals = (
  sessions: Array<{ start_time: string; end_time: string }>,
  lessonDurationMinutes: number = 45
): { totalDuration: number; totalJP: number } => {
  let totalDuration = 0;
  let totalJP = 0;

  sessions.forEach(session => {
    const duration = calculateDurationMinutes(session.start_time, session.end_time);
    const jp = calculateJP(duration, lessonDurationMinutes);
    totalDuration += duration;
    totalJP += jp;
  });

  return {
    totalDuration,
    totalJP: parseFloat(totalJP.toFixed(2))
  };
};

/**
 * Test function for debugging - can be called from browser console
 */
export const testTimeCalculation = () => {
  console.log('🧪 Running time calculation tests...');

  const testCases = [
    { start: '03:00', end: '03:15', expected: 15 },
    { start: '03:15', end: '04:00', expected: 45 },
    { start: '04:00', end: '06:00', expected: 120 },
    { start: '06:00', end: '07:30', expected: 90 },
    { start: '07:30', end: '08:00', expected: 30 },
    { start: '08:00', end: '09:30', expected: 90 },
    { start: '14:15', end: '16:45', expected: 150 },
    { start: '23:30', end: '01:00', expected: 90 },
    { start: '09:00', end: '10:00', expected: 60 },
  ];

  testCases.forEach(({ start, end, expected }) => {
    const result = calculateDurationMinutes(start, end);
    const jp = calculateJP(result, 45);
    console.log(`🧪 Test: ${start} - ${end}`);
    console.log(`   Expected: ${expected} minutes`);
    console.log(`   Got: ${result} minutes`);
    console.log(`   JP: ${jp}`);
    console.log(`   Formatted: ${formatDuration(result)} | ${formatJP(jp)}`);
    console.log(`   ✅ ${result === expected ? 'PASS' : 'FAIL'}`);
    console.log('---');
  });
};

// Make it available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testTimeCalculation = testTimeCalculation;
}
