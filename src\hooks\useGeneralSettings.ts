
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

export interface GeneralSettings {
  id: string;
  school_id: string;
  academic_year_id: string;
  lesson_duration_minutes: number;
  active_academic_year_id: string | null;
  created_at: string;
  updated_at: string;
}

export const useGeneralSettings = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['general-settings'],
    queryFn: async () => {
      if (!profile?.school_id) {
        return null;
      }

      const { data, error } = await supabase
        .from('general_settings')
        .select('*')
        .eq('school_id', profile.school_id)
        .single();
      
      if (error && error.code !== 'PGRST116') throw error;
      return data as GeneralSettings | null;
    },
    enabled: !!profile?.school_id,
  });
};

export const useUpdateGeneralSettings = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async (data: Partial<GeneralSettings>) => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      // First try to update existing settings
      const { data: existingData } = await supabase
        .from('general_settings')
        .select('id')
        .eq('school_id', profile.school_id)
        .single();

      if (existingData) {
        // Update existing
        const { data: result, error } = await supabase
          .from('general_settings')
          .update(data)
          .eq('id', existingData.id)
          .select()
          .single();

        if (error) throw error;
        return result;
      } else {
        // Create new
        const { data: result, error } = await supabase
          .from('general_settings')
          .insert({
            ...data,
            school_id: profile.school_id,
            academic_year_id: data.active_academic_year_id || profile.school_id // temporary fallback
          })
          .select()
          .single();

        if (error) throw error;
        return result;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['general-settings'] });
      toast({
        title: "Berhasil",
        description: "Pengaturan berhasil disimpan",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal menyimpan pengaturan",
        variant: "destructive",
      });
      console.error('Error updating general settings:', error);
    },
  });
};
