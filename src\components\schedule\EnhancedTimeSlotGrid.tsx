
import React from 'react';
import { TimeSlotGrid } from './TimeSlotGrid';

interface EnhancedTimeSlotGridProps {
  selectedDate: Date | undefined;
  selectedWeek: number;
  selectedClassId: string | null;
  onTimeSlotClick: (timeSlot: any) => void;
  onScheduleEdit: (schedule: any) => void;
  onScheduleResize: (scheduleId: string, duration: number) => void;
}

export const EnhancedTimeSlotGrid: React.FC<EnhancedTimeSlotGridProps> = (props) => {
  return <TimeSlotGrid {...props} />;
};
