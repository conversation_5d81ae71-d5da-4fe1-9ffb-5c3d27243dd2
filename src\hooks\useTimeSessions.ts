
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const useTimeSessions = () => {
  return useQuery({
    queryKey: ['time_sessions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('time_sessions')
        .select(`
          *,
          session_categories (
            id,
            name,
            color
          )
        `)
        .order('start_time', { ascending: true });
      
      if (error) throw error;
      return data;
    },
  });
};

export const useCreateTimeSession = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (timeSessionData: any) => {
      // Get current user's school_id and active academic year
      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      const { data: activeYear } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile?.school_id)
        .eq('is_active', true)
        .single();

      // Get next session number
      const { data: existingSessions } = await supabase
        .from('time_sessions')
        .select('session_number')
        .eq('school_id', profile?.school_id)
        .order('session_number', { ascending: false })
        .limit(1);

      const nextSessionNumber = existingSessions?.length ? 
        existingSessions[0].session_number + 1 : 1;

      // Ensure duration and JP count are properly calculated and stored
      const durationMinutes = timeSessionData.durationMinutes || 0;
      const jpCount = timeSessionData.jpCount || 0;

      const { data, error } = await supabase
        .from('time_sessions')
        .insert({
          session_name: timeSessionData.sessionName,
          start_time: timeSessionData.startTime,
          end_time: timeSessionData.endTime,
          session_number: nextSessionNumber,
          duration_minutes: durationMinutes,
          jp_count: jpCount,
          category_id: timeSessionData.categoryId || null,
          school_id: profile?.school_id,
          academic_year_id: activeYear?.id,
          day_of_week: 1, // Default value since it's required but not used
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time_sessions'] });
      toast({
        title: "Berhasil",
        description: "Data sesi waktu berhasil ditambahkan",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menambahkan data sesi waktu: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateTimeSession = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...timeSessionData }: any) => {
      const updateData: any = {};

      if (timeSessionData.sessionName !== undefined) updateData.session_name = timeSessionData.sessionName;
      if (timeSessionData.startTime) updateData.start_time = timeSessionData.startTime;
      if (timeSessionData.endTime) updateData.end_time = timeSessionData.endTime;

      // Ensure duration and JP count are properly calculated and stored
      if (timeSessionData.durationMinutes !== undefined) {
        updateData.duration_minutes = timeSessionData.durationMinutes || 0;
      }
      if (timeSessionData.jpCount !== undefined) {
        updateData.jp_count = timeSessionData.jpCount || 0;
      }
      if (timeSessionData.categoryId !== undefined) updateData.category_id = timeSessionData.categoryId || null;

      const { data, error } = await supabase
        .from('time_sessions')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time_sessions'] });
      toast({
        title: "Berhasil",
        description: "Data sesi waktu berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal memperbarui data sesi waktu: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteTimeSession = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('time_sessions')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time_sessions'] });
      toast({
        title: "Berhasil",
        description: "Data sesi waktu berhasil dihapus",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menghapus data sesi waktu: " + error.message,
        variant: "destructive",
      });
    },
  });
};
