import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface MergeByCategoryParams {
  sourceClassId: string;
  targetClassId: string;
  categories: string[];
  overwriteConflicts: boolean;
}

interface MergeAllParams {
  sourceClassId: string;
  targetClassId: string;
  overwriteConflicts: boolean;
}

interface ConflictInfo {
  schedule: any;
  conflictTime: string;
  existingSchedule: any;
}

export const useMergeSchedule = (sourceClassId?: string, targetClassId?: string) => {
  const queryClient = useQueryClient();

  // Get all classes for selection
  const { data: classes = [] } = useQuery({
    queryKey: ['classes'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('classes')
        .select('id, name')
        .order('name');

      if (error) throw error;
      return data;
    }
  });

  // Get source schedules for preview
  const { data: sourceSchedules = [] } = useQuery({
    queryKey: ['source-schedules', sourceClassId],
    queryFn: async () => {
      if (!sourceClassId) return [];

      console.log('🔍 Fetching source schedules for class:', sourceClassId);

      // Try schedules_view first, then fallback to schedules table
      let { data, error } = await supabase
        .from('schedules_view')
        .select('*')
        .eq('class_id', sourceClassId)
        .not('day_of_week', 'is', null);

      if (error) {
        console.log('⚠️ schedules_view failed, trying schedules table:', error);

        const result = await supabase
          .from('class_schedules')
          .select(`
            *,
            schedule_subjects (
              id,
              name,
              session_category_id,
              session_categories (id, name)
            )
          `)
          .eq('class_id', sourceClassId)
          .not('day_of_week', 'is', null);

        data = result.data;
        error = result.error;
      }

      if (error) {
        console.error('❌ Error fetching source schedules:', error);
        throw error;
      }

      console.log('✅ Found source schedules:', data?.length || 0);
      return data || [];
    },
    enabled: !!sourceClassId
  });

  // Get target schedules for conflict checking
  const { data: targetSchedules = [] } = useQuery({
    queryKey: ['target-schedules', targetClassId],
    queryFn: async () => {
      if (!targetClassId) return [];

      console.log('🔍 Fetching target schedules for class:', targetClassId);

      // Try schedules_view first, then fallback to schedules table
      let { data, error } = await supabase
        .from('schedules_view')
        .select('*')
        .eq('class_id', targetClassId)
        .not('day_of_week', 'is', null);

      if (error) {
        console.log('⚠️ schedules_view failed, trying schedules table:', error);

        const result = await supabase
          .from('class_schedules')
          .select(`
            *,
            schedule_subjects (id, name)
          `)
          .eq('class_id', targetClassId)
          .not('day_of_week', 'is', null);

        data = result.data;
        error = result.error;
      }

      if (error) {
        console.error('❌ Error fetching target schedules:', error);
        throw error;
      }

      console.log('✅ Found target schedules:', data?.length || 0);
      return data || [];
    },
    enabled: !!targetClassId
  });

  // Merge schedules by category
  const mergeByCategory = useMutation({
    mutationFn: async ({ sourceClassId, targetClassId, categories, overwriteConflicts }: MergeByCategoryParams) => {
      console.log('🔄 Starting merge by category:', { sourceClassId, targetClassId, categories, overwriteConflicts });

      // Get schedules from source class for selected categories
      let { data: sourceSchedules, error: sourceError } = await supabase
        .from('schedules_view')
        .select('*')
        .eq('class_id', sourceClassId)
        .not('day_of_week', 'is', null);

      if (sourceError) {
        console.log('⚠️ schedules_view failed, trying schedules table:', sourceError);

        const result = await supabase
          .from('class_schedules')
          .select(`
            *,
            schedule_subjects (
              id,
              name,
              session_category_id,
              session_categories (id, name)
            )
          `)
          .eq('class_id', sourceClassId)
          .not('day_of_week', 'is', null);

        sourceSchedules = result.data;
        sourceError = result.error;
      }

      if (sourceError) {
        console.error('❌ Error fetching source schedules:', sourceError);
        throw sourceError;
      }

      if (!sourceSchedules || sourceSchedules.length === 0) {
        throw new Error('No schedules found in source class');
      }

      console.log('✅ Found source schedules:', sourceSchedules.length);

      // Filter schedules by selected categories
      const filteredSchedules = sourceSchedules.filter(schedule => {
        // Check session_category_id from schedules_view or schedule_subjects relation
        const categoryId = schedule.session_category_id ||
                          schedule.schedule_subjects?.session_category_id;

        console.log('🔍 Checking schedule category:', {
          scheduleId: schedule.id,
          categoryId,
          selectedCategories: categories
        });

        return categoryId && categories.includes(categoryId);
      });

      console.log('🔍 Filtered schedules by categories:', {
        total: sourceSchedules.length,
        filtered: filteredSchedules.length,
        categories,
        sampleSourceSchedule: sourceSchedules[0],
        sampleFilteredSchedule: filteredSchedules[0]
      });

      if (filteredSchedules.length === 0) {
        throw new Error('No schedules found in source class for selected categories');
      }

      // Check for conflicts if not overwriting
      if (!overwriteConflicts) {
        const conflicts = await checkConflictsForSchedules(filteredSchedules, targetClassId);
        if (conflicts.length > 0) {
          throw new Error(`Found ${conflicts.length} conflicts. Enable overwrite to proceed.`);
        }
      }

      // If overwriting, delete conflicting schedules first
      if (overwriteConflicts) {
        console.log('🗑️ Deleting conflicting schedules...');
        for (const schedule of filteredSchedules) {
          await supabase
            .from('class_schedules')
            .delete()
            .eq('class_id', targetClassId)
            .eq('day_of_week', schedule.day_of_week)
            .eq('start_time', schedule.start_time)
            .eq('end_time', schedule.end_time);
        }
      }

      // Copy schedules to target class
      const schedulesToInsert = filteredSchedules.map(schedule => ({
        class_id: targetClassId,
        subject_id: schedule.subject_id,
        day_of_week: schedule.day_of_week,
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        room: schedule.room,
        academic_week: schedule.academic_week,
        teacher_id: schedule.teacher_id,
        time_session_id: schedule.time_session_id,
        schedule_date: schedule.schedule_date,
        notes: schedule.notes,
        tujuan_pembelajaran: schedule.tujuan_pembelajaran || schedule.learning_objectives,
        materi_pembelajaran: schedule.materi_pembelajaran || schedule.learning_materials,
        school_id: schedule.school_id,
        academic_year_id: schedule.academic_year_id,
        hours_per_week: schedule.hours_per_week || 0,
        hours_per_year: schedule.hours_per_year || 0
      }));

      console.log('📝 Inserting schedules:', schedulesToInsert.length);
      console.log('📝 Sample schedule to insert:', schedulesToInsert[0]);

      // ✅ CRITICAL FIX: Use class_schedules table (same as schedules_view source)
      const { data, error } = await supabase
        .from('class_schedules')
        .insert(schedulesToInsert);

      if (error) {
        console.error('❌ Error inserting schedules:', error);
        console.error('❌ Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      console.log('✅ Successfully merged schedules');
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      queryClient.invalidateQueries({ queryKey: ['schedules', variables.targetClassId] });

      // ✅ FIXED: Invalidate JP progress queries for real-time updates
      queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
      queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

      toast.success(`Jadwal berhasil di-merge dari ${variables.categories.length} kategori`);
    },
    onError: (error) => {
      console.error('Merge by category error:', error);
      toast.error('Gagal merge jadwal per kategori');
    }
  });

  // Merge all schedules
  const mergeAll = useMutation({
    mutationFn: async ({ sourceClassId, targetClassId, overwriteConflicts }: MergeAllParams) => {
      console.log('🔄 Starting merge all schedules:', { sourceClassId, targetClassId, overwriteConflicts });

      // Get all schedules from source class
      let { data: sourceSchedules, error: sourceError } = await supabase
        .from('schedules_view')
        .select('*')
        .eq('class_id', sourceClassId)
        .not('day_of_week', 'is', null);

      if (sourceError) {
        console.log('⚠️ schedules_view failed, trying schedules table:', sourceError);

        const result = await supabase
          .from('class_schedules')
          .select(`
            *,
            schedule_subjects (
              id,
              name,
              session_category_id,
              session_categories (id, name)
            )
          `)
          .eq('class_id', sourceClassId)
          .not('day_of_week', 'is', null);

        sourceSchedules = result.data;
        sourceError = result.error;
      }

      if (sourceError) {
        console.error('❌ Error fetching source schedules:', sourceError);
        throw sourceError;
      }

      if (!sourceSchedules || sourceSchedules.length === 0) {
        throw new Error('No schedules found in source class');
      }

      console.log('✅ Found source schedules for merge all:', sourceSchedules.length);

      // Check for conflicts if not overwriting
      if (!overwriteConflicts) {
        const conflicts = await checkConflictsForSchedules(sourceSchedules, targetClassId);
        if (conflicts.length > 0) {
          throw new Error(`Found ${conflicts.length} conflicts. Enable overwrite to proceed.`);
        }
      }

      // If overwriting, delete all existing schedules in target class
      if (overwriteConflicts) {
        console.log('🗑️ Deleting all existing schedules in target class...');
        await supabase
          .from('class_schedules')
          .delete()
          .eq('class_id', targetClassId);
      }

      // Copy all schedules to target class
      const schedulesToInsert = sourceSchedules.map(schedule => ({
        class_id: targetClassId,
        subject_id: schedule.subject_id,
        day_of_week: schedule.day_of_week,
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        room: schedule.room,
        academic_week: schedule.academic_week,
        teacher_id: schedule.teacher_id,
        time_session_id: schedule.time_session_id,
        schedule_date: schedule.schedule_date,
        notes: schedule.notes,
        tujuan_pembelajaran: schedule.tujuan_pembelajaran || schedule.learning_objectives,
        materi_pembelajaran: schedule.materi_pembelajaran || schedule.learning_materials,
        school_id: schedule.school_id,
        academic_year_id: schedule.academic_year_id,
        hours_per_week: schedule.hours_per_week || 0,
        hours_per_year: schedule.hours_per_year || 0
      }));

      console.log('📝 Inserting all schedules:', schedulesToInsert.length);
      console.log('📝 Sample schedule to insert:', schedulesToInsert[0]);

      // ✅ CRITICAL FIX: Use class_schedules table (same as schedules_view source)
      const { data, error } = await supabase
        .from('class_schedules')
        .insert(schedulesToInsert);

      if (error) {
        console.error('❌ Error inserting all schedules:', error);
        console.error('❌ Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      console.log('✅ Successfully merged all schedules');
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      queryClient.invalidateQueries({ queryKey: ['schedules', variables.targetClassId] });

      // ✅ FIXED: Invalidate JP progress queries for real-time updates
      queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
      queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

      toast.success(`Semua jadwal berhasil di-merge`);
    },
    onError: (error) => {
      console.error('Merge all error:', error);
      toast.error('Gagal merge semua jadwal');
    }
  });

  // Helper function to check conflicts
  const checkConflictsForSchedules = async (schedules: any[], targetClassId: string): Promise<ConflictInfo[]> => {
    const conflicts: ConflictInfo[] = [];

    for (const schedule of schedules) {
      // Try schedules_view first, then fallback to schedules table
      let { data: existingSchedules, error } = await supabase
        .from('schedules_view')
        .select('*')
        .eq('class_id', targetClassId)
        .eq('day_of_week', schedule.day_of_week)
        .eq('start_time', schedule.start_time)
        .eq('end_time', schedule.end_time);

      if (error) {
        const result = await supabase
          .from('class_schedules')
          .select(`
            *,
            schedule_subjects (id, name)
          `)
          .eq('class_id', targetClassId)
          .eq('day_of_week', schedule.day_of_week)
          .eq('start_time', schedule.start_time)
          .eq('end_time', schedule.end_time);

        existingSchedules = result.data;
        error = result.error;
      }

      if (error) continue;

      if (existingSchedules && existingSchedules.length > 0) {
        conflicts.push({
          schedule,
          conflictTime: `${schedule.start_time}-${schedule.end_time}`,
          existingSchedule: existingSchedules[0]
        });
      }
    }

    return conflicts;
  };

  // Get preview schedules for merge
  const getPreviewSchedules = (
    classId: string,
    categories: string[],
    type: 'category' | 'all'
  ) => {
    if (!sourceSchedules || !classId) return [];

    // If type is category and categories are specified, filter by categories
    if (type === 'category' && categories.length > 0) {
      return sourceSchedules.filter(schedule => {
        const categoryId = schedule.session_category_id ||
                          schedule.schedule_subjects?.session_category_id;
        return categoryId && categories.includes(categoryId);
      });
    }

    // For 'all' type, return all schedules
    return sourceSchedules;
  };

  // Check merge conflicts
  const checkMergeConflicts = (schedules: any[], classId: string): ConflictInfo[] => {
    if (!targetSchedules || !schedules || !classId) return [];

    const conflicts: ConflictInfo[] = [];

    schedules.forEach(schedule => {
      const conflictingSchedule = targetSchedules.find(target =>
        target.day_of_week === schedule.day_of_week &&
        target.academic_week === schedule.academic_week &&
        (
          (schedule.start_time >= target.start_time && schedule.start_time < target.end_time) ||
          (schedule.end_time > target.start_time && schedule.end_time <= target.end_time) ||
          (schedule.start_time <= target.start_time && schedule.end_time >= target.end_time)
        )
      );

      if (conflictingSchedule) {
        conflicts.push({
          schedule,
          conflictTime: `${schedule.start_time}-${schedule.end_time}`,
          existingSchedule: conflictingSchedule
        });
      }
    });

    return conflicts;
  };

  const isLoading = mergeByCategory.isPending || mergeAll.isPending;

  return {
    mergeByCategory,
    mergeAll,
    getPreviewSchedules,
    checkMergeConflicts,
    classes,
    isLoading
  };
};
