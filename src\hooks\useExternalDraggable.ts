
import { useEffect, useRef } from 'react';
import { Draggable } from '@fullcalendar/interaction';

interface UseExternalDraggableProps {
  containerSelector: string;
  itemSelector: string;
  enabled?: boolean;
}

export const useExternalDraggable = ({ 
  containerSelector, 
  itemSelector, 
  enabled = true 
}: UseExternalDraggableProps) => {
  const draggableRef = useRef<Draggable | null>(null);

  useEffect(() => {
    if (!enabled) return;

    const containerEl = document.querySelector(containerSelector);
    if (!containerEl) return;

    // Initialize FullCalendar's Draggable
    draggableRef.current = new Draggable(containerEl, {
      itemSelector,
      eventData: function(eventEl) {
        const subjectData = eventEl.dataset.subject;
        if (subjectData) {
          const subject = JSON.parse(subjectData);
          return {
            title: subject.name,
            id: subject.id,
            duration: '00:45:00', // ✅ CONSISTENT: 45 minutes default duration (9 x 5-minute intervals)
            extendedProps: {
              subject: subject,
              type: 'external'
            }
          };
        }
        return {
          title: eventEl.textContent || 'Unknown Subject',
          duration: '00:45:00' // ✅ CONSISTENT: 45 minutes default duration
        };
      }
    });

    return () => {
      if (draggableRef.current) {
        draggableRef.current.destroy();
      }
    };
  }, [containerSelector, itemSelector, enabled]);

  return draggableRef.current;
};
