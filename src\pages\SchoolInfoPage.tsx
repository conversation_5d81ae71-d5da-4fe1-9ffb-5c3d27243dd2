import React, { useState, useRef } from 'react';
import { School, Save, Upload, Loader2, MapPin, Phone, Mail, User, Building } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useSchools, useUpdateSchool, useUploadSchoolLogo } from '@/hooks/useSchools';
import { toast } from '@/hooks/use-toast';
const SchoolInfoPage: React.FC = () => {
  const {
    data: school,
    isLoading: isLoadingSchool
  } = useSchools();
  const updateSchool = useUpdateSchool();
  const uploadLogo = useUploadSchoolLogo();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [formData, setFormData] = useState({
    name: '',
    principal_name: '',
    address: '',
    phone: '',
    email: ''
  });
  const [previewLogo, setPreviewLogo] = useState<string | null>(null);

  // Update form data when school data is loaded
  React.useEffect(() => {
    if (school) {
      setFormData({
        name: school.name || '',
        principal_name: school.principal_name || '',
        address: school.address || '',
        phone: school.phone || '',
        email: school.email || ''
      });
    }
  }, [school]);
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Error",
        description: "Format file harus PNG, JPG, atau JPEG",
        variant: "destructive"
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Error",
        description: "Ukuran file maksimal 5MB",
        variant: "destructive"
      });
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewLogo(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Upload file
    uploadLogo.mutate(file, {
      onSuccess: () => {
        setPreviewLogo(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      },
      onError: () => {
        setPreviewLogo(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    });
  };
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };
  const handleSaveChanges = () => {
    updateSchool.mutate(formData);
  };
  const currentLogo = previewLogo || school?.logo_url;
  const isSaving = updateSchool.isPending;
  const isUploading = uploadLogo.isPending;
  if (isLoadingSchool) {
    return <div className="min-h-screen bg-background p-6 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-cyan-400 mx-auto mb-4" />
          <p className="text-muted-foreground">Memuat data sekolah...</p>
        </div>
      </div>;
  }
  return <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Enhanced Header Section with icons */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center space-x-2">
                <School className="h-8 w-8 text-cyan-400" />
                <h1 className="text-3xl font-bold text-foreground">Info Sekolah</h1>
              </div>
            </div>
            <p className="text-muted-foreground">Kelola informasi dan identitas sekolah</p>
          </div>
          <Button onClick={handleSaveChanges} disabled={isSaving} className="bg-cyan-500 hover:bg-cyan-600 text-gray-900 font-semibold shadow-lg transition-all duration-300 transform hover:scale-105">
            {isSaving ? <Loader2 className="h-6 w-6 mr-3 animate-spin" /> : <Save className="h-6 w-6 mr-3" />}
            Simpan Perubahan
          </Button>
        </div>

        {/* Info Overview Cards */}
        

        {/* School Info Form */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Enhanced Logo Section */}
          <Card className="bg-card backdrop-blur-sm border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground flex items-center text-xl">
                <Upload className="h-6 w-6 text-cyan-400 mr-3" />
                Logo Sekolah
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Upload logo sekolah (PNG, JPG, JPEG - Max 5MB)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col items-center space-y-4">
                <div className="w-36 h-36 bg-muted/50 rounded-xl flex items-center justify-center overflow-hidden border border-border backdrop-blur-sm">
                  {currentLogo ? <img src={currentLogo} alt="Logo Sekolah" className="w-full h-full object-cover rounded-xl" /> : <School className="h-20 w-20 text-cyan-400" />}
                </div>
                <input type="file" ref={fileInputRef} onChange={handleFileSelect} accept="image/*" className="hidden" />
                <Button variant="outline" onClick={handleUploadClick} disabled={isUploading} className="bg-cyan-400/10 border border-cyan-400/20 text-cyan-400 hover:bg-cyan-400/20 flex items-center justify-center space-x-2">
                  {isUploading ? <Loader2 className="h-5 w-5 mr-2 animate-spin" /> : <Upload className="h-5 w-5 mr-2" />}
                  {isUploading ? 'Mengupload...' : 'Upload Logo'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Basic Info */}
          <Card className="lg:col-span-2 bg-card backdrop-blur-sm border-border">
            <CardHeader>
              <CardTitle className="text-card-foreground flex items-center text-xl">
                <Building className="h-6 w-6 text-lime-400 mr-3" />
                Informasi Dasar
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Data dasar sekolah dan informasi kontak
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="school-name" className="text-foreground flex items-center mb-2">
                    <School className="h-4 w-4 text-lime-400 mr-2" />
                    Nama Sekolah
                  </Label>
                  <Input id="school-name" value={formData.name} onChange={e => handleInputChange('name', e.target.value)} placeholder="Masukkan nama sekolah" className="bg-background border-lime-400/30 text-foreground placeholder-muted-foreground focus:border-lime-400/50 focus:ring-lime-400/20 focus:ring-2 backdrop-blur-sm h-12" />
                </div>
                <div>
                  <Label htmlFor="principal-name" className="text-foreground flex items-center mb-2">
                    <User className="h-4 w-4 text-cyan-400 mr-2" />
                    Nama Kepala Sekolah
                  </Label>
                  <Input id="principal-name" value={formData.principal_name} onChange={e => handleInputChange('principal_name', e.target.value)} placeholder="Masukkan nama kepala sekolah" className="bg-background border-cyan-400/30 text-foreground placeholder-muted-foreground focus:border-cyan-400/50 focus:ring-cyan-400/20 focus:ring-2 backdrop-blur-sm h-12" />
                </div>
              </div>
              <div>
                <Label htmlFor="address" className="text-foreground flex items-center mb-2">
                  <MapPin className="h-4 w-4 text-blue-400 mr-2" />
                  Alamat
                </Label>
                <Textarea id="address" value={formData.address} onChange={e => handleInputChange('address', e.target.value)} placeholder="Masukkan alamat lengkap" className="bg-background border-blue-400/30 text-foreground placeholder-muted-foreground focus:border-blue-400/50 focus:ring-blue-400/20 focus:ring-2 backdrop-blur-sm" rows={3} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="phone" className="text-foreground flex items-center mb-2">
                    <Phone className="h-4 w-4 text-purple-400 mr-2" />
                    Nomor Telepon
                  </Label>
                  <Input id="phone" value={formData.phone} onChange={e => handleInputChange('phone', e.target.value)} placeholder="Masukkan nomor telepon" className="bg-background border-purple-400/30 text-foreground placeholder-muted-foreground focus:border-purple-400/50 focus:ring-purple-400/20 focus:ring-2 backdrop-blur-sm h-12" />
                </div>
                <div>
                  <Label htmlFor="email" className="text-foreground flex items-center mb-2">
                    <Mail className="h-4 w-4 text-pink-400 mr-2" />
                    Email
                  </Label>
                  <Input id="email" type="email" value={formData.email} onChange={e => handleInputChange('email', e.target.value)} placeholder="Masukkan email" className="bg-background border-pink-400/30 text-foreground placeholder-muted-foreground focus:border-pink-400/50 focus:ring-pink-400/20 focus:ring-2 backdrop-blur-sm h-12" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>


      </div>
    </div>;
};
export default SchoolInfoPage;