
import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUpdateExtracurricularClass, useDeleteExtracurricularClass } from '@/hooks/useExtracurricularClasses';

interface UpdateExtracurricularModalProps {
  isOpen: boolean;
  onClose: () => void;
  extracurricularClass: any;
  extracurricularName: string;
  className: string;
}

export const UpdateExtracurricularModal: React.FC<UpdateExtracurricularModalProps> = ({
  isOpen,
  onClose,
  extracurricularClass,
  extracurricularName,
  className
}) => {
  const [hoursPerWeek, setHoursPerWeek] = useState(1);
  const updateExtracurricularClass = useUpdateExtracurricularClass();
  const deleteExtracurricularClass = useDeleteExtracurricularClass();

  useEffect(() => {
    if (extracurricularClass) {
      setHoursPerWeek(extracurricularClass.hours_per_week);
    }
  }, [extracurricularClass]);

  const handleUpdate = () => {
    if (extracurricularClass) {
      updateExtracurricularClass.mutate({
        id: extracurricularClass.id,
        hours_per_year: hoursPerWeek
      });
      onClose();
    }
  };

  const handleDelete = () => {
    if (extracurricularClass) {
      deleteExtracurricularClass.mutate(extracurricularClass.id);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            Update Ekstrakurikuler
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label>Ekstrakurikuler</Label>
            <div className="mt-1 p-2 rounded-md border bg-muted text-muted-foreground">
              {extracurricularName}
            </div>
          </div>
          
          <div>
            <Label>Kelas</Label>
            <div className="mt-1 p-2 rounded-md border bg-muted text-muted-foreground">
              {className}
            </div>
          </div>
          
          <div>
            <Label htmlFor="hoursPerWeek">
              Jam Pelajaran per Minggu
            </Label>
            <Input
              id="hoursPerWeek"
              type="number"
              min="1"
              value={hoursPerWeek}
              onChange={(e) => setHoursPerWeek(parseInt(e.target.value) || 1)}
              className="mt-1"
            />
          </div>
          
          <div className="flex justify-between space-x-3 pt-4">
            <Button
              onClick={handleDelete}
              variant="destructive"
            >
              Hapus
            </Button>
            <div className="flex space-x-2">
              <Button
                onClick={onClose}
                variant="outline"
              >
                Batal
              </Button>
              <Button
                onClick={handleUpdate}
              >
                Update
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
