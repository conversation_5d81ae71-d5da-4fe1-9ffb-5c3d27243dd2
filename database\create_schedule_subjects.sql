-- =====================================================
-- CREATE NEW SCHEDULE SUBJECTS TABLES
-- =====================================================
-- Tabel baru untuk menggantikan subjects yang bermasalah
-- dengan struktur yang lebih sederhana dan fleksibel

-- 1. Tabel utama: schedule_subjects
CREATE TABLE schedule_subjects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  code TEXT, -- Optional, bisa kosong untuk menghindari constraint issues
  color TEXT NOT NULL DEFAULT '#6B7280',
  total_hours_per_year INTEGER DEFAULT 0,
  standard_duration INTEGER DEFAULT 45, -- dalam menit
  
  -- <PERSON><PERSON><PERSON> dengan tabel yang sudah ada
  session_category_id UUID REFERENCES session_categories(id) ON DELETE SET NULL,
  school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  academic_year_id UUID NOT NULL REFERENCES academic_years(id) ON DELETE CASCADE,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraint yang sederhana: nama unik per kategori per sekolah per tahun
  UNIQUE(name, session_category_id, school_id, academic_year_id)
);

-- 2. Tabel relasi: schedule_class_subjects  
CREATE TABLE schedule_class_subjects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
  schedule_subject_id UUID NOT NULL REFERENCES schedule_subjects(id) ON DELETE CASCADE,
  hours_per_week INTEGER DEFAULT 0,
  hours_per_year INTEGER DEFAULT 0,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraint: satu subject per kelas
  UNIQUE(class_id, schedule_subject_id)
);

-- 3. Indexes untuk performance
CREATE INDEX idx_schedule_subjects_school_year ON schedule_subjects(school_id, academic_year_id);
CREATE INDEX idx_schedule_subjects_category ON schedule_subjects(session_category_id);
CREATE INDEX idx_schedule_class_subjects_class ON schedule_class_subjects(class_id);
CREATE INDEX idx_schedule_class_subjects_subject ON schedule_class_subjects(schedule_subject_id);

-- 4. RLS Policies
ALTER TABLE schedule_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_class_subjects ENABLE ROW LEVEL SECURITY;

-- Policy untuk schedule_subjects
CREATE POLICY "Users can view schedule_subjects from their school" ON schedule_subjects
  FOR SELECT USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Admin can manage schedule_subjects" ON schedule_subjects
  FOR ALL USING (
    school_id = get_user_school_id(auth.uid()) AND 
    (
      has_role(auth.uid(), 'superadmin'::user_role) OR 
      has_role(auth.uid(), 'kepsek'::user_role) OR 
      has_role(auth.uid(), 'kesiswaan'::user_role)
    )
  );

-- Policy untuk schedule_class_subjects
CREATE POLICY "Users can view schedule_class_subjects from their school" ON schedule_class_subjects
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM classes c 
      WHERE c.id = schedule_class_subjects.class_id 
      AND c.school_id = get_user_school_id(auth.uid())
    )
  );

CREATE POLICY "Admin can manage schedule_class_subjects" ON schedule_class_subjects
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM classes c 
      WHERE c.id = schedule_class_subjects.class_id 
      AND c.school_id = get_user_school_id(auth.uid()) 
      AND (
        has_role(auth.uid(), 'superadmin'::user_role) OR 
        has_role(auth.uid(), 'kepsek'::user_role) OR 
        has_role(auth.uid(), 'kesiswaan'::user_role)
      )
    )
  );

-- 5. Trigger untuk updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_schedule_subjects_updated_at 
  BEFORE UPDATE ON schedule_subjects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedule_class_subjects_updated_at 
  BEFORE UPDATE ON schedule_class_subjects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- CREATE SEMESTERS TABLE
-- =====================================================

-- Tabel semester untuk sistem pendidikan Indonesia
CREATE TABLE semesters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL, -- 'Semester 1', 'Semester 2'
  semester_number INTEGER NOT NULL CHECK (semester_number IN (1, 2)),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT FALSE,

  -- Relasi
  academic_year_id UUID NOT NULL REFERENCES academic_years(id) ON DELETE CASCADE,
  school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,

  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  UNIQUE(semester_number, academic_year_id, school_id),
  CHECK (start_date < end_date)
);

-- Index untuk performance
CREATE INDEX idx_semesters_academic_year ON semesters(academic_year_id);
CREATE INDEX idx_semesters_school ON semesters(school_id);
CREATE INDEX idx_semesters_active ON semesters(is_active) WHERE is_active = true;

-- RLS Policy
ALTER TABLE semesters ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view semesters from their school" ON semesters
  FOR SELECT USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can insert semesters for their school" ON semesters
  FOR INSERT WITH CHECK (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can update semesters from their school" ON semesters
  FOR UPDATE USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can delete semesters from their school" ON semesters
  FOR DELETE USING (school_id = get_user_school_id(auth.uid()));

-- Function untuk auto-generate semester saat academic year dibuat
CREATE OR REPLACE FUNCTION create_default_semesters()
RETURNS TRIGGER AS $$
DECLARE
  semester1_start DATE;
  semester1_end DATE;
  semester2_start DATE;
  semester2_end DATE;
BEGIN
  -- Hitung tanggal semester berdasarkan tahun ajaran Indonesia
  -- Semester 1: Juli - Desember
  -- Semester 2: Januari - Juni (tahun berikutnya)

  semester1_start := NEW.start_date;
  semester1_end := DATE(EXTRACT(YEAR FROM NEW.start_date) || '-12-31');
  semester2_start := DATE((EXTRACT(YEAR FROM NEW.start_date) + 1) || '-01-01');
  semester2_end := NEW.end_date;

  -- Insert Semester 1
  INSERT INTO semesters (
    name,
    semester_number,
    start_date,
    end_date,
    academic_year_id,
    school_id,
    is_active
  ) VALUES (
    'Semester 1',
    1,
    semester1_start,
    semester1_end,
    NEW.id,
    NEW.school_id,
    NEW.is_active -- Semester 1 aktif jika tahun ajaran aktif
  );

  -- Insert Semester 2
  INSERT INTO semesters (
    name,
    semester_number,
    start_date,
    end_date,
    academic_year_id,
    school_id,
    is_active
  ) VALUES (
    'Semester 2',
    2,
    semester2_start,
    semester2_end,
    NEW.id,
    NEW.school_id,
    FALSE -- Semester 2 tidak aktif secara default
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger untuk auto-generate semester
CREATE TRIGGER trigger_create_default_semesters
  AFTER INSERT ON academic_years
  FOR EACH ROW
  EXECUTE FUNCTION create_default_semesters();

-- Function untuk update active semester
CREATE OR REPLACE FUNCTION update_active_semester()
RETURNS TRIGGER AS $$
BEGIN
  -- Jika academic year diaktifkan, aktifkan semester 1 secara default
  IF NEW.is_active = TRUE AND OLD.is_active = FALSE THEN
    -- Nonaktifkan semua semester di sekolah ini
    UPDATE semesters
    SET is_active = FALSE
    WHERE school_id = NEW.school_id;

    -- Aktifkan semester 1 dari tahun ajaran yang baru diaktifkan
    UPDATE semesters
    SET is_active = TRUE
    WHERE academic_year_id = NEW.id
      AND semester_number = 1
      AND school_id = NEW.school_id;
  END IF;

  -- Jika academic year dinonaktifkan, nonaktifkan semua semesternya
  IF NEW.is_active = FALSE AND OLD.is_active = TRUE THEN
    UPDATE semesters
    SET is_active = FALSE
    WHERE academic_year_id = NEW.id
      AND school_id = NEW.school_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger untuk update active semester
CREATE TRIGGER trigger_update_active_semester
  AFTER UPDATE ON academic_years
  FOR EACH ROW
  EXECUTE FUNCTION update_active_semester();

-- 6. Comments untuk dokumentasi
COMMENT ON TABLE semesters IS 'Tabel semester untuk sistem pendidikan Indonesia (Semester 1: Juli-Desember, Semester 2: Januari-Juni)';
COMMENT ON COLUMN semesters.semester_number IS 'Nomor semester: 1 atau 2';
COMMENT ON COLUMN semesters.is_active IS 'Semester yang sedang aktif (hanya satu per sekolah)';

-- =====================================================
-- CREATE SCHEDULES_VIEW FOR CALENDAR DISPLAY
-- =====================================================

-- Create a comprehensive view that joins schedules with subjects and categories
CREATE OR REPLACE VIEW schedules_view AS
SELECT
  s.id,
  s.academic_week,
  s.academic_year_id,
  s.class_id,
  s.created_at,
  s.day_of_week,
  s.end_time,
  s.notes,
  s.room,
  s.schedule_date,
  s.school_id,
  s.start_time,
  s.subject_id,
  s.teacher_id,
  s.time_session_id,
  s.tujuan_pembelajaran,
  s.materi_pembelajaran,
  s.updated_at,

  -- Subject information (from subjects table - legacy)
  subj.name as subject_name,
  subj.code as subject_code,
  subj.category as subject_category,
  COALESCE(subj.color, '#6B7280') as subject_color,

  -- Schedule subject information (from schedule_subjects table - new system)
  ss.name as schedule_subject_name,
  ss.code as schedule_subject_code,
  ss.color as schedule_subject_color,
  ss.total_hours_per_year as schedule_subject_hours_per_year,
  ss.standard_duration as schedule_subject_duration,

  -- Session category information
  sc.id as session_category_id,
  sc.name as session_category_name,
  sc.color as session_category_color,
  sc.description as session_category_description,

  -- Class information
  c.name as class_name,
  c.level as class_level,
  c.grade as class_grade,

  -- Teacher information
  t.full_name as teacher_name,
  t.nip as teacher_nip

FROM schedules s
LEFT JOIN subjects subj ON s.subject_id = subj.id
LEFT JOIN schedule_subjects ss ON s.subject_id = ss.id
LEFT JOIN session_categories sc ON (subj.category_id = sc.id OR ss.session_category_id = sc.id)
LEFT JOIN classes c ON s.class_id = c.id
LEFT JOIN teachers t ON s.teacher_id = t.id
ORDER BY s.created_at DESC;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_schedules_view_lookup ON schedules(subject_id, class_id, academic_week, day_of_week);

COMMENT ON VIEW schedules_view IS 'Comprehensive view for schedule display with joined subject, category, class, and teacher information';

-- =====================================================
-- TABLE COMMENTS
-- =====================================================

COMMENT ON TABLE schedule_subjects IS 'Tabel mata pelajaran untuk sistem jadwal dengan struktur yang disederhanakan';
COMMENT ON TABLE schedule_class_subjects IS 'Tabel relasi antara mata pelajaran dan kelas';
COMMENT ON COLUMN schedule_subjects.code IS 'Kode mata pelajaran (optional untuk menghindari constraint issues)';
COMMENT ON COLUMN schedule_subjects.session_category_id IS 'Relasi ke session_categories (KBM, EKSTRAKURIKULER, dll)';
COMMENT ON COLUMN schedule_subjects.total_hours_per_year IS 'Total jam pelajaran per tahun (JP/Tahun)';
COMMENT ON COLUMN schedule_subjects.standard_duration IS 'Durasi standar per sesi dalam menit';
