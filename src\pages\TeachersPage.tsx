
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Edit, Trash2, User, Users, Mail, Phone, Filter, Grid3X3, List } from 'lucide-react';
import { useTeachers, useDeleteTeacher } from '@/hooks/useTeachers';
import AddTeacherModal from '@/components/modals/AddTeacherModal';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const TeachersPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [availabilityFilter, setAvailabilityFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'card' | 'table'>('card');

  const { data: teachers, isLoading, error } = useTeachers();
  const deleteTeacher = useDeleteTeacher();

  const handleEdit = (teacher: any) => {
    setEditingTeacher(teacher);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus guru ini?')) {
      deleteTeacher.mutate(id);
    }
  };

  const filteredTeachers = teachers?.filter(teacher => {
    const matchesSearch = teacher.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         teacher.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         teacher.nip?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesAvailability = availabilityFilter === 'all' || 
                               (availabilityFilter === 'available' && teacher.is_available) ||
                               (availabilityFilter === 'unavailable' && !teacher.is_available);
    return matchesSearch && matchesAvailability;
  }) || [];

  const getAvailabilityColor = (isAvailable: boolean) => {
    return isAvailable 
      ? 'bg-green-400/10 border-green-400/20 text-green-400'
      : 'bg-red-400/10 border-red-400/20 text-red-400';
  };

  const getAvailabilityLabel = (isAvailable: boolean) => {
    return isAvailable ? 'Tersedia' : 'Tidak Tersedia';
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-purple-400" />
                <h1 className="text-3xl font-bold text-foreground">Guru</h1>
              </div>
            </div>
            <p className="text-muted-foreground">Kelola data guru dan tenaga pengajar</p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* View Toggle */}
            <div className="flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
              <Button
                variant={viewMode === 'card' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('card')}
                className={`${viewMode === 'card' ? 'bg-purple-500 text-white' : 'text-muted-foreground hover:text-foreground'}`}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('table')}
                className={`${viewMode === 'table' ? 'bg-purple-500 text-white' : 'text-muted-foreground hover:text-foreground'}`}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            <Button
              onClick={() => {
                setEditingTeacher(null);
                setIsModalOpen(true);
              }}
              className="bg-gradient-to-r from-purple-400 to-purple-600 hover:from-purple-500 hover:to-purple-700 text-white font-semibold shadow-lg shadow-purple-400/25 transition-all duration-300 transform hover:scale-105"
            >
              <Plus className="mr-2 h-5 w-5" />
              Tambah Guru
            </Button>
          </div>
        </div>

        {/* Filters Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Cari guru..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-background border-border text-foreground placeholder-muted-foreground focus:border-purple-400/50 focus:ring-purple-400/20"
            />
          </div>

          <Select value={availabilityFilter} onValueChange={setAvailabilityFilter}>
            <SelectTrigger className="bg-background border-border text-foreground">
              <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
              <SelectValue placeholder="Filter ketersediaan" />
            </SelectTrigger>
            <SelectContent className="bg-popover border-border">
              <SelectItem value="all" className="text-popover-foreground hover:bg-accent">Semua Status</SelectItem>
              <SelectItem value="available" className="text-popover-foreground hover:bg-accent">Tersedia</SelectItem>
              <SelectItem value="unavailable" className="text-popover-foreground hover:bg-accent">Tidak Tersedia</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <User className="h-4 w-4" />
            <span>Total: {filteredTeachers.length} guru</span>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="bg-destructive/10 backdrop-blur-sm border-destructive/30">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <User className="h-12 w-12 text-destructive mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Error Memuat Data</h3>
              <p className="text-destructive text-center mb-6">
                {error.message || 'Terjadi kesalahan saat memuat data guru'}
              </p>
              <Button
                onClick={() => window.location.reload()}
                className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
              >
                Muat Ulang
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Teachers Display */}
        {!error && isLoading && (
          viewMode === 'card' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="bg-card border-border animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="bg-card border-border">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="border-border">
                      <TableHead className="text-purple-400">Nama</TableHead>
                      <TableHead className="text-purple-400">NIP</TableHead>
                      <TableHead className="text-purple-400">Email</TableHead>
                      <TableHead className="text-purple-400">Telepon</TableHead>
                      <TableHead className="text-purple-400">Status</TableHead>
                      <TableHead className="text-purple-400">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Array.from({ length: 6 }).map((_, i) => (
                      <TableRow key={i} className="border-border">
                        <TableCell><div className="h-4 bg-muted rounded w-3/4 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-1/2 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-2/3 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-1/2 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-1/3 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-1/4 animate-pulse"></div></TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )
        )}

        {!error && !isLoading && (
          viewMode === 'card' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTeachers.map((teacher) => (
                <Card key={teacher.id} className="bg-card backdrop-blur-sm border-border hover:border-purple-400/30 transition-all duration-300 group">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-card-foreground text-lg font-semibold mb-1">
                          {teacher.full_name}
                        </CardTitle>
                        <p className="text-muted-foreground text-sm font-mono">
                          NIP: {teacher.nip || '-'}
                        </p>
                      </div>
                      <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(teacher)}
                          className="h-8 w-8 p-0 text-blue-400 hover:bg-blue-400/20"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(teacher.id)}
                          className="h-8 w-8 p-0 text-destructive hover:bg-destructive/20"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <Badge className={`${getAvailabilityColor(teacher.is_available)} border`}>
                        {getAvailabilityLabel(teacher.is_available)}
                      </Badge>

                      {teacher.email && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span className="text-card-foreground truncate">{teacher.email}</span>
                        </div>
                      )}

                      {teacher.phone && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="text-card-foreground">{teacher.phone}</span>
                        </div>
                      )}

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Bergabung:</span>
                        <span className="text-card-foreground">
                          {new Date(teacher.created_at).toLocaleDateString('id-ID')}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="bg-card border-border">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="border-border">
                      <TableHead className="text-purple-400">Nama</TableHead>
                      <TableHead className="text-purple-400">NIP</TableHead>
                      <TableHead className="text-purple-400">Email</TableHead>
                      <TableHead className="text-purple-400">Telepon</TableHead>
                      <TableHead className="text-purple-400">Status</TableHead>
                      <TableHead className="text-purple-400 text-center">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTeachers.map((teacher) => (
                      <TableRow key={teacher.id} className="border-border hover:bg-accent/50">
                        <TableCell className="text-foreground font-medium">{teacher.full_name}</TableCell>
                        <TableCell className="text-muted-foreground font-mono">{teacher.nip || '-'}</TableCell>
                        <TableCell className="text-muted-foreground">{teacher.email || '-'}</TableCell>
                        <TableCell className="text-muted-foreground">{teacher.phone || '-'}</TableCell>
                        <TableCell>
                          <Badge className={`${getAvailabilityColor(teacher.is_available)} border`}>
                            {getAvailabilityLabel(teacher.is_available)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-center">
                          <div className="flex justify-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(teacher)}
                              className="h-8 w-8 p-0 text-blue-400 hover:bg-blue-400/20"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(teacher.id)}
                              className="h-8 w-8 p-0 text-destructive hover:bg-destructive/20"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )
        )}

        {!error && filteredTeachers.length === 0 && !isLoading && (
          <Card className="bg-card backdrop-blur-sm border-border">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <User className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Belum ada guru</h3>
              <p className="text-muted-foreground text-center mb-6">
                {searchTerm || availabilityFilter !== 'all'
                  ? 'Tidak ditemukan guru yang sesuai dengan filter'
                  : 'Mulai dengan menambahkan guru pertama'
                }
              </p>
              {!searchTerm && availabilityFilter === 'all' && (
                <Button 
                  onClick={() => {
                    setEditingTeacher(null);
                    setIsModalOpen(true);
                  }}
                  className="bg-gradient-to-r from-purple-400 to-purple-600 hover:from-purple-500 hover:to-purple-700 text-white font-semibold"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Tambah Guru
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        <AddTeacherModal
          open={isModalOpen}
          onOpenChange={(open) => {
            setIsModalOpen(open);
            if (!open) setEditingTeacher(null);
          }}
          editingTeacher={editingTeacher}
        />
      </div>
    </div>
  );
};

export default TeachersPage;
