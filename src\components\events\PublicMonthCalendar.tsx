import React from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, getDay, startOfWeek, endOfWeek, isSameMonth, isSameDay, parseISO, isWithinInterval } from 'date-fns';
import { id } from 'date-fns/locale';
import { Holiday } from '@/types/event';
import { cn } from '@/lib/utils';

interface PublicMonthCalendarProps {
  month: Date;
  holidays: Holiday[];
}

export const PublicMonthCalendar: React.FC<PublicMonthCalendarProps> = ({
  month,
  holidays,
}) => {
  const monthStart = startOfMonth(month);
  const monthEnd = endOfMonth(month);
  const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 }); // Start on Monday
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 });
  
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });
  
  const getHolidaysForDate = (date: Date) => {
    return holidays.filter(holiday => {
      const startDate = parseISO(holiday.start_date);
      const endDate = parseISO(holiday.end_date);
      
      return isWithinInterval(date, { start: startDate, end: endDate });
    });
  };

  const monthlyEvents = holidays
    .filter(holiday => 
      isSameMonth(parseISO(holiday.start_date), month) || 
      isSameMonth(parseISO(holiday.end_date), month)
    )
    .sort((a, b) => new Date(a.start_date).getTime() - new Date(b.start_date).getTime());

  return (
    <div className="bg-card border border-lime-400/20 rounded-xl p-4 shadow-lg flex flex-col h-full">
      <h4 className="text-lg font-semibold text-center mb-3 text-card-foreground">
        {format(month, 'MMMM yyyy', { locale: id })}
      </h4>

      {/* Header dengan nama hari */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'].map(day => (
          <div key={day} className="text-xs font-medium text-muted-foreground text-center p-1">
            {day}
          </div>
        ))}
      </div>
      
      {/* Grid tanggal */}
      <div className="grid grid-cols-7 gap-1">
        {calendarDays.map((day, index) => {
          const dayHolidays = getHolidaysForDate(day);
          const isCurrentMonth = isSameMonth(day, month);
          const isToday = isSameDay(day, new Date());
          const hasEvents = dayHolidays.length > 0;

          const dayStyle = hasEvents ? { backgroundColor: dayHolidays[0].color } : {};

          return (
            <div
              key={`${format(day, 'yyyy-MM-dd')}-${index}`}
              className={cn(
                "relative h-10 w-10 text-xs rounded-full transition-colors flex items-center justify-center",
                "border border-transparent",
                isCurrentMonth ? "text-foreground" : "text-muted-foreground/50",
                isToday && !hasEvents && "bg-primary/20 border-primary/50",
                hasEvents && "text-white font-bold shadow-inner cursor-pointer hover:opacity-80",
                !hasEvents && "hover:bg-muted/50"
              )}
              style={dayStyle}
              title={hasEvents ? dayHolidays.map(h => h.name).join(', ') : ''}
            >
              {format(day, 'd')}
            </div>
          );
        })}
      </div>
      
      {/* Event List - Read Only */}
      {monthlyEvents.length > 0 && (
        <div className="mt-3 pt-3 border-t border-border/50 flex-grow min-h-0">
          <div className="space-y-1.5 max-h-28 overflow-y-auto pr-2">
            {monthlyEvents.map(event => {
              const startDate = parseISO(event.start_date);
              const endDate = parseISO(event.end_date);
              const isMultiDay = !isSameDay(startDate, endDate);

              return (
                <div 
                  key={event.id} 
                  className="flex items-start gap-2 text-xs p-1.5 rounded-md bg-muted/30"
                >
                  <div className="w-2 h-2 rounded-full mt-1 flex-shrink-0" style={{ backgroundColor: event.color }} />
                  <div className="flex-grow">
                    <span className="font-semibold">
                      {isMultiDay
                        ? `${format(startDate, 'd')} - ${format(endDate, 'd')}`
                        : format(startDate, 'd')}
                      :
                    </span>
                    <span className="ml-1.5 text-muted-foreground">{event.name}</span>
                    {event.description && (
                      <div className="text-xs text-muted-foreground/80 mt-0.5">
                        {event.description}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};