
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCreateTimeSession, useUpdateTimeSession } from '@/hooks/useTimeSessions';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useGeneralSettingsForSessions } from '@/hooks/useGeneralSettingsForSessions';
import {
  calculateDurationMinutes,
  calculateJP,
  formatDuration,
  formatJP,
  crossesMidnight,
  isValidTimeFormat
} from '@/utils/timeCalculations';

interface AddTimeSessionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingTimeSession?: any;
}

const AddTimeSessionModal: React.FC<AddTimeSessionModalProps> = ({ open, onOpenChange, editingTimeSession }) => {
  const [formData, setFormData] = useState({
    sessionName: '',
    startTime: '',
    endTime: '',
    categoryId: ''
  });

  const [calculatedData, setCalculatedData] = useState({
    durationMinutes: 0,
    jpCount: 0
  });

  const createTimeSession = useCreateTimeSession();
  const updateTimeSession = useUpdateTimeSession();
  const { data: categories } = useSessionCategories();
  const { data: generalSettings } = useGeneralSettingsForSessions();

  useEffect(() => {
    if (editingTimeSession) {
      setFormData({
        sessionName: editingTimeSession.session_name || '',
        startTime: editingTimeSession.start_time?.substring(0, 5) || '',
        endTime: editingTimeSession.end_time?.substring(0, 5) || '',
        categoryId: editingTimeSession.category_id || '',
      });
    } else {
      setFormData({
        sessionName: '',
        startTime: '',
        endTime: '',
        categoryId: ''
      });
    }
  }, [editingTimeSession, open]);

  // Validation state
  const [validationErrors, setValidationErrors] = useState<{
    startTime?: string;
    endTime?: string;
  }>({});

  // Calculate duration and JP count when times change
  useEffect(() => {
    // Validate time formats
    const errors: { startTime?: string; endTime?: string } = {};

    if (formData.startTime && !isValidTimeFormat(formData.startTime)) {
      errors.startTime = 'Format waktu tidak valid';
    }

    if (formData.endTime && !isValidTimeFormat(formData.endTime)) {
      errors.endTime = 'Format waktu tidak valid';
    }

    setValidationErrors(errors);

    // Calculate duration and JP if both times are present and valid
    if (formData.startTime && formData.endTime && Object.keys(errors).length === 0) {
      const durationMinutes = calculateDurationMinutes(formData.startTime, formData.endTime);
      const lessonDuration = generalSettings?.lesson_duration_minutes || 45;
      const jpCount = calculateJP(durationMinutes, lessonDuration);

      setCalculatedData({
        durationMinutes,
        jpCount
      });
    } else {
      setCalculatedData({
        durationMinutes: 0,
        jpCount: 0
      });
    }
  }, [formData.startTime, formData.endTime, generalSettings]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Final validation
    if (Object.keys(validationErrors).length > 0) {
      return;
    }

    if (!formData.startTime || !formData.endTime) {
      return;
    }

    const submitData = {
      sessionName: formData.sessionName,
      startTime: formData.startTime,
      endTime: formData.endTime,
      categoryId: formData.categoryId,
      durationMinutes: calculatedData.durationMinutes,
      jpCount: calculatedData.jpCount
    };

    if (editingTimeSession) {
      updateTimeSession.mutate(
        { id: editingTimeSession.id, ...submitData },
        {
          onSuccess: () => {
            onOpenChange(false);
          }
        }
      );
    } else {
      createTimeSession.mutate(submitData, {
        onSuccess: () => {
          onOpenChange(false);
        }
      });
    }
  };

  const isLoading = createTimeSession.isPending || updateTimeSession.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {editingTimeSession ? 'Edit Sesi Waktu' : 'Tambah Sesi Waktu Baru'}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="sessionName">Nama Sesi (Opsional)</Label>
            <Input
              id="sessionName"
              value={formData.sessionName}
              onChange={(e) => setFormData(prev => ({ ...prev, sessionName: e.target.value }))}
              placeholder="Contoh: Sesi 1, Persiapan Shalat Tahajud, dll"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="categoryId">Kategori Sesi*</Label>
            <Select value={formData.categoryId} onValueChange={(value) => setFormData(prev => ({ ...prev, categoryId: value }))}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Pilih kategori sesi" />
              </SelectTrigger>
              <SelectContent>
                {categories?.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-sm" 
                        style={{ backgroundColor: category.color }}
                      />
                      <span>{category.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime">Waktu Mulai</Label>
              <Input
                id="startTime"
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                className={`${validationErrors.startTime ? 'border-destructive' : ''}`}
                required
              />
              {validationErrors.startTime && (
                <p className="text-destructive text-xs">{validationErrors.startTime}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="endTime">Waktu Selesai</Label>
              <Input
                id="endTime"
                type="time"
                value={formData.endTime}
                onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                className={`${validationErrors.endTime ? 'border-destructive' : ''}`}
                required
              />
              {validationErrors.endTime && (
                <p className="text-destructive text-xs">{validationErrors.endTime}</p>
              )}
            </div>
          </div>

          {(formData.startTime && formData.endTime) && (
            <div className="bg-muted rounded-lg p-3 space-y-2">
              {crossesMidnight(formData.startTime, formData.endTime) && (
                <div className="flex items-center space-x-2 text-xs text-amber-500 dark:text-amber-400 mb-2">
                  <span>⚠️</span>
                  <span>Sesi ini melewati tengah malam</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Durasi:</span>
                <span className="font-medium">
                  {formatDuration(calculatedData.durationMinutes)}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Jumlah JP:</span>
                <span className="font-medium">
                  {formatJP(calculatedData.jpCount)}
                </span>
              </div>
              {generalSettings?.lesson_duration_minutes && (
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Durasi per JP:</span>
                  <span>{generalSettings.lesson_duration_minutes} menit</span>
                </div>
              )}
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)} 
              
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button 
              type="submit" 
              
              disabled={isLoading}
            >
              {isLoading ? 'Menyimpan...' : (editingTimeSession ? 'Update' : 'Simpan')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddTimeSessionModal;
