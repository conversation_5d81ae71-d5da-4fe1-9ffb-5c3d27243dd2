import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useSchedulesSimple = () => {
  return useQuery({
    queryKey: ['schedules-simple'],
    queryFn: async () => {
      console.log('🔄 Fetching schedules with simple query...');

      // 🔧 TRY SCHEDULES_VIEW FIRST, FALLBACK TO CLASS_SCHEDULES
      let data, error;

      // Try schedules_view first
      const viewResult = await supabase
        .from('schedules_view')
        .select(`
          id,
          academic_week,
          day_of_week,
          start_time,
          end_time,
          subject_name,
          subject_color,
          class_name,
          class_id,
          subject_id,
          teacher_name
        `)
        .not('day_of_week', 'is', null)
        .order('academic_week', { ascending: true })
        .order('day_of_week', { ascending: true })
        .order('start_time', { ascending: true })
        .limit(1000);

      if (viewResult.error) {
        console.log('⚠️ schedules_view failed, trying class_schedules:', viewResult.error);

        // Fallback to class_schedules with joins
        const fallbackResult = await supabase
          .from('class_schedules')
          .select(`
            id,
            academic_week,
            day_of_week,
            start_time,
            end_time,
            class_id,
            subject_id,
            teacher_id,
            room,
            notes,
            schedule_subjects (id, name, color),
            classes (id, name, level, grade),
            teachers (id, full_name, nip)
          `)
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true })
          .limit(1000);

        if (fallbackResult.error) {
          console.error('❌ Both queries failed:', fallbackResult.error);
          throw fallbackResult.error;
        }

        // Transform fallback data to match schedules_view structure
        data = fallbackResult.data?.map(schedule => ({
          id: schedule.id,
          academic_week: schedule.academic_week,
          day_of_week: schedule.day_of_week,
          start_time: schedule.start_time,
          end_time: schedule.end_time,
          subject_name: schedule.schedule_subjects?.name,
          subject_color: schedule.schedule_subjects?.color,
          class_name: schedule.classes?.name,
          class_id: schedule.class_id,
          subject_id: schedule.subject_id,
          teacher_name: schedule.teachers?.full_name
        }));

        console.log('✅ Using class_schedules fallback data');
      } else {
        data = viewResult.data;
        console.log('✅ Using schedules_view data');
      }

      console.log('✅ Simple schedules data fetched:', data?.length, 'records');

      // Log distribution
      const weekDistribution = data?.reduce((acc: any, s: any) => {
        acc[s.academic_week] = (acc[s.academic_week] || 0) + 1;
        return acc;
      }, {});

      console.log('📊 Week distribution:', weekDistribution);
      console.log('📅 Available weeks:', Object.keys(weekDistribution || {}).sort((a, b) => parseInt(a) - parseInt(b)));

      // ✅ CRITICAL: Check specific data for week 2 and class 10.A
      const week2Data = data?.filter(s => s.academic_week === 2);
      const class10AData = data?.filter(s => s.class_id === '556ea61c-8dad-4441-8001-a70f3d448ffa');
      const week2Class10A = data?.filter(s => s.academic_week === 2 && s.class_id === '556ea61c-8dad-4441-8001-a70f3d448ffa');

      console.log('🎯 CRITICAL DATA CHECK:', {
        week2Count: week2Data?.length || 0,
        class10ACount: class10AData?.length || 0,
        week2Class10ACount: week2Class10A?.length || 0,
        week2Class10ASample: week2Class10A?.slice(0, 3)
      });

      return data;
    },
    // ✅ FORCE FRESH DATA: Disable all caching to ensure all weeks 1-24 are loaded
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache data
    refetchOnWindowFocus: true, // Force refetch to get latest data
    refetchOnMount: true,
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
  });
};
