
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

export interface AcademicYear {
  id: string;
  school_id: string;
  year_name: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const useAcademicYears = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['academic-years'],
    queryFn: async () => {
      if (!profile?.school_id) {
        return [];
      }

      const { data, error } = await supabase
        .from('academic_years')
        .select('*')
        .eq('school_id', profile.school_id)
        .order('start_date', { ascending: false });
      
      if (error) throw error;
      return data as AcademicYear[];
    },
    enabled: !!profile?.school_id,
  });
};

export const useActiveAcademicYear = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['active-academic-year'],
    queryFn: async () => {
      if (!profile?.school_id) {
        return null;
      }

      const { data, error } = await supabase
        .from('academic_years')
        .select('*')
        .eq('school_id', profile.school_id)
        .eq('is_active', true)
        .single();
      
      if (error && error.code !== 'PGRST116') throw error;
      return data as AcademicYear | null;
    },
    enabled: !!profile?.school_id,
  });
};

export const useUpdateActiveAcademicYear = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async (academicYearId: string) => {
      if (!profile?.school_id) {
        throw new Error('School ID not found');
      }

      // First, set all academic years as inactive for this school
      await supabase
        .from('academic_years')
        .update({ is_active: false })
        .eq('school_id', profile.school_id);

      // Then set the selected one as active
      const { data, error } = await supabase
        .from('academic_years')
        .update({ is_active: true })
        .eq('id', academicYearId)
        .eq('school_id', profile.school_id)
        .select()
        .single();

      if (error) throw error;

      // Update general settings
      await supabase
        .from('general_settings')
        .upsert({ 
          school_id: profile.school_id,
          active_academic_year_id: academicYearId,
          academic_year_id: academicYearId
        });

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['academic-years'] });
      queryClient.invalidateQueries({ queryKey: ['active-academic-year'] });
      queryClient.invalidateQueries({ queryKey: ['general-settings'] });
      toast({
        title: "Berhasil",
        description: "Tahun ajaran aktif berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Gagal memperbarui tahun ajaran aktif",
        variant: "destructive",
      });
      console.error('Error updating active academic year:', error);
    },
  });
};
