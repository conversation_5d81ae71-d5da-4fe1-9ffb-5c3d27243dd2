
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface ClassSubject {
  id: string;
  class_id: string;
  subject_id: string;
  hours_per_week: number;
  created_at: string;
  updated_at: string;
}

export interface ClassSubjectWithRelations extends ClassSubject {
  classes: {
    id: string;
    name: string;
    level: string;
    grade: number;
    capacity?: number;
    school_id: string;
    academic_year_id: string;
    created_at: string;
    updated_at: string;
  };
  schedule_subjects: {
    id: string;
    name: string;
    code: string;
    color: string;
    total_hours_per_year: number;
    session_category_id: string;
    school_id: string;
    academic_year_id: string;
    created_at: string;
    updated_at: string;
  };
}

export const useClassSubjects = () => {
  return useQuery({
    queryKey: ['class-subjects'],
    queryFn: async () => {
      console.log('🔍 useClassSubjects: Fetching class subjects...');

      // Try the new structure first
      const { data, error } = await supabase
        .from('class_schedules')
        .select(`
          *,
          classes(*),
          schedule_subjects(*)
        `)
        .is('day_of_week', null) // Only unscheduled subjects (class_subjects equivalent)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ useClassSubjects error:', error);
        console.log('⚠️ Falling back to empty array to prevent 400 errors');
        // Return empty array instead of throwing to prevent 400 errors
        return [] as ClassSubjectWithRelations[];
      }

      console.log('✅ useClassSubjects: Found', data?.length || 0, 'class subjects');
      return data as ClassSubjectWithRelations[];
    },
    // Add retry and error handling
    retry: 1,
    retryDelay: 1000,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateClassSubject = (showToast: boolean = true) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: { class_id: string; subject_id: string; hours_per_year: number }) => {
      try {
        console.log('Creating class subject with data:', data);

        const insertData = {
          class_id: data.class_id,
          subject_id: data.subject_id,
          hours_per_week: Math.round(data.hours_per_year / 36) || 0, // Keep backward compatibility
          hours_per_year: data.hours_per_year || 0
        };

        console.log('Insert data for class_subjects:', insertData);

        const { data: result, error } = await supabase
          .from('class_schedules')
          .insert(insertData)
          .select()
          .single();

        if (error) {
          console.error('Supabase error creating class subject:', error);
          console.error('Error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });
          throw error;
        }

        console.log('Class subject created successfully:', result);
        return result;
      } catch (error) {
        console.error('Full error in createClassSubject mutationFn:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('useCreateClassSubject onSuccess called with:', data);
      queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['subjects'] });
      if (showToast) {
        toast({
          title: "Berhasil",
          description: "Mata pelajaran berhasil ditambahkan ke kelas",
        });
      }
    },
    onError: (error: any) => {
      console.error('useCreateClassSubject onError called with:', error);
      if (showToast) {
        toast({
          title: "Gagal",
          description: "Gagal menambahkan mata pelajaran ke kelas: " + (error?.message || 'Unknown error'),
          variant: "destructive",
        });
      }
    },
  });
};

export const useUpdateClassSubject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, hours_per_year }: { id: string; hours_per_year: number }) => {
      console.log('🔄 Updating class subject:', { id, hours_per_year });

      const { data: result, error } = await supabase
        .from('class_schedules')
        .update({
          hours_per_year,
          hours_per_week: Math.round(hours_per_year / 36) // Keep backward compatibility
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating class subject:', error);
        throw error;
      }

      console.log('✅ Class subject updated successfully:', result);
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });

      // ✅ FIXED: Invalidate JP progress queries for real-time updates
      queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
      queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

      console.log('✅ Class subject update successful - cache invalidated');
    },
    onError: (error: any) => {
      console.error('❌ Class subject update failed:', error);
      toast({
        title: "Gagal",
        description: "Gagal memperbarui data mata pelajaran: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteClassSubject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      console.log('🗑️ Deleting class subject:', id);

      const { error } = await supabase
        .from('class_schedules')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Error deleting class subject:', error);
        throw error;
      }

      console.log('✅ Class subject deleted successfully');
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });

      // ✅ FIXED: Invalidate JP progress queries for real-time updates
      queryClient.invalidateQueries({ queryKey: ['jp-progress'] });
      queryClient.invalidateQueries({ queryKey: ['jp-progress-simple'] });

      toast({
        title: "Berhasil",
        description: "Mata pelajaran berhasil dihapus dari kelas",
      });

      console.log('✅ Class subject delete successful - cache invalidated');
    },
    onError: (error: any) => {
      console.error('❌ Class subject delete failed:', error);
      toast({
        title: "Gagal",
        description: "Gagal menghapus mata pelajaran: " + error.message,
        variant: "destructive",
      });
    },
  });
};



export const useCopyClassSubjects = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ fromClassId, toClassIds, subjectIds }: {
      fromClassId: string;
      toClassIds: string[];
      subjectIds: string[];
    }) => {
      // Get existing class subjects for the source class
      const { data: sourceClassSubjects, error: fetchError } = await supabase
        .from('class_schedules')
        .select('*')
        .eq('class_id', fromClassId)
        .in('subject_id', subjectIds)
        .is('day_of_week', null); // Only unscheduled subjects

      if (fetchError) throw fetchError;

      // Create new class subjects for target classes
      const newClassSubjects = toClassIds.flatMap(classId =>
        sourceClassSubjects.map(cs => ({
          class_id: classId,
          subject_id: cs.subject_id,
          hours_per_week: cs.hours_per_week,
          hours_per_year: cs.hours_per_year || cs.hours_per_week * 36,
          school_id: cs.school_id,
          academic_year_id: cs.academic_year_id,
        }))
      );

      const { error: insertError } = await supabase
        .from('class_schedules')
        .insert(newClassSubjects);

      if (insertError) throw insertError;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
      toast({
        title: "Berhasil",
        description: "Mata pelajaran berhasil disalin ke kelas lain",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Gagal",
        description: "Gagal menyalin mata pelajaran: " + error.message,
        variant: "destructive",
      });
    },
  });
};
