
import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCreateExtracurricular, useUpdateExtracurricular } from '@/hooks/useExtracurriculars';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useToast } from '@/hooks/use-toast';

interface AddExtracurricularModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingExtracurricular?: any;
}

// Define color palette for extracurriculars
const extracurricularColorPalette = [
  '#F97316', // Orange (default)
  '#EAB308', // Yellow
  '#84CC16', // Lime
  '#22C55E', // Green
  '#14B8A6', // Teal
  '#06B6D4', // Cyan
  '#3B82F6', // Blue
  '#6366F1', // Indigo
  '#8B5CF6', // Purple
  '#EC4899', // Pink
  '#EF4444', // Red
  '#F59E0B', // Amber
];

const AddExtracurricularModal: React.FC<AddExtracurricularModalProps> = ({
  open,
  onOpenChange,
  editingExtracurricular
}) => {
  const [formData, setFormData] = useState({
    name: '',
    hours_per_year: '',
    session_category_id: '', // ✅ FIXED: Remove 's' to match database field
    color: extracurricularColorPalette[0] // Default to orange
  });

  const createExtracurricular = useCreateExtracurricular();
  const updateExtracurricular = useUpdateExtracurricular();
  const { data: sessionCategories = [] } = useSessionCategories();
  const { toast } = useToast();

  // Filter hanya kategori Ekskul
  const ekstrakurikulerCategories = sessionCategories.filter(cat => cat.name === 'Ekskul');

  useEffect(() => {
    // ✅ FIXED: Only reset form when modal opens or when editing different extracurricular
    if (open) {
      if (editingExtracurricular) {
        setFormData({
          name: editingExtracurricular.name || '',
          hours_per_year: editingExtracurricular.hours_per_year?.toString() || '',
          session_category_id: editingExtracurricular.session_category_id || (ekstrakurikulerCategories[0]?.id || ''),
          color: editingExtracurricular.color || extracurricularColorPalette[0]
        });
      } else {
        // Only reset form when opening for new extracurricular
        setFormData({
          name: '',
          hours_per_year: '',
          session_category_id: ekstrakurikulerCategories[0]?.id || '',
          color: extracurricularColorPalette[0]
        });
      }
    }
  }, [editingExtracurricular?.id, open]); // ✅ FIXED: Removed ekstrakurikulerCategories from dependency

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || formData.hours_per_year.trim() === '') {
      toast({
        title: "Error",
        description: "Nama ekstrakurikuler dan JP/Tahun harus diisi",
        variant: "destructive",
      });
      return;
    }

    const hoursPerYear = parseInt(formData.hours_per_year);
    if (isNaN(hoursPerYear) || hoursPerYear < 0) {
      toast({
        title: "Error",
        description: "JP/Tahun harus berupa angka yang valid (0 atau lebih)",
        variant: "destructive",
      });
      return;
    }

    console.log('Form data:', { ...formData, hours_per_year: hoursPerYear });
    
    try {
      const submitData = {
        name: formData.name.trim(),
        hours_per_year: hoursPerYear,
        color: formData.color,
        session_category_id: formData.session_category_id // ✅ FIXED: Include session_category_id
      };

      console.log('🚀 Submitting extracurricular data:', submitData);

      if (editingExtracurricular) {
        await updateExtracurricular.mutateAsync({ id: editingExtracurricular.id, ...submitData });
        toast({
          title: "✅ Berhasil",
          description: "Ekstrakurikuler berhasil diperbarui",
        });
      } else {
        await createExtracurricular.mutateAsync(submitData);
        toast({
          title: "✅ Berhasil",
          description: "Ekstrakurikuler berhasil ditambahkan",
        });
      }
      onOpenChange(false);
    } catch (error) {
      console.error('❌ Error in handleSubmit:', error);
      toast({
        title: "❌ Gagal",
        description: `Gagal menyimpan ekstrakurikuler: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const isLoading = createExtracurricular.isPending || updateExtracurricular.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {editingExtracurricular ? 'Edit Ekstrakurikuler' : 'Tambah Ekstrakurikuler Baru'}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Ekstrakurikuler*</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Contoh: Pramuka, Basket, Musik"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="hours_per_year">JP/Tahun*</Label>
              <Input
                id="hours_per_year"
                type="number"
                value={formData.hours_per_year}
                onChange={(e) => setFormData(prev => ({ ...prev, hours_per_year: e.target.value }))}
                placeholder="Contoh: 72 (atau 0 jika belum direncanakan)"
                min="0"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Kategori*</Label>
            <Select value={formData.session_category_id} onValueChange={(value) => setFormData(prev => ({ ...prev, session_category_id: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih kategori" />
              </SelectTrigger>
              <SelectContent>
                {ekstrakurikulerCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      {category.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <p className="text-xs text-muted-foreground">
            Masukkan 0 jika belum ada perencanaan pasti. Nanti akan otomatis terhitung dari menu Jadwal.
          </p>

          <div className="space-y-2">
            <Label>Warna Ekstrakurikuler</Label>
            <div className="grid grid-cols-6 gap-2">
              {extracurricularColorPalette.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-10 h-10 rounded-lg border-2 transition-all duration-200 hover:scale-105 ${
                    formData.color === color
                      ? 'border-foreground ring-2 ring-offset-2 ring-foreground'
                      : 'border-border hover:border-muted-foreground'
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setFormData(prev => ({ ...prev, color }))}
                  title={`Pilih warna ${color}`}
                />
              ))}
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <div
                className="w-4 h-4 rounded border"
                style={{ backgroundColor: formData.color }}
              />
              <span className="text-sm text-muted-foreground">Warna terpilih: {formData.color}</span>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)} 
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? 'Menyimpan...' : (editingExtracurricular ? 'Update' : 'Simpan')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddExtracurricularModal;
