-- =====================================================
-- UNIFIED SUBJECT SYSTEM MIGRATION
-- =====================================================
-- Mengkonsolidasikan KBM dan EKSKUL dalam satu sistem yang konsisten
-- Menghapus duplikasi dan inkonsistensi tabel

-- =====================================================
-- STEP 1: BACKUP DATA YANG ADA
-- =====================================================

-- Backup subjects data
CREATE TEMP TABLE subjects_backup AS 
SELECT * FROM subjects;

-- Backup schedule_subjects data  
CREATE TEMP TABLE schedule_subjects_backup AS 
SELECT * FROM schedule_subjects;

-- =====================================================
-- STEP 2: STANDARDIZE SCHEDULE_SUBJECTS TABLE
-- =====================================================

-- Drop schedule_subjects table and recreate with correct structure
DROP TABLE IF EXISTS schedule_subjects CASCADE;

CREATE TABLE schedule_subjects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  code TEXT,
  color TEXT NOT NULL DEFAULT '#6B7280',
  total_hours_per_year INTEGER DEFAULT 0,
  standard_duration INTEGER DEFAULT 45, -- dalam menit
  
  -- UNIFIED: Menggunakan session_category_id untuk konsistensi
  session_category_id UUID REFERENCES session_categories(id) ON DELETE SET NULL,
  
  -- Metadata
  school_id UUID NOT NULL,
  academic_year_id UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraint: nama unik per kategori per sekolah per tahun
  UNIQUE(name, session_category_id, school_id, academic_year_id)
);

-- =====================================================
-- STEP 3: CREATE EXTRACURRICULARS TABLE
-- =====================================================

-- Create dedicated extracurriculars table for EKSKUL category
CREATE TABLE extracurriculars (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  color TEXT NOT NULL DEFAULT '#F97316',
  hours_per_year INTEGER DEFAULT 0,
  
  -- Always linked to EKSKUL session category
  session_category_id UUID REFERENCES session_categories(id) ON DELETE SET NULL,
  
  -- Metadata
  school_id UUID NOT NULL,
  academic_year_id UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraint: nama unik per sekolah per tahun
  UNIQUE(name, school_id, academic_year_id)
);

-- =====================================================
-- STEP 4: CREATE RELATIONSHIP TABLES
-- =====================================================

-- Table for KBM subjects assigned to classes
CREATE TABLE schedule_class_subjects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_subject_id UUID NOT NULL REFERENCES schedule_subjects(id) ON DELETE CASCADE,
  class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
  hours_per_week INTEGER DEFAULT 0,
  hours_per_year INTEGER DEFAULT 0,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraint: one subject per class
  UNIQUE(schedule_subject_id, class_id)
);

-- Table for EKSKUL assigned to classes
CREATE TABLE extracurricular_classes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  extracurricular_id UUID NOT NULL REFERENCES extracurriculars(id) ON DELETE CASCADE,
  class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
  hours_per_week INTEGER DEFAULT 0,
  hours_per_year INTEGER DEFAULT 0,
  
  -- Metadata
  school_id UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraint: one extracurricular per class
  UNIQUE(extracurricular_id, class_id)
);

-- =====================================================
-- STEP 5: MIGRATE DATA
-- =====================================================

-- Migrate KBM subjects from subjects table to schedule_subjects
INSERT INTO schedule_subjects (
  name, code, color, total_hours_per_year, session_category_id, 
  school_id, academic_year_id, created_at, updated_at
)
SELECT 
  s.name,
  s.code,
  s.color,
  s.total_hours_per_year,
  s.session_categories_id, -- Map to session_category_id
  s.school_id,
  s.academic_year_id,
  s.created_at,
  s.updated_at
FROM subjects_backup s
INNER JOIN session_categories sc ON s.session_categories_id = sc.id
WHERE sc.name = 'KBM';

-- Migrate EKSKUL subjects from subjects table to extracurriculars
INSERT INTO extracurriculars (
  name, description, color, hours_per_year, session_category_id,
  school_id, academic_year_id, created_at, updated_at
)
SELECT 
  s.name,
  'Ekstrakurikuler ' || s.name, -- Generate description
  s.color,
  s.total_hours_per_year,
  s.session_categories_id,
  s.school_id,
  s.academic_year_id,
  s.created_at,
  s.updated_at
FROM subjects_backup s
INNER JOIN session_categories sc ON s.session_categories_id = sc.id
WHERE sc.name = 'Ekskul';

-- Migrate data from schedule_subjects_backup if exists
INSERT INTO schedule_subjects (
  name, code, color, total_hours_per_year, session_category_id,
  school_id, academic_year_id, created_at, updated_at
)
SELECT 
  name, code, color, total_hours_per_year, session_category_id,
  school_id, academic_year_id, created_at, updated_at
FROM schedule_subjects_backup
WHERE NOT EXISTS (
  SELECT 1 FROM schedule_subjects ss 
  WHERE ss.name = schedule_subjects_backup.name 
  AND ss.session_category_id = schedule_subjects_backup.session_category_id
  AND ss.school_id = schedule_subjects_backup.school_id
  AND ss.academic_year_id = schedule_subjects_backup.academic_year_id
);

-- =====================================================
-- STEP 6: CREATE INDEXES
-- =====================================================

-- Indexes for schedule_subjects
CREATE INDEX idx_schedule_subjects_category ON schedule_subjects(session_category_id);
CREATE INDEX idx_schedule_subjects_school_year ON schedule_subjects(school_id, academic_year_id);

-- Indexes for extracurriculars
CREATE INDEX idx_extracurriculars_category ON extracurriculars(session_category_id);
CREATE INDEX idx_extracurriculars_school_year ON extracurriculars(school_id, academic_year_id);

-- Indexes for relationship tables
CREATE INDEX idx_schedule_class_subjects_subject ON schedule_class_subjects(schedule_subject_id);
CREATE INDEX idx_schedule_class_subjects_class ON schedule_class_subjects(class_id);
CREATE INDEX idx_extracurricular_classes_extra ON extracurricular_classes(extracurricular_id);
CREATE INDEX idx_extracurricular_classes_class ON extracurricular_classes(class_id);

-- =====================================================
-- STEP 7: CREATE RLS POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE schedule_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE extracurriculars ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_class_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE extracurricular_classes ENABLE ROW LEVEL SECURITY;

-- RLS Policies for schedule_subjects
CREATE POLICY "Users can view schedule subjects for their school" 
  ON schedule_subjects FOR SELECT 
  USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Admin can manage schedule subjects" 
  ON schedule_subjects FOR ALL 
  USING (
    school_id = get_user_school_id(auth.uid()) AND (
      has_role(auth.uid(), 'superadmin'::user_role) OR 
      has_role(auth.uid(), 'kepsek'::user_role) OR 
      has_role(auth.uid(), 'kesiswaan'::user_role)
    )
  );

-- RLS Policies for extracurriculars
CREATE POLICY "Users can view extracurriculars for their school" 
  ON extracurriculars FOR SELECT 
  USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Admin can manage extracurriculars" 
  ON extracurriculars FOR ALL 
  USING (
    school_id = get_user_school_id(auth.uid()) AND (
      has_role(auth.uid(), 'superadmin'::user_role) OR 
      has_role(auth.uid(), 'kepsek'::user_role) OR 
      has_role(auth.uid(), 'kesiswaan'::user_role)
    )
  );

-- RLS Policies for relationship tables
CREATE POLICY "Users can view schedule class subjects for their school" 
  ON schedule_class_subjects FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM classes c 
      WHERE c.id = schedule_class_subjects.class_id 
      AND c.school_id = get_user_school_id(auth.uid())
    )
  );

CREATE POLICY "Admin can manage schedule class subjects" 
  ON schedule_class_subjects FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM classes c 
      WHERE c.id = schedule_class_subjects.class_id 
      AND c.school_id = get_user_school_id(auth.uid()) 
      AND (
        has_role(auth.uid(), 'superadmin'::user_role) OR 
        has_role(auth.uid(), 'kepsek'::user_role) OR 
        has_role(auth.uid(), 'kesiswaan'::user_role)
      )
    )
  );

CREATE POLICY "Users can view extracurricular classes for their school" 
  ON extracurricular_classes FOR SELECT 
  USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Admin can manage extracurricular classes" 
  ON extracurricular_classes FOR ALL 
  USING (
    school_id = get_user_school_id(auth.uid()) AND (
      has_role(auth.uid(), 'superadmin'::user_role) OR 
      has_role(auth.uid(), 'kepsek'::user_role) OR 
      has_role(auth.uid(), 'kesiswaan'::user_role)
    )
  );

-- =====================================================
-- STEP 8: CREATE TRIGGERS
-- =====================================================

-- Updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_schedule_subjects_updated_at 
  BEFORE UPDATE ON schedule_subjects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_extracurriculars_updated_at 
  BEFORE UPDATE ON extracurriculars 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedule_class_subjects_updated_at 
  BEFORE UPDATE ON schedule_class_subjects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_extracurricular_classes_updated_at 
  BEFORE UPDATE ON extracurricular_classes 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STEP 9: CREATE VIEWS
-- =====================================================

-- Unified view for all subjects (KBM + EKSKUL)
CREATE OR REPLACE VIEW unified_subjects_view AS
SELECT 
  'kbm' as type,
  s.id,
  s.name,
  s.code,
  s.color,
  s.total_hours_per_year,
  s.session_category_id,
  s.school_id,
  s.academic_year_id,
  s.created_at,
  s.updated_at,
  sc.name as category_name,
  sc.color as category_color
FROM schedule_subjects s
LEFT JOIN session_categories sc ON s.session_category_id = sc.id

UNION ALL

SELECT 
  'ekskul' as type,
  e.id,
  e.name,
  NULL as code,
  e.color,
  e.hours_per_year as total_hours_per_year,
  e.session_category_id,
  e.school_id,
  e.academic_year_id,
  e.created_at,
  e.updated_at,
  sc.name as category_name,
  sc.color as category_color
FROM extracurriculars e
LEFT JOIN session_categories sc ON e.session_category_id = sc.id

ORDER BY type, name;

-- =====================================================
-- STEP 10: CLEANUP
-- =====================================================

-- Drop old subjects table (backup data already saved)
DROP TABLE IF EXISTS subjects CASCADE;

-- Add comments
COMMENT ON TABLE schedule_subjects IS 'Tabel mata pelajaran KBM (Kegiatan Belajar Mengajar)';
COMMENT ON TABLE extracurriculars IS 'Tabel ekstrakurikuler (EKSKUL)';
COMMENT ON TABLE schedule_class_subjects IS 'Relasi mata pelajaran KBM dengan kelas';
COMMENT ON TABLE extracurricular_classes IS 'Relasi ekstrakurikuler dengan kelas';
COMMENT ON VIEW unified_subjects_view IS 'View gabungan untuk semua mata pelajaran dan ekstrakurikuler';

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ UNIFIED SUBJECT SYSTEM MIGRATION COMPLETED!';
  RAISE NOTICE '📚 KBM subjects: schedule_subjects table';
  RAISE NOTICE '🏃 EKSKUL subjects: extracurriculars table';
  RAISE NOTICE '🔗 Class relationships: schedule_class_subjects & extracurricular_classes';
  RAISE NOTICE '👁️ Unified view: unified_subjects_view';
END $$;
