import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, BookOpen, Users, Trophy } from 'lucide-react';
import { useMatrixSubjects, useMatrixClassSubjects, useCreateMatrixClassSubject, useDeleteMatrixClassSubject } from '@/hooks/useMatrixSubjects';
import { useScheduleSubjects, useDeleteScheduleSubject } from '@/hooks/useScheduleSubjects';
import { useClasses } from '@/hooks/useClasses';
import { useExtracurriculars } from '@/hooks/useExtracurriculars';
import { useExtracurricularClasses, useCreateExtracurricularClass } from '@/hooks/useExtracurricularClasses';
import { useAcademicYears } from '@/hooks/useAcademicYears';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useAuth } from '@/contexts/AuthContext';
import { useRealTimeSync } from '@/hooks/useRealTimeSync';
import AddSubjectModal from '@/components/modals/AddSubjectModal';
import AddExtracurricularModal from '@/components/modals/AddExtracurricularModal';
import EditSubjectAssignmentModal from '@/components/modals/EditSubjectAssignmentModal';
import DeleteConfirmationModal from '@/components/modals/DeleteConfirmationModal';
import SubjectMatrixGrid from './SubjectMatrixGrid';
import ExtracurricularGrid from './ExtracurricularGrid';
const SubjectsContentPage = () => {
  // ✅ ENHANCED: Enable real-time sync for immediate UI updates
  useRealTimeSync();

  const [isAddSubjectModalOpen, setIsAddSubjectModalOpen] = useState(false);
  const [isAddExtracurricularModalOpen, setIsAddExtracurricularModalOpen] = useState(false);
  const [isEditAssignmentModalOpen, setIsEditAssignmentModalOpen] = useState(false);
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState<any>(null);
  const [selectedSubjectName, setSelectedSubjectName] = useState('');
  const [selectedClassName, setSelectedClassName] = useState('');
  const [isEditingExtracurricular, setIsEditingExtracurricular] = useState(false);
  const [editingSubject, setEditingSubject] = useState<any>(null);
  const [subjectToDelete, setSubjectToDelete] = useState<any>(null);

  const {
    data: academicYears
  } = useAcademicYears();
  const activeAcademicYear = academicYears?.find(year => year.is_active);

  // Matrix subjects data
  const {
    data: matrixSubjects,
    isLoading: matrixSubjectsLoading,
    error: matrixSubjectsError
  } = useMatrixSubjects();

  const {
    data: matrixClassSubjects,
    isLoading: matrixClassSubjectsLoading,
    error: matrixClassSubjectsError
  } = useMatrixClassSubjects();

  const {
    data: classes,
    isLoading: classesLoading,
    error: classesError
  } = useClasses();
  const {
    data: extracurriculars,
    isLoading: extracurricularsLoading,
    error: extracurricularsError
  } = useExtracurriculars();
  const {
    data: extracurricularClasses,
    isLoading: extracurricularClassesLoading,
    error: extracurricularClassesError
  } = useExtracurricularClasses();
  const {
    data: sessionCategories,
    isLoading: sessionCategoriesLoading,
    error: sessionCategoriesError
  } = useSessionCategories();

  // Mutations
  const createMatrixClassSubject = useCreateMatrixClassSubject();
  const createExtracurricularClass = useCreateExtracurricularClass();
  const deleteScheduleSubject = useDeleteScheduleSubject();
  const deleteMatrixClassSubject = useDeleteMatrixClassSubject();

  // Memoize expensive calculations
  const sortedClasses = useMemo(() => {
    if (!classes) return [];
    return classes.sort((a, b) => {
      const gradeOrder = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'];
      const aIndex = gradeOrder.indexOf(a.level);
      const bIndex = gradeOrder.indexOf(b.level);
      if (aIndex !== bIndex) return aIndex - bIndex;
      return a.name.localeCompare(b.name);
    });
  }, [classes]);

  // Filter matrix subjects to only show KBM category
  const kbmSubjects = useMemo(() => {
    console.log('🔍 Filtering KBM subjects...', {
      matrixSubjectsCount: matrixSubjects?.length || 0,
      sessionCategoriesCount: sessionCategories?.length || 0,
      timestamp: new Date().toISOString()
    });

    if (!matrixSubjects || !sessionCategories) {
      console.log('⚠️ Missing data for KBM filtering:', {
        hasMatrixSubjects: !!matrixSubjects,
        hasSessionCategories: !!sessionCategories
      });
      return [];
    }

    // Find KBM category
    const kbmCategory = sessionCategories.find(category => category.name === 'KBM');
    console.log('🎯 KBM Category found:', {
      found: !!kbmCategory,
      category: kbmCategory ? { id: kbmCategory.id, name: kbmCategory.name } : null,
      allCategories: sessionCategories.map(cat => ({ id: cat.id, name: cat.name }))
    });

    if (!kbmCategory) {
      console.log('❌ No KBM category found');
      return [];
    }

    // Filter subjects that belong to KBM category
    const filtered = matrixSubjects.filter(subject => subject.session_category_id === kbmCategory.id);
    console.log('📊 KBM subjects filtering result:', {
      totalMatrixSubjects: matrixSubjects.length,
      kbmCategoryId: kbmCategory.id,
      filteredCount: filtered.length,
      allSubjects: matrixSubjects.map(s => ({
        id: s.id,
        name: s.name,
        session_category_id: s.session_category_id,
        category_name: s.category_name
      })),
      filteredSubjects: filtered.map(s => ({
        id: s.id,
        name: s.name,
        session_category_id: s.session_category_id,
        category_name: s.category_name
      }))
    });

    return filtered;
  }, [matrixSubjects, sessionCategories]);

  const subjectColors = useMemo(() => ['bg-purple-400/10 border-purple-400/20 text-purple-400', 'bg-orange-400/10 border-orange-400/20 text-orange-400', 'bg-blue-400/10 border-blue-400/20 text-blue-400', 'bg-green-400/10 border-green-400/20 text-green-400', 'bg-cyan-400/10 border-cyan-400/20 text-cyan-400', 'bg-pink-400/10 border-pink-400/20 text-pink-400', 'bg-yellow-400/10 border-yellow-400/20 text-yellow-400', 'bg-red-400/10 border-red-400/20 text-red-400'], []);
  const extracurricularColors = useMemo(() => ['bg-orange-400/10 border-orange-400/20 text-orange-400', 'bg-amber-400/10 border-amber-400/20 text-amber-400', 'bg-yellow-400/10 border-yellow-400/20 text-yellow-400', 'bg-lime-400/10 border-lime-400/20 text-lime-400', 'bg-emerald-400/10 border-emerald-400/20 text-emerald-400', 'bg-teal-400/10 border-teal-400/20 text-teal-400', 'bg-cyan-400/10 border-cyan-400/20 text-cyan-400', 'bg-sky-400/10 border-sky-400/20 text-sky-400'], []);
  const getSubjectColor = useMemo(() => (index: number) => {
    return subjectColors[index % subjectColors.length];
  }, [subjectColors]);
  const getExtracurricularColor = useMemo(() => (index: number) => {
    return extracurricularColors[index % extracurricularColors.length];
  }, [extracurricularColors]);
  const getClassSubject = useMemo(() => {
    if (!matrixClassSubjects) return () => undefined;
    const classSubjectMap = new Map();
    matrixClassSubjects.forEach(cs => {
      const key = `${cs.subject_id}-${cs.class_id}`;
      classSubjectMap.set(key, cs);
    });
    return (subjectId: string, classId: string) => {
      return classSubjectMap.get(`${subjectId}-${classId}`);
    };
  }, [matrixClassSubjects]);
  const getExtracurricularClass = useMemo(() => {
    if (!extracurricularClasses) return () => undefined;
    const extracurricularClassMap = new Map();
    extracurricularClasses.forEach((ec: any) => {
      const key = `${ec.extracurricular_id}-${ec.class_id}`;
      extracurricularClassMap.set(key, ec);
    });
    return (extracurricularId: string, classId: string) => {
      return extracurricularClassMap.get(`${extracurricularId}-${classId}`);
    };
  }, [extracurricularClasses]);
  const handleAddSubjectToClass = (subject: any, classItem: any) => {
    const existingAssignment = getClassSubject(subject.id, classItem.id);
    if (existingAssignment) return;
    createMatrixClassSubject.mutate({
      subject_id: subject.id,
      class_id: classItem.id,
      hours_per_year: 72 // Default 2 hours per week * 36 weeks
    });
  };
  const handleAddExtracurricularToClass = (extracurricular: any, classItem: any) => {
    const existingAssignment = getExtracurricularClass(extracurricular.id, classItem.id);
    if (existingAssignment) return;
    createExtracurricularClass.mutate({
      extracurricular_id: extracurricular.id,
      class_id: classItem.id,
      hours_per_year: 36 // Default 1 hour per week * 36 weeks
    });
  };
  const handleEditAssignment = (assignment: any, subjectName: string, className: string) => {
    setSelectedAssignment(assignment);
    setSelectedSubjectName(subjectName);
    setSelectedClassName(className);
    setIsEditingExtracurricular(false);
    setIsEditAssignmentModalOpen(true);
  };

  const handleDeleteAssignment = (assignment: any) => {
    if (confirm(`Apakah Anda yakin ingin menghapus assignment ini?`)) {
      deleteMatrixClassSubject.mutate(assignment.id);
    }
  };
  const handleEditExtracurricularAssignment = (assignment: any, extracurricularName: string, className: string) => {
    setSelectedAssignment(assignment);
    setSelectedSubjectName(extracurricularName);
    setSelectedClassName(className);
    setIsEditingExtracurricular(true);
    setIsEditAssignmentModalOpen(true);
  };

  // Handler untuk edit mata pelajaran
  const handleEditSubject = (subject: any) => {
    setEditingSubject(subject);
    setIsAddSubjectModalOpen(true);
  };

  // Handler untuk delete mata pelajaran
  const handleDeleteSubject = (subject: any) => {
    setSubjectToDelete(subject);
    setIsDeleteConfirmModalOpen(true);
  };

  // Handler untuk konfirmasi delete mata pelajaran
  const handleConfirmDeleteSubject = () => {
    if (subjectToDelete) {
      deleteScheduleSubject.mutate(subjectToDelete.id);
      setSubjectToDelete(null);
    }
  };

  // Handler untuk menutup modal edit subject
  const handleCloseAddSubjectModal = (open: boolean) => {
    setIsAddSubjectModalOpen(open);
    if (!open) {
      setEditingSubject(null);
    }
  };



  // Handle errors
  const hasErrors = matrixSubjectsError || matrixClassSubjectsError || classesError || extracurricularsError || extracurricularClassesError || sessionCategoriesError;
  const isLoading = matrixSubjectsLoading || matrixClassSubjectsLoading || classesLoading || extracurricularsLoading || extracurricularClassesLoading || sessionCategoriesLoading;

  if (hasErrors) {
    console.error('Errors in SubjectsContentPage:', {
      matrixSubjectsError,
      matrixClassSubjectsError,
      classesError,
      extracurricularsError,
      extracurricularClassesError,
      sessionCategoriesError
    });
    return <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-foreground">
          <h2 className="text-xl mb-4">❌ Error Loading Data</h2>
          <div className="text-sm text-muted-foreground space-y-1">
            {matrixSubjectsError && <p>Matrix Subjects: {matrixSubjectsError.message}</p>}
            {matrixClassSubjectsError && <p>Matrix Class Subjects: {matrixClassSubjectsError.message}</p>}
            {classesError && <p>Classes: {classesError.message}</p>}
            {sessionCategoriesError && <p>Session Categories: {sessionCategoriesError.message}</p>}
            {extracurricularsError && <p>Extracurriculars: {extracurricularsError.message}</p>}
          </div>
        </div>
      </div>;
  }

  if (isLoading) {
    return <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-foreground">Memuat data...</div>
      </div>;
  }
  return <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section with Icons */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center space-x-2">
                <BookOpen className="h-8 w-8 text-lime-400" />

                <h1 className="text-3xl font-bold text-foreground">Mata Pelajaran</h1>
              </div>
            </div>
            <p className="text-muted-foreground">Kelola penugasan mata pelajaran untuk setiap kelas</p>
          </div>
        </div>

        {/* Tabs for Subjects and Extracurriculars */}
        <Tabs defaultValue="subjects" className="w-full">
          <TabsList className="bg-muted/30 border-border/30">
            <TabsTrigger value="subjects" className="data-[state=active]:bg-lime-400/20 data-[state=active]:text-lime-400">
              <BookOpen className="mr-2 h-4 w-4" />
              Mata Pelajaran
            </TabsTrigger>
            <TabsTrigger value="extracurriculars" className="data-[state=active]:bg-orange-400/20 data-[state=active]:text-orange-400">
              <Trophy className="mr-2 h-4 w-4" />
              Ekstrakurikuler
            </TabsTrigger>
          </TabsList>

          <TabsContent value="subjects" className="bg-transparent">
            <div className="flex justify-end">
              <Button onClick={() => setIsAddSubjectModalOpen(true)} className="bg-lime-500 hover:bg-lime-600 text-gray-900 font-semibold shadow-lg transition-all duration-300 transform hover:scale-105">
                <Plus className="mr-2 h-5 w-5" />
                Tambah Mata Pelajaran
              </Button>
            </div>

            <Card className="bg-card backdrop-blur-sm border-border">
              <CardContent className="p-6">
                {sortedClasses.length === 0 ? <div className="p-8 text-center">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-foreground mb-2">Belum ada kelas</h3>
                    <p className="text-muted-foreground">Silakan tambah kelas terlebih dahulu di menu Kelas</p>
                  </div> : <SubjectMatrixGrid subjects={kbmSubjects || []} extracurriculars={[]} sortedClasses={sortedClasses} getSubjectColor={() => ''} getClassSubject={getClassSubject} handleAddSubjectToClass={handleAddSubjectToClass} handleEditAssignment={handleEditAssignment} handleDeleteAssignment={handleDeleteAssignment} createClassSubject={createMatrixClassSubject} onAddSubject={() => setIsAddSubjectModalOpen(true)} onAddExtracurricular={() => {}} onEditSubject={handleEditSubject} onDeleteSubject={handleDeleteSubject} />}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="extracurriculars" className="space-y-4">
            <div className="flex justify-end">
              <Button onClick={() => setIsAddExtracurricularModalOpen(true)} className="bg-orange-500 hover:bg-orange-600 text-gray-900 font-semibold shadow-lg transition-all duration-300 transform hover:scale-105">
                <Plus className="mr-2 h-5 w-5" />
                Tambah Ekstrakurikuler
              </Button>
            </div>

            <Card className="bg-card backdrop-blur-sm border-border">
              <CardContent className="p-6">
                {sortedClasses.length === 0 ? <div className="p-8 text-center">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-foreground mb-2">Belum ada kelas</h3>
                    <p className="text-muted-foreground">Silakan tambah kelas terlebih dahulu di menu Kelas</p>
                  </div> : <ExtracurricularGrid
                    activeAcademicYear={activeAcademicYear}
                    onEditAssignment={handleEditExtracurricularAssignment}
                  />}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <AddSubjectModal
          open={isAddSubjectModalOpen}
          onOpenChange={handleCloseAddSubjectModal}
          editingSubject={editingSubject}
        />

        <AddExtracurricularModal open={isAddExtracurricularModalOpen} onOpenChange={setIsAddExtracurricularModalOpen} />

        <EditSubjectAssignmentModal
          open={isEditAssignmentModalOpen}
          onOpenChange={setIsEditAssignmentModalOpen}
          assignment={selectedAssignment}
          subjectName={selectedSubjectName}
          className={selectedClassName}
          isExtracurricular={isEditingExtracurricular}
          isMatrixAssignment={true} // ✅ FIXED: This is always from matrix in subjects page
        />

        <DeleteConfirmationModal
          open={isDeleteConfirmModalOpen}
          onOpenChange={setIsDeleteConfirmModalOpen}
          onConfirm={handleConfirmDeleteSubject}
          title="Hapus Mata Pelajaran"
          description="Apakah Anda yakin ingin menghapus mata pelajaran ini? Semua data terkait akan ikut terhapus."
          itemName={subjectToDelete?.name}
          isLoading={deleteScheduleSubject.isPending}
        />
      </div>
    </div>;
};
export default SubjectsContentPage;