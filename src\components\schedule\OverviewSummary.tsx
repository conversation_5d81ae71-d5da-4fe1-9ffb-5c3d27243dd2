import React, { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Calendar, Clock, BookOpen, Users, Info } from 'lucide-react';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { format, startOfMonth, endOfMonth, eachDayOfInterval } from 'date-fns';
import { id } from 'date-fns/locale';

interface OverviewSummaryProps {
  schedules: any[];
  isLoading: boolean;
}

export const OverviewSummary: React.FC<OverviewSummaryProps> = ({
  schedules,
  isLoading
}) => {
  const { academicWeeks } = useAcademicWeeks();

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!schedules || schedules.length === 0 || academicWeeks.length === 0) {
      return {
        totalSchedules: 0,
        totalDaysWithSchedule: 0,
        monthlyBreakdown: [],
        totalSubjects: 0,
        totalHours: 0
      };
    }

    // Convert schedules to actual dates
    const scheduleDates = new Set<string>();
    const subjectIds = new Set<string>();
    let totalMinutes = 0;

    schedules.forEach(schedule => {
      // Calculate actual date from academic_week and day_of_week
      if (schedule.academic_week && schedule.day_of_week) {
        const academicWeek = academicWeeks.find(week => week.weekNumber === schedule.academic_week);
        
        if (academicWeek) {
          const weekStartDate = new Date(academicWeek.startDate);
          const dayOffset = schedule.day_of_week === 7 ? 6 : schedule.day_of_week - 1;
          
          const scheduleDate = new Date(weekStartDate);
          scheduleDate.setDate(weekStartDate.getDate() + dayOffset);
          
          const dateString = format(scheduleDate, 'yyyy-MM-dd');
          scheduleDates.add(dateString);
        }
      }

      // Count unique subjects
      if (schedule.subject_id) {
        subjectIds.add(schedule.subject_id);
      }

      // Calculate total duration
      if (schedule.start_time && schedule.end_time) {
        const startTime = new Date(`2000-01-01 ${schedule.start_time}`);
        const endTime = new Date(`2000-01-01 ${schedule.end_time}`);
        const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
        totalMinutes += durationMinutes;
      }
    });

    // Calculate monthly breakdown
    const monthlyBreakdown = [];

    // Get academic year from academicWeeks if available
    let startYear = new Date().getFullYear();
    if (academicWeeks.length > 0) {
      startYear = new Date(academicWeeks[0].startDate).getFullYear();
    }

    // Academic year months (July to June)
    const academicMonths = [
      { name: 'Juli', monthIndex: 6, year: startYear },
      { name: 'Agustus', monthIndex: 7, year: startYear },
      { name: 'September', monthIndex: 8, year: startYear },
      { name: 'Oktober', monthIndex: 9, year: startYear },
      { name: 'November', monthIndex: 10, year: startYear },
      { name: 'Desember', monthIndex: 11, year: startYear },
      { name: 'Januari', monthIndex: 0, year: startYear + 1 },
      { name: 'Februari', monthIndex: 1, year: startYear + 1 },
      { name: 'Maret', monthIndex: 2, year: startYear + 1 },
      { name: 'April', monthIndex: 3, year: startYear + 1 },
      { name: 'Mei', monthIndex: 4, year: startYear + 1 },
      { name: 'Juni', monthIndex: 5, year: startYear + 1 },
    ];

    academicMonths.forEach(month => {
      const monthStart = startOfMonth(new Date(month.year, month.monthIndex, 1));
      const monthEnd = endOfMonth(new Date(month.year, month.monthIndex, 1));
      const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });
      
      let daysWithSchedule = 0;
      daysInMonth.forEach(day => {
        const dayString = format(day, 'yyyy-MM-dd');
        if (scheduleDates.has(dayString)) {
          daysWithSchedule++;
        }
      });

      monthlyBreakdown.push({
        month: month.name,
        totalDays: daysInMonth.length,
        daysWithSchedule,
        percentage: Math.round((daysWithSchedule / daysInMonth.length) * 100)
      });
    });

    return {
      totalSchedules: schedules.length,
      totalDaysWithSchedule: scheduleDates.size,
      monthlyBreakdown,
      totalSubjects: subjectIds.size,
      totalHours: Math.round(totalMinutes / 60 * 10) / 10 // Round to 1 decimal
    };
  }, [schedules, academicWeeks]);

  if (isLoading) {
    return (
      <Card className="bg-card border-border shadow-sm">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-foreground">
            Ringkasan Statistik
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="text-muted-foreground">Memuat statistik...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-card border-border shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-primary/10 rounded-lg">
                <Calendar className="h-6 w-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Jadwal</p>
                <p className="text-2xl font-bold text-foreground">{summaryStats.totalSchedules}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-border shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-500/10 rounded-lg">
                <Clock className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Hari Aktif</p>
                <p className="text-2xl font-bold text-foreground">{summaryStats.totalDaysWithSchedule}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-border shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-500/10 rounded-lg">
                <BookOpen className="h-6 w-6 text-green-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Mata Pelajaran</p>
                <p className="text-2xl font-bold text-foreground">{summaryStats.totalSubjects}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-border shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-orange-500/10 rounded-lg">
                <Users className="h-6 w-6 text-orange-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Jam</p>
                <p className="text-2xl font-bold text-foreground">{summaryStats.totalHours}h</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total JP Card */}
        <Card className="bg-card border-border shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-purple-500/10 rounded-lg">
                <Info className="h-6 w-6 text-purple-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total JP</p>
                <p className="text-2xl font-bold text-foreground">
                  {Math.round((summaryStats.totalHours * 60) / 45)} JP
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Breakdown */}
      <Card className="bg-card border-border shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground">
            Ringkasan Bulanan
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Jumlah hari dengan jadwal/kegiatan per bulan
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {summaryStats.monthlyBreakdown.map((month, index) => (
              <div key={index} className="p-4 bg-muted/20 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium text-foreground">{month.month}</h4>
                  <span className="text-sm text-muted-foreground">{month.percentage}%</span>
                </div>
                <div className="text-sm text-muted-foreground mb-2">
                  {month.daysWithSchedule} dari {month.totalDays} hari
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${month.percentage}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
