import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDeleteSchedule } from '@/hooks/useDeleteSchedule';
import { useClasses } from '@/hooks/useClasses';

export const DeleteScheduleDebug: React.FC = () => {
  const [selectedClassId, setSelectedClassId] = useState<string>('');
  const [selectedMonth, setSelectedMonth] = useState<number>(1);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  const { data: classes } = useClasses();
  const { getPreviewSchedules } = useDeleteSchedule();

  const months = [
    { value: 1, label: '<PERSON><PERSON><PERSON>' },
    { value: 2, label: '<PERSON><PERSON><PERSON>' },
    { value: 3, label: '<PERSON><PERSON>' },
    { value: 4, label: 'April' },
    { value: 5, label: '<PERSON>' },
    { value: 6, label: 'Juni' },
    { value: 7, label: 'Juli' },
    { value: 8, label: 'Agustus' },
    { value: 9, label: 'September' },
    { value: 10, label: 'Oktober' },
    { value: 11, label: 'November' },
    { value: 12, label: 'Desember' },
  ];

  const testDeleteMonth = () => {
    if (!selectedClassId) {
      alert('Pilih kelas terlebih dahulu!');
      return;
    }

    try {
      console.log('🔍 Testing delete month preview...');
      
      // Test the preview function that would be used before actual deletion
      const previewSchedules = getPreviewSchedules(
        undefined, // dayOrUndefined
        selectedMonth, // weekOrWeeksOrMonth (month number)
        selectedClassId, // targetClassId
        'month' // type
      );

      const result = {
        selectedClass: classes?.find(c => c.id === selectedClassId)?.name,
        selectedMonth: months.find(m => m.value === selectedMonth)?.label,
        previewCount: previewSchedules.length,
        previewSample: previewSchedules.slice(0, 3),
        allWeeksFound: [...new Set(previewSchedules.map(s => s.academic_week))].sort((a, b) => a - b),
        success: true
      };

      setDebugInfo(result);
      console.log('✅ Delete month test result:', result);

    } catch (error: any) {
      console.error('❌ Delete month test error:', error);
      setDebugInfo({
        error: error.message,
        success: false
      });
    }
  };

  return (
    <Card className="bg-gray-800/20 backdrop-blur-sm border-gray-700/30">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          🗑️ Delete Schedule Debug - Month Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Pilih Kelas:
            </label>
            <Select value={selectedClassId} onValueChange={setSelectedClassId}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Pilih kelas..." />
              </SelectTrigger>
              <SelectContent>
                {classes?.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Pilih Bulan:
            </label>
            <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Pilih bulan..." />
              </SelectTrigger>
              <SelectContent>
                {months.map((month) => (
                  <SelectItem key={month.value} value={month.value.toString()}>
                    {month.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button 
          onClick={testDeleteMonth}
          disabled={!selectedClassId}
          className="w-full bg-red-500 hover:bg-red-600"
        >
          Test Delete Month Preview
        </Button>

        {debugInfo && (
          <div className="space-y-4">
            {debugInfo.success ? (
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                <h3 className="text-green-400 font-semibold mb-2">✅ Test Successful!</h3>
                <div className="text-green-300 text-sm space-y-1">
                  <p><strong>Class:</strong> {debugInfo.selectedClass}</p>
                  <p><strong>Month:</strong> {debugInfo.selectedMonth}</p>
                  <p><strong>Schedules Found:</strong> {debugInfo.previewCount}</p>
                  <p><strong>Weeks Found:</strong> {debugInfo.allWeeksFound.join(', ')}</p>
                </div>
              </div>
            ) : (
              <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
                <h3 className="text-red-400 font-semibold mb-2">❌ Test Failed</h3>
                <p className="text-red-300 text-sm">{debugInfo.error}</p>
              </div>
            )}

            <div className="bg-gray-900/50 p-4 rounded-lg max-h-64 overflow-y-auto">
              <h3 className="text-white font-semibold mb-2">Debug Details:</h3>
              <pre className="text-xs text-gray-300">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
          <h3 className="text-blue-400 font-semibold mb-2">ℹ️ How This Works</h3>
          <div className="text-blue-300 text-sm space-y-1">
            <p>• This test checks if the delete month function can find schedules</p>
            <p>• It uses the same logic as the actual delete operation</p>
            <p>• If this test succeeds, the actual delete should work</p>
            <p>• No actual deletion is performed in this test</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
