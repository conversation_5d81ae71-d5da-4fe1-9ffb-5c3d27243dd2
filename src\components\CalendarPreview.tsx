
import React from 'react';
import { Calendar, Clock, Users, MapPin } from 'lucide-react';

const CalendarPreview = () => {
  const mockLessons = [
    { time: '08:00', subject: '<PERSON><PERSON><PERSON><PERSON>', teacher: '<PERSON>', room: 'Kelas 1A', color: 'lime' },
    { time: '09:30', subject: 'Bahasa Indonesia', teacher: '<PERSON><PERSON> <PERSON><PERSON>', room: 'Kelas 1B', color: 'blue' },
    { time: '11:00', subject: 'IPA', teacher: '<PERSON> Bud<PERSON>', room: 'Lab IPA', color: 'purple' },
    { time: '13:00', subject: '<PERSON><PERSON><PERSON>', teacher: '<PERSON><PERSON> <PERSON>na', room: '<PERSON>las 2A', color: 'cyan' },
  ];

  return (
    <div className="relative max-w-4xl mx-auto">
      {/* Glow effect background */}
      <div className="absolute inset-0 bg-gradient-to-r from-lime-400/10 to-blue-400/10 rounded-3xl blur-xl"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-lime-400/5 to-blue-400/5 rounded-3xl"></div>
      
      {/* Main calendar container */}
      <div className="relative bg-gray-800/40 backdrop-blur-sm border border-gray-600/30 rounded-3xl p-8 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white flex items-center space-x-3">
            <Calendar className="text-lime-400" size={28} />
            <span>Jadwal Hari Ini</span>
          </h3>
          <div className="text-sm text-gray-400">
            Senin, 27 Mei 2025
          </div>
        </div>

        <div className="grid gap-4">
          {mockLessons.map((lesson, index) => (
            <div
              key={index}
              className="bg-gray-700/30 backdrop-blur-sm border border-gray-600/20 rounded-xl p-4 hover:bg-gray-700/50 transition-all duration-300 group"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`w-3 h-12 rounded-full bg-gradient-to-b from-${lesson.color}-400 to-${lesson.color}-600`}></div>
                  <div>
                    <h4 className="text-lg font-semibold text-white">{lesson.subject}</h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-400 mt-1">
                      <span className="flex items-center space-x-1">
                        <Clock size={14} />
                        <span>{lesson.time}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Users size={14} />
                        <span>{lesson.teacher}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <MapPin size={14} />
                        <span>{lesson.room}</span>
                      </span>
                    </div>
                  </div>
                </div>
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button className="text-lime-400 hover:text-lime-300 text-sm font-medium">
                    Edit
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Stats section */}
        <div className="grid grid-cols-3 gap-4 mt-8 pt-6 border-t border-gray-600/30">
          <div className="text-center">
            <div className="text-2xl font-bold text-lime-400">12</div>
            <div className="text-sm text-gray-400">Kelas Hari Ini</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">8</div>
            <div className="text-sm text-gray-400">Guru Aktif</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">245</div>
            <div className="text-sm text-gray-400">Siswa</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalendarPreview;
