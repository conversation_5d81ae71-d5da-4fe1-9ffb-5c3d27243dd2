import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Printer, Download, Calendar, FileText, Table, Image } from 'lucide-react';
// Simple fallback without external libraries for now
// TODO: Add PDF/Excel/PNG generation later
import { useClasses } from '@/hooks/useClasses';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useSchedulesSingleWeek, useSchedulesCalendar } from '@/hooks/useSchedulesPaginated';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useSchools } from '@/hooks/useSchools';
import { useEducationLevels } from '@/hooks/useEducationLevels';
import { useAcademicContext } from '@/hooks/useAcademicContext';

type PrintPeriod = 'week' | 'month';
type PrintFormat = 'pdf' | 'excel' | 'png';

const PrintSchedule = () => {
  const [selectedClassId, setSelectedClassId] = useState<string>('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [printPeriod, setPrintPeriod] = useState<PrintPeriod>('week');
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [selectedMonth, setSelectedMonth] = useState<number>(1);
  const [printFormat, setPrintFormat] = useState<PrintFormat>('pdf');

  const { data: classes = [] } = useClasses();
  const { data: categories = [] } = useSessionCategories();
  const { academicWeeks } = useAcademicWeeks();
  const { data: currentSchool } = useSchools();
  const { data: educationLevels = [] } = useEducationLevels();
  const {
    activeAcademicYear,
    activeSemester,
    academicPeriodDisplay,
    academicYearInfo,
    semesterInfo
  } = useAcademicContext();

  // Get schedules based on selected period
  const weekToFetch = printPeriod === 'week' ? selectedWeek : 1;
  const { data: schedules = [], isLoading, error } = useSchedulesSingleWeek(
    weekToFetch,
    selectedClassId || undefined
  );

  // Fallback: try calendar hook if single week hook fails
  const { data: calendarSchedules = [] } = useSchedulesCalendar(
    weekToFetch,
    selectedClassId || undefined
  );

  // Use fallback data if primary hook fails
  const finalSchedules = schedules.length > 0 ? schedules : calendarSchedules;

  // Additional debug for hook parameters
  console.log('Hook Parameters:', {
    weekToFetch,
    selectedClassId,
    hookEnabled: weekToFetch > 0 && weekToFetch <= 24,
    primarySchedulesCount: schedules.length,
    fallbackSchedulesCount: calendarSchedules.length,
    finalSchedulesCount: finalSchedules.length
  });

  // Filter schedules by selected categories
  const filteredSchedules = useMemo(() => {
    console.log('🔍 FILTERING DEBUG:', {
      finalSchedulesLength: finalSchedules?.length || 0,
      selectedCategoriesLength: selectedCategories.length,
      selectedCategories,
      sampleSchedule: finalSchedules?.[0]
    });

    if (!finalSchedules || finalSchedules.length === 0) {
      console.log('❌ No schedules data available');
      return [];
    }

    if (selectedCategories.length === 0) {
      console.log('✅ No categories selected, showing all schedules:', finalSchedules.length);
      return finalSchedules;
    }

    const filtered = finalSchedules.filter(schedule => {
      const hasCategory = selectedCategories.includes(schedule.session_category_id);
      console.log('Filter check:', {
        scheduleId: schedule.id,
        subjectName: schedule.subject_name,
        sessionCategoryId: schedule.session_category_id,
        hasCategory
      });
      return hasCategory;
    });

    console.log('🎯 Filtered schedules result:', {
      originalCount: finalSchedules.length,
      filteredCount: filtered.length,
      selectedCategories
    });

    return filtered;
  }, [finalSchedules, selectedCategories]);

  // Debug logging
  console.log('PrintSchedule Debug:', {
    selectedWeek,
    selectedClassId,
    printPeriod,
    schedulesCount: finalSchedules?.length || 0,
    isLoading,
    error,
    schedules: finalSchedules?.slice(0, 3), // Show first 3 items for debugging
    filteredSchedulesCount: filteredSchedules?.length || 0,
    selectedCategories,
    academicPeriodDisplay,
    activeAcademicYear,
    activeSemester
  });

  // Group schedules by month for month view
  const monthlySchedules = useMemo(() => {
    const grouped: { [key: number]: typeof schedules } = {};
    schedules.forEach(schedule => {
      const month = new Date(schedule.date).getMonth() + 1;
      if (!grouped[month]) grouped[month] = [];
      grouped[month].push(schedule);
    });
    return grouped;
  }, [schedules]);

  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handlePrint = () => {
    if (!selectedClassId) {
      alert('Silakan pilih kelas terlebih dahulu');
      return;
    }

    // For now, all formats will use HTML print
    // TODO: Implement PDF/Excel/PNG generation
    generateHTMLPrint();
  };

  // TODO: Implement PDF, Excel, PNG generation
  // For now, these are placeholder functions

  const generateHTMLPrint = () => {
    // Generate HTML content for printing
    const printContent = generatePrintContent();

    // Open print window
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();

      // Trigger print dialog
      printWindow.onload = () => {
        printWindow.print();
      };
    }
  };

  const generatePrintContent = () => {
    const selectedClass = classes.find(c => c.id === selectedClassId);
    const selectedCategoryNames = categories
      .filter(cat => selectedCategories.includes(cat.id))
      .map(cat => cat.name);

    const printTitle = printPeriod === 'week'
      ? `Jadwal Pekan ${selectedWeek}`
      : `Jadwal Bulan ${monthNames[selectedMonth - 1]}`;

    // Get education level for the selected class
    const classEducationLevel = educationLevels.find(level =>
      selectedClass?.level >= level.min_grade && selectedClass?.level <= level.max_grade
    );

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${printTitle} - ${selectedClass?.name}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .letterhead { margin-bottom: 20px; display: flex; align-items: flex-start; justify-content: center; gap: 15px; }
          .logo { width: 80px; height: 80px; object-fit: contain; flex-shrink: 0; }
          .school-info { text-align: center; }
          .school-name { font-size: 18px; font-weight: bold; margin-bottom: 2px; text-transform: uppercase; }
          .education-level { font-size: 14px; font-weight: normal; margin-bottom: 8px; text-transform: uppercase; }
          .school-address { font-size: 11px; color: #666; margin-bottom: 3px; }
          .school-contact { font-size: 11px; color: #666; margin-bottom: 10px; }
          .divider { border: none; border-top: 2px solid #333; margin: 15px 0; }
          .title { font-size: 16px; margin-bottom: 10px; font-weight: bold; }
          .info { font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f5f5f5; font-weight: bold; }
          .time-col { width: 100px; }
          .subject-col { width: 150px; }
          .teacher-col { width: 120px; }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="letterhead">
            ${currentSchool?.logo_url ? `<img src="${currentSchool.logo_url}" alt="Logo Sekolah" class="logo" />` : ''}
            <div class="school-info">
              <div class="school-name">${currentSchool?.name || 'NAMA SEKOLAH'}</div>
              ${classEducationLevel ? `<div class="education-level">${classEducationLevel.name}</div>` : ''}
              ${currentSchool?.address ? `<div class="school-address">${currentSchool.address}</div>` : ''}
              <div class="school-contact">
                ${currentSchool?.phone ? `Telp: ${currentSchool.phone}` : ''}
                ${currentSchool?.phone && currentSchool?.email ? ' | ' : ''}
                ${currentSchool?.email ? `Email: ${currentSchool.email}` : ''}
              </div>
            </div>
          </div>
          <hr class="divider" />
          <div class="title">${printTitle}</div>
          <div class="info">
            Kelas: ${selectedClass?.name} |
            Kategori: ${selectedCategoryNames.length > 0 ? selectedCategoryNames.join(', ') : 'Semua'} |
            ${academicPeriodDisplay} ${semesterInfo ? `| Semester ${semesterInfo.number}` : ''}
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th class="time-col">Waktu</th>
              <th>Hari</th>
              <th class="subject-col">Mata Pelajaran</th>
              <th class="teacher-col">Guru</th>
            </tr>
          </thead>
          <tbody>
            ${generateTableRows()}
          </tbody>
        </table>

        <div style="margin-top: 30px; font-size: 10px; color: #666;">
          Dicetak pada: ${new Date().toLocaleDateString('id-ID')} ${new Date().toLocaleTimeString('id-ID')}
        </div>
      </body>
      </html>
    `;
  };

  const generateTableRows = () => {
    if (!filteredSchedules || filteredSchedules.length === 0) {
      return '<tr><td colspan="4" style="text-align: center; color: #666;">Tidak ada jadwal untuk periode ini</td></tr>';
    }

    const dayNames = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];

    return filteredSchedules
      .sort((a, b) => {
        // Sort by day of week, then by start time
        if (a.day_of_week !== b.day_of_week) {
          return a.day_of_week - b.day_of_week;
        }
        return a.start_time.localeCompare(b.start_time);
      })
      .map(schedule => `
        <tr>
          <td>${schedule.start_time} - ${schedule.end_time}</td>
          <td>${dayNames[schedule.day_of_week] || schedule.day_of_week}</td>
          <td>${schedule.subject_name}</td>
          <td>${schedule.teacher_name || '-'}</td>
        </tr>
      `).join('');
  };

  const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);
  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-muted/20 border-border shadow-sm">
        <CardHeader>
          <CardTitle className="text-center text-2xl font-bold text-foreground flex items-center justify-center gap-3">
            <Printer className="h-8 w-8 text-primary" />
            Print Jadwal
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Cetak jadwal dalam format tabel dengan berbagai pilihan periode dan kategori.
            Tersedia dalam format PDF, Excel, dan PNG.
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Panel */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="bg-card border-border shadow-sm">
            <CardHeader>
              <CardTitle className="text-foreground flex items-center gap-2">
                <Calendar className="h-5 w-5 text-primary" />
                Konfigurasi Print
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* School Info */}
              <div>
                <Label className="text-foreground">Sekolah</Label>
                <div className="mt-1 p-3 bg-muted/50 rounded-lg border border-border">
                  <div className="text-foreground font-semibold">{currentSchool?.name || 'Nama Sekolah'}</div>
                  <div className="text-sm text-muted-foreground">{academicPeriodDisplay}</div>
                </div>
              </div>

              {/* Class Selection */}
              <div>
                <Label className="text-foreground">Kelas</Label>
                <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                  <SelectTrigger className="mt-1 bg-background border-border text-foreground">
                    <SelectValue placeholder="Pilih kelas" />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border-border">
                    {classes.map(cls => (
                      <SelectItem key={cls.id} value={cls.id} className="text-foreground hover:bg-accent">
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category Selection */}
              <div>
                <Label className="text-foreground">Kategori Kegiatan</Label>
                <div className="mt-2 flex flex-wrap gap-2">
                  {categories.map(category => (
                    <Badge
                      key={category.id}
                      variant={selectedCategories.includes(category.id) ? "default" : "outline"}
                      className={`cursor-pointer transition-all ${
                        selectedCategories.includes(category.id)
                          ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                          : 'border-border text-muted-foreground hover:border-primary hover:text-primary'
                      }`}
                      onClick={() => handleCategoryToggle(category.id)}
                    >
                      {category.name}
                    </Badge>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Kosongkan untuk mencetak semua kategori
                </p>
              </div>

              {/* Period Selection */}
              <div>
                <Label className="text-foreground">Periode</Label>
                <Tabs value={printPeriod} onValueChange={(value) => setPrintPeriod(value as PrintPeriod)} className="mt-2">
                  <TabsList className="grid w-full grid-cols-2 bg-muted">
                    <TabsTrigger value="week" className="data-[state=active]:bg-background data-[state=active]:text-foreground">
                      Mingguan
                    </TabsTrigger>
                    <TabsTrigger value="month" className="data-[state=active]:bg-background data-[state=active]:text-foreground">
                      Bulanan
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="week" className="mt-4">
                    <Select value={selectedWeek.toString()} onValueChange={(value) => setSelectedWeek(parseInt(value))}>
                      <SelectTrigger className="bg-background border-border text-foreground">
                        <SelectValue placeholder="Pilih pekan" />
                      </SelectTrigger>
                      <SelectContent className="bg-popover border-border max-h-60">
                        {academicWeeks.map(week => (
                          <SelectItem key={week.weekNumber} value={week.weekNumber.toString()} className="text-foreground hover:bg-accent">
                            Pekan {week.weekNumber} ({new Date(week.startDate).toLocaleDateString('id-ID')} - {new Date(week.endDate).toLocaleDateString('id-ID')})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TabsContent>

                  <TabsContent value="month" className="mt-4">
                    <Select value={selectedMonth.toString()} onValueChange={(value) => setSelectedMonth(parseInt(value))}>
                      <SelectTrigger className="bg-background border-border text-foreground">
                        <SelectValue placeholder="Pilih bulan" />
                      </SelectTrigger>
                      <SelectContent className="bg-popover border-border">
                        {monthNames.map((month, index) => (
                          <SelectItem key={index + 1} value={(index + 1).toString()} className="text-foreground hover:bg-accent">
                            {month}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Format Selection */}
              <div>
                <Label className="text-foreground">Format Output</Label>
                <div className="mt-2 grid grid-cols-3 gap-2">
                  <Button
                    variant={printFormat === 'pdf' ? 'default' : 'outline'}
                    onClick={() => setPrintFormat('pdf')}
                    className={`${printFormat === 'pdf' ? 'bg-primary hover:bg-primary/90 text-primary-foreground' : 'border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground'}`}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    PDF
                  </Button>
                  <Button
                    variant={printFormat === 'excel' ? 'default' : 'outline'}
                    onClick={() => setPrintFormat('excel')}
                    className={`${printFormat === 'excel' ? 'bg-primary hover:bg-primary/90 text-primary-foreground' : 'border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground'}`}
                  >
                    <Table className="h-4 w-4 mr-2" />
                    Excel
                  </Button>
                  <Button
                    variant={printFormat === 'png' ? 'default' : 'outline'}
                    onClick={() => setPrintFormat('png')}
                    className={`${printFormat === 'png' ? 'bg-primary hover:bg-primary/90 text-primary-foreground' : 'border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground'}`}
                  >
                    <Image className="h-4 w-4 mr-2" />
                    PNG
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Preview & Action Panel */}
        <div className="space-y-6">
          <Card className="bg-card border-border shadow-sm">
            <CardHeader>
              <CardTitle className="text-foreground">Preview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-foreground space-y-2">
                <div><span className="text-muted-foreground">Kelas:</span> {classes.find(c => c.id === selectedClassId)?.name || 'Semua Kelas'}</div>
                <div><span className="text-muted-foreground">Periode:</span> {
                  printPeriod === 'week'
                    ? `Pekan ${selectedWeek}`
                    : monthNames[selectedMonth - 1]
                }</div>
                <div><span className="text-muted-foreground">Kategori:</span> {
                  selectedCategories.length === 0
                    ? 'Semua Kategori'
                    : `${selectedCategories.length} kategori dipilih`
                }</div>
                <div><span className="text-muted-foreground">Tahun Akademik:</span> {academicPeriodDisplay}</div>
                <div><span className="text-muted-foreground">Format:</span> {printFormat.toUpperCase()}</div>
                <div><span className="text-muted-foreground">Total Jadwal:</span> {filteredSchedules.length} item</div>
              </div>

              <Button
                onClick={handlePrint}
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                disabled={!selectedClassId}
              >
                <Download className="h-4 w-4 mr-2" />
                Generate & Download
              </Button>
            </CardContent>
          </Card>

          {/* Format Info */}
          <Card className="bg-card border-border shadow-sm">
            <CardHeader>
              <CardTitle className="text-foreground text-sm">Format Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-xs text-muted-foreground">
              <div>
                <span className="text-primary font-semibold">PDF:</span> Ideal untuk print dan sharing resmi
              </div>
              <div>
                <span className="text-primary font-semibold">Excel:</span> Dapat diedit dan dianalisis lebih lanjut
              </div>
              <div>
                <span className="text-primary font-semibold">PNG:</span> Gambar untuk media sosial atau presentasi
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PrintSchedule;
