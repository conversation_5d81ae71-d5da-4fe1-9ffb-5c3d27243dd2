
export interface Subject {
  id: string;
  name: string;
  code: string;
  category: 'wajib' | 'peminatan' | 'muatan-lokal' | 'ekstrakurikuler';
  category_id?: string; // ✅ ADDED: Foreign key to session_categories
  color: string; // ✅ UPDATED: Color field for subject display
  total_hours_per_year?: number;
  school_id: string;
  academic_year_id: string;
  created_at: string;
  updated_at: string;
}

export interface ClassSubject {
  id: string;
  class_id: string;
  subject_id: string;
  hours_per_week: number;
  created_at: string;
  updated_at: string;
}

export interface Class {
  id: string;
  name: string;
  level: string;
  grade: number;
  capacity?: number;
  school_id: string;
  academic_year_id: string;
  created_at: string;
  updated_at: string;
}

export interface SubjectWithClasses extends Subject {
  class_subjects?: (ClassSubject & { class: Class })[];
}

export type SubjectCategory = 'mapel' | 'ekskul';
export type GradeLevel = 'sd' | 'smp' | 'sma';
