import React, { useState, useEffect, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { eachDayOfInterval, getDay, startOfDay } from 'date-fns';
import { CalendarDays, Calendar, ArrowLeft, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Holiday } from '@/types/event';
import PublicAnnualCalendar from '../components/events/PublicAnnualCalendar';

interface PublicEventsData {
  school: {
    name: string;
    logo_url?: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  class: {
    id: string;
    name: string;
    level: string;
  };
  holidays: Holiday[];
  classes: Array<{
    id: string;
    name: string;
    level: string;
  }>;
  academicYear: {
    id: string;
    year_name: string;
    start_date: string;
    end_date: string;
  };
}

const PublicEventsPage: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const [data, setData] = useState<PublicEventsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedClassId, setSelectedClassId] = useState<string>('all');

  // Navigate back to schedule
  const handleBackToSchedule = () => {
    if (token) {
      window.location.href = `/external/${token}`;
    }
  };

  useEffect(() => {
    const fetchPublicEventsData = async () => {
      if (!token) {
        setError('Token tidak valid');
        setLoading(false);
        return;
      }

      try {
        console.log('🔍 Fetching public events data for token:', token);

        // Decode token to get class_id and school_id
        let classId: string;
        let schoolId: string;

        try {
          const decoded = atob(token);
          const [decodedClassId, decodedSchoolId] = decoded.split(':');
          classId = decodedClassId;
          schoolId = decodedSchoolId;
          console.log('✅ Token decoded:', { classId, schoolId });
        } catch (decodeError) {
          console.error('❌ Token decode error:', decodeError);
          setError('Token tidak valid atau rusak');
          setLoading(false);
          return;
        }

        // Fetch school data
        const { data: schoolData, error: schoolError } = await supabase
          .from('schools')
          .select('name, logo_url, address, phone, email')
          .eq('id', schoolId)
          .single();

        if (schoolError) {
          console.error('❌ School fetch error:', schoolError);
          setError('Data sekolah tidak ditemukan');
          setLoading(false);
          return;
        }

        // Fetch class data
        const { data: classData, error: classError } = await supabase
          .from('classes')
          .select('id, name, level')
          .eq('id', classId)
          .eq('school_id', schoolId)
          .single();

        if (classError) {
          console.error('❌ Class fetch error:', classError);
          setError('Data kelas tidak ditemukan');
          setLoading(false);
          return;
        }

        // Fetch all classes for filter
        const { data: allClassesData, error: allClassesError } = await supabase
          .from('classes')
          .select('id, name, level')
          .eq('school_id', schoolId)
          .order('name');

        if (allClassesError) {
          console.error('❌ All classes fetch error:', allClassesError);
        }

        // Fetch active academic year
        const { data: academicYearData, error: academicYearError } = await supabase
          .from('academic_years')
          .select('id, year_name, start_date, end_date')
          .eq('school_id', schoolId)
          .eq('is_active', true)
          .single();

        if (academicYearError) {
          console.error('❌ Academic year fetch error:', academicYearError);
          setError('Data tahun akademik tidak ditemukan');
          setLoading(false);
          return;
        }

        // Fetch holidays/events
        const { data: holidaysData, error: holidaysError } = await supabase
          .from('holidays')
          .select('*')
          .eq('school_id', schoolId)
          .eq('academic_year_id', academicYearData.id)
          .order('start_date');

        if (holidaysError) {
          console.error('❌ Holidays fetch error:', holidaysError);
          setError('Data event tidak ditemukan');
          setLoading(false);
          return;
        }

        setData({
          school: schoolData,
          class: classData,
          holidays: holidaysData || [],
          classes: allClassesData || [],
          academicYear: academicYearData
        });

        // Set default selected class to the token class
        setSelectedClassId(classId);

        console.log('✅ Public events data loaded:', {
          school: schoolData.name,
          class: classData.name,
          holidaysCount: holidaysData?.length || 0,
          classesCount: allClassesData?.length || 0,
          academicYear: academicYearData.year_name
        });

      } catch (fetchError) {
        console.error('❌ General fetch error:', fetchError);
        setError('Terjadi kesalahan saat memuat data');
      } finally {
        setLoading(false);
      }
    };

    fetchPublicEventsData();
  }, [token]);

  // Calculate effective days count with class filter
  const { effectiveDaysCount, totalDays } = useMemo(() => {
    if (!data?.academicYear || !data?.holidays) {
      return { effectiveDaysCount: 0, totalDays: 0 };
    }

    const yearStartDate = new Date(data.academicYear.start_date);
    const yearEndDate = new Date(data.academicYear.end_date);

    const allDaysInYear = eachDayOfInterval({ start: yearStartDate, end: yearEndDate });
    const nonEffectiveDays = new Set<string>();

    // Mark all Sundays
    allDaysInYear.forEach(day => {
      if (getDay(day) === 0) { // 0 is Sunday
        nonEffectiveDays.add(day.toISOString().split('T')[0]);
      }
    });

    // Filter holidays based on selected class - same logic as EventsPage
    const filteredHolidays = data.holidays.filter(holiday => {
      if (!selectedClassId || selectedClassId === 'all') return true;
      if (!holiday.class_ids || holiday.class_ids.length === 0) return true; // All classes
      return holiday.class_ids.includes(selectedClassId);
    });

    // Mark filtered holidays only
    filteredHolidays.forEach(holiday => {
      const holidayStart = startOfDay(new Date(holiday.start_date));
      const holidayEnd = startOfDay(new Date(holiday.end_date));
      const daysInHoliday = eachDayOfInterval({ start: holidayStart, end: holidayEnd });
      daysInHoliday.forEach(day => {
        nonEffectiveDays.add(day.toISOString().split('T')[0]);
      });
    });

    const totalDaysInYear = allDaysInYear.length;
    const effectiveDays = totalDaysInYear - nonEffectiveDays.size;

    console.log('🎯 Public Effective Days Calculation:', {
      selectedClassId,
      totalHolidays: data.holidays.length,
      filteredHolidays: filteredHolidays.length,
      totalDays: totalDaysInYear,
      nonEffectiveDays: nonEffectiveDays.size,
      effectiveDays
    });

    return {
      effectiveDaysCount: effectiveDays,
      totalDays: totalDaysInYear,
    };
  }, [data?.academicYear, data?.holidays, selectedClassId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Memuat kalender event...</p>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                {error || 'Data tidak ditemukan'}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentYear = new Date(data.academicYear.start_date).getFullYear();

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleBackToSchedule}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Kembali ke Jadwal
            </Button>
            
            {data.school.logo_url && (
              <img 
                src={data.school.logo_url} 
                alt="Logo Sekolah" 
                className="h-12 w-12 object-contain"
              />
            )}
            
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <CalendarDays className="h-8 w-8 text-lime-400" />
                <h1 className="text-3xl font-bold text-foreground">Kalender Event</h1>
              </div>
              <p className="text-muted-foreground">
                {data.school.name} - Tahun akademik {data.academicYear.year_name}
              </p>
            </div>
          </div>
        </div>

        {/* Filter Section */}
        <Card className="bg-card border-border rounded-2xl p-6">
          <CardContent className="p-0">
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-lime-400" />
                  <span className="text-foreground">Filter Kelas:</span>
                </div>
                <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                  <SelectTrigger className="w-48 bg-background border-lime-400/30 text-foreground">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Kelas</SelectItem>
                    {data.classes.map(cls => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="text-right">
                <div className="flex items-center gap-2 text-sm font-medium text-lime-500">
                  <CheckCircle className="h-5 w-5" />
                  <span>Hari Efektif</span>
                </div>
                <div className="text-2xl font-bold text-foreground mt-1">
                  {effectiveDaysCount} <span className="text-base font-normal text-muted-foreground">/ {totalDays} hari</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Area */}
        <Card className="bg-card border-border rounded-2xl p-2">
          <CardHeader className="p-6 flex flex-row items-start justify-between">
            <div>
              <CardTitle className="text-card-foreground text-xl">
                Kalender Event Tahunan - Tampilan Publik
              </CardTitle>
              <CardDescription className="text-muted-foreground mt-1">
                Lihat event dan kegiatan sepanjang tahun akademik
              </CardDescription>
            </div>
          </CardHeader>
          <CardContent className="p-6 bg-transparent">
            <PublicAnnualCalendar 
              year={currentYear} 
              holidays={data.holidays} 
              selectedClassId={selectedClassId}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PublicEventsPage;