// =====================================================
// HOOKS FOR NEW SCHEDULE SUBJECTS SYSTEM
// =====================================================

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useAcademicYears } from '@/hooks/useAcademicYears';
import {
  ScheduleSubject,
  ScheduleSubjectClass,
  CreateScheduleSubjectData,
  CreateScheduleClassSubjectData,
  UpdateScheduleSubjectData,
  ScheduleSubjectWithRelations
} from '@/types/scheduleCategory';

// =====================================================
// SCHEDULE SUBJECTS HOOKS
// =====================================================

export const useScheduleSubjects = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useQuery({
    queryKey: ['schedule-subjects', profile?.school_id, activeYear?.id],
    queryFn: async () => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      console.log('Fetching schedule subjects for:', {
        school_id: profile.school_id,
        academic_year_id: activeYear.id
      });

      // Get schedule subjects with session categories
      const { data, error } = await supabase
        .from('schedule_subjects')
        .select(`
          *,
          session_categories (
            id,
            name,
            color
          )
        `)
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeYear.id)
        .order('name', { ascending: true });

      if (error) {
        console.error('Error fetching schedule subjects:', error);
        throw error;
      }

      console.log('Schedule Subjects fetched:', {
        academicYear: activeYear.id,
        totalSubjects: data?.length || 0,
        subjects: data?.map(s => ({ name: s.name, category: s.session_categories?.name }))
      });

      return data as ScheduleSubjectWithRelations[];
    },
    enabled: !!profile?.school_id && !!activeYear?.id,
    staleTime: 0,
    gcTime: 0,
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

export const useCreateScheduleSubject = (showToast: boolean = true) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useMutation({
    mutationFn: async (subjectData: CreateScheduleSubjectData) => {
      try {
        console.log('Creating schedule subject with data:', subjectData);

        if (!profile?.school_id || !activeYear?.id) {
          throw new Error('Data sekolah atau tahun akademik tidak ditemukan');
        }

        // ✅ FIXED: Prepare data for insert (use session_category_id)
        const insertData = {
          name: subjectData.name.trim(),
          code: subjectData.code?.trim() || null,
          color: subjectData.color,
          total_hours_per_year: subjectData.total_hours_per_year || 0,
          standard_duration: subjectData.standard_duration || 45,
          session_category_id: subjectData.schedule_category_id, // Map to correct column
          school_id: profile.school_id,
          academic_year_id: activeYear.id,
        };

        console.log('Insert data for schedule_subjects:', insertData);

        const { data: result, error } = await supabase
          .from('schedule_subjects')
          .insert(insertData)
          .select('*')
          .single();

        if (error) {
          console.error('Supabase error creating schedule subject:', error);
          
          // Handle specific errors
          if (error.code === '23505') {
            throw new Error('Mata pelajaran dengan nama tersebut sudah ada untuk kategori ini');
          }
          
          throw error;
        }

        // ✅ Step 2: Create class assignments if selected_classes provided
        if (subjectData.selected_classes && subjectData.selected_classes.length > 0) {
          console.log('Creating class assignments for:', subjectData.selected_classes);

          const classAssignments = subjectData.selected_classes.map(classId => ({
            schedule_subject_id: result.id,
            class_id: classId,
            hours_per_week: 0,
            hours_per_year: 0
          }));

          const { error: classError } = await supabase
            .from('schedule_class_subjects')
            .insert(classAssignments);

          if (classError) {
            console.error('❌ Error creating class assignments:', classError);
            // Don't throw error, just log it
          } else {
            console.log('✅ Class assignments created successfully for', subjectData.selected_classes.length, 'classes');
          }
        } else {
          console.log('⚠️ No classes selected, subject will appear for all classes by default');
        }

        console.log('Schedule subject created successfully:', result);
        return result as ScheduleSubjectWithRelations;
      } catch (error) {
        console.error('Full error in createScheduleSubject:', error);
        throw error;
      }
    },
    onSuccess: async (data) => {
      console.log('✅ useCreateScheduleSubject onSuccess called with:', data);
      console.log('🎯 Created subject details:', {
        id: data?.id,
        name: data?.name,
        session_category_id: data?.session_category_id,
        school_id: data?.school_id,
        academic_year_id: data?.academic_year_id,
        timestamp: new Date().toISOString()
      });

      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      console.log('🔄 Starting comprehensive cache invalidation...');
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] }),
        queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['matrix-class-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] }),
        queryClient.invalidateQueries({ queryKey: ['class-subjects-by-category'] }),
        queryClient.invalidateQueries({ queryKey: ['kbm-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['unified-subjects'] })
      ]);
      console.log('✅ Cache invalidation completed');

      // ✅ ENHANCED: Force immediate refetch for instant UI updates with delay
      setTimeout(() => {
        console.log('🔄 Starting force refetch...');
        queryClient.refetchQueries({ queryKey: ['schedule-subjects'] });
        queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });
        queryClient.refetchQueries({ queryKey: ['all-subjects-by-category'] });
        queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
        queryClient.refetchQueries({ queryKey: ['kbm-subjects'] });
        console.log('✅ Force refetch completed');
      }, 200);

      if (showToast) {
        toast({
          title: "Berhasil",
          description: "Mata pelajaran berhasil dibuat",
        });
      }
    },
    onError: (error: any) => {
      console.error('useCreateScheduleSubject onError called with:', error);
      if (showToast) {
        toast({
          title: "Gagal",
          description: error?.message || 'Gagal membuat mata pelajaran',
          variant: "destructive",
        });
      }
    },
  });
};

export const useUpdateScheduleSubject = (showToast: boolean = true) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: { id: string } & UpdateScheduleSubjectData) => {
      const { data, error } = await supabase
        .from('schedule_subjects')
        .update(updateData)
        .eq('id', id)
        .select('*')
        .single();

      if (error) throw error;
      return data as ScheduleSubjectWithRelations;
    },
    onSuccess: async () => {
      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] });

      // ✅ ENHANCED: Force immediate refetch for instant UI updates
      queryClient.refetchQueries({ queryKey: ['schedule-subjects'] });
      queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });

      if (showToast) {
        toast({
          title: "Berhasil",
          description: "Mata pelajaran berhasil diperbarui",
        });
      }
    },
    onError: (error: any) => {
      if (showToast) {
        toast({
          title: "Gagal",
          description: error?.message || 'Gagal memperbarui mata pelajaran',
          variant: "destructive",
        });
      }
    },
  });
};

export const useDeleteScheduleSubject = (showToast: boolean = true) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('schedule_subjects')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });
      
      if (showToast) {
        toast({
          title: "Berhasil",
          description: "Mata pelajaran berhasil dihapus",
        });
      }
    },
    onError: (error: any) => {
      if (showToast) {
        toast({
          title: "Gagal",
          description: error?.message || 'Gagal menghapus mata pelajaran',
          variant: "destructive",
        });
      }
    },
  });
};

// =====================================================
// SCHEDULE CLASS SUBJECTS HOOKS
// =====================================================

export const useScheduleClassSubjects = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useQuery({
    queryKey: ['schedule-class-subjects', profile?.school_id, activeYear?.id],
    queryFn: async () => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      console.log('Fetching schedule class subjects for:', {
        school_id: profile.school_id,
        academic_year_id: activeYear.id
      });

      // Get schedule class subjects with relations
      const { data, error } = await supabase
        .from('schedule_class_subjects')
        .select(`
          *,
          classes!inner (
            id, name, level, grade, capacity, school_id, academic_year_id
          ),
          schedule_subjects (
            id, name, code, color, total_hours_per_year
          )
        `)
        .eq('classes.school_id', profile.school_id)
        .eq('classes.academic_year_id', activeYear.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching schedule class subjects:', error);
        throw error;
      }

      console.log('Schedule Class Subjects fetched:', {
        academicYear: activeYear.id,
        totalRelations: data?.length || 0
      });

      return data as ScheduleClassSubject[];
    },
    enabled: !!profile?.school_id && !!activeYear?.id,
    staleTime: 0,
    gcTime: 0,
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

export const useCreateScheduleClassSubject = (showToast: boolean = true) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: CreateScheduleClassSubjectData) => {
      try {
        console.log('Creating schedule class subject with data:', data);

        const insertData = {
          class_id: data.class_id,
          schedule_subject_id: data.schedule_subject_id,
          hours_per_week: data.hours_per_week || 0,
          hours_per_year: data.hours_per_year || 0
        };

        console.log('Insert data for schedule_class_subjects:', insertData);

        const { data: result, error } = await supabase
          .from('schedule_class_subjects')
          .insert(insertData)
          .select(`
            *,
            class:classes(id, name, level, grade),
            schedule_subject:schedule_subjects(*)
          `)
          .single();

        if (error) {
          console.error('Supabase error creating schedule class subject:', error);

          if (error.code === '23505') {
            throw new Error('Mata pelajaran sudah ditambahkan ke kelas ini');
          }

          throw error;
        }

        console.log('Schedule class subject created successfully:', result);
        return result as ScheduleClassSubject;
      } catch (error) {
        console.error('Full error in createScheduleClassSubject:', error);
        throw error;
      }
    },
    onSuccess: async (data) => {
      console.log('useCreateScheduleClassSubject onSuccess called with:', data);

      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] }),
        queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['matrix-class-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['class-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] }),
        queryClient.invalidateQueries({ queryKey: ['class-subjects-by-category'] })
      ]);

      // ✅ ENHANCED: Force immediate refetch for instant UI updates with delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['schedule-class-subjects'] });
        queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });
        queryClient.refetchQueries({ queryKey: ['all-subjects-by-category'] });
        queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
      }, 200);

      if (showToast) {
        toast({
          title: "Berhasil",
          description: "Mata pelajaran berhasil ditambahkan ke kelas",
        });
      }
    },
    onError: (error: any) => {
      console.error('useCreateScheduleClassSubject onError called with:', error);
      if (showToast) {
        toast({
          title: "Gagal",
          description: error?.message || 'Gagal menambahkan mata pelajaran ke kelas',
          variant: "destructive",
        });
      }
    },
  });
};

export const useDeleteScheduleClassSubject = (showToast: boolean = true) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('schedule_class_subjects')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });
      queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });

      if (showToast) {
        toast({
          title: "Berhasil",
          description: "Relasi mata pelajaran dengan kelas berhasil dihapus",
        });
      }
    },
    onError: (error: any) => {
      if (showToast) {
        toast({
          title: "Gagal",
          description: error?.message || 'Gagal menghapus relasi mata pelajaran dengan kelas',
          variant: "destructive",
        });
      }
    },
  });
};
