import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// 📊 PAGINATION CONFIGURATION (exactly like your example)
const ITEMS_PER_PAGE = 50; // Same as your example
const MAX_TOTAL_ITEMS = 500000; // 500k limit

// 🚀 EXACT IMPLEMENTATION of your fetchPageData function
async function fetchPageData(pageNumber: number) {
  const startIndex = pageNumber * ITEMS_PER_PAGE; // Indeks awal untuk halaman ini
  const endIndex = startIndex + ITEMS_PER_PAGE - 1; // Indeks akhir untuk halaman ini

  const { data, error } = await supabase
    .from('schedules_view') // Using schedules_view as suggested
    .select('*')
    .range(startIndex, endIndex) // <--- Ini yang penting! (your comment)
    .order('academic_week', { ascending: true }); // Tambahkan pengurutan yang relevan

  if (error) {
    console.error('Error fetching data:', error);
    return null;
  }
  return data;
}

// 🎯 HOOK: Single Page Data (for specific page)
export const useSchedulePage = (pageNumber: number) => {
  return useQuery({
    queryKey: ['schedules-page', pageNumber],
    queryFn: () => fetchPageData(pageNumber),
    enabled: pageNumber >= 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// 🚀 HOOK: Infinite Scroll Pagination (load more on demand)
export const useSchedulesInfinite = () => {
  return useInfiniteQuery({
    queryKey: ['schedules-infinite'],
    queryFn: ({ pageParam = 0 }) => fetchPageData(pageParam),
    getNextPageParam: (lastPage, allPages) => {
      // If last page has full data, there might be more
      if (lastPage && lastPage.length === ITEMS_PER_PAGE) {
        const nextPage = allPages.length;
        const totalItems = nextPage * ITEMS_PER_PAGE;
        
        // Don't exceed 500k limit
        if (totalItems < MAX_TOTAL_ITEMS) {
          return nextPage;
        }
      }
      return undefined; // No more pages
    },
    initialPageParam: 0,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// 🎯 HOOK: Load Specific Week Range (optimized for calendar) - ENHANCED WITH PAGINATION
export const useSchedulesWeekRange = (startWeek: number, endWeek: number) => {
  return useQuery({
    queryKey: ['schedules-week-range', startWeek, endWeek],
    queryFn: async () => {
      console.log(`📅 Fetching weeks ${startWeek} to ${endWeek} with pagination`);

      // 🚀 ENHANCED: Use pagination for week range queries
      let allData: any[] = [];
      let from = 0;
      const batchSize = 250; // Smaller batches for week range
      let hasMore = true;
      let totalFetched = 0;

      while (hasMore) {
        console.log(`📦 Week range batch: ${from} to ${from + batchSize - 1} (Progress: ${totalFetched} rows)`);

        const { data: batchData, error: batchError } = await supabase
          .from('schedules_view')
          .select('*')
          .gte('academic_week', startWeek)
          .lte('academic_week', endWeek)
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true })
          .range(from, from + batchSize - 1);

        if (batchError) {
          console.error('Error fetching week range batch:', batchError);
          throw batchError;
        }

        if (!batchData || batchData.length === 0) {
          hasMore = false;
          break;
        }

        allData = [...allData, ...batchData];
        totalFetched += batchData.length;

        // Check if we got less than batch size (end of data)
        if (batchData.length < batchSize) {
          hasMore = false;
        } else {
          from += batchSize;
        }

        // Safety limit for week range (should be much smaller)
        if (totalFetched >= 10000) {
          console.log('⚠️ Reached safety limit of 10k records for week range');
          hasMore = false;
        }
      }

      console.log(`✅ Week range ${startWeek}-${endWeek} fetched: ${allData.length} records (Total batches: ${Math.ceil(totalFetched / batchSize)})`);
      return allData;
    },
    enabled: startWeek > 0 && endWeek > 0 && startWeek <= endWeek,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// 🚀 HOOK: Smart Calendar Data (load only visible weeks) - ENHANCED WITH PAGINATION
export const useSchedulesCalendar = (currentWeek: number, classId?: string) => {
  // Default to week 1 if currentWeek is invalid
  const validCurrentWeek = currentWeek && currentWeek > 0 ? currentWeek : 1;

  // Load current week ± 2 weeks for smooth navigation
  const startWeek = Math.max(1, validCurrentWeek - 2);
  const endWeek = Math.min(24, validCurrentWeek + 2);

  return useQuery({
    queryKey: ['schedules-calendar', validCurrentWeek, classId],
    queryFn: async () => {
      console.log(`📅 Calendar: Loading weeks ${startWeek}-${endWeek} for class ${classId || 'all'} (currentWeek: ${currentWeek} → ${validCurrentWeek})`);

      // 🚀 ENHANCED PAGINATION: Use range() method to bypass Supabase limits
      let allData: any[] = [];
      let from = 0;
      const batchSize = 1000; // Process in batches
      let hasMore = true;
      let totalFetched = 0;

      while (hasMore) {
        console.log(`📦 Calendar batch: ${from} to ${from + batchSize - 1} (Progress: ${totalFetched} rows)`);

        // Try schedules_view first with pagination
        let query = supabase
          .from('schedules_view')
          .select('*')
          .gte('academic_week', startWeek)
          .lte('academic_week', endWeek)
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true })
          .range(from, from + batchSize - 1);

        // Add class filter if specified
        if (classId) {
          query = query.eq('class_id', classId);
        }

        const { data: batchData, error: batchError } = await query;

        if (batchError) {
          console.error('❌ Error fetching batch from schedules_view:', batchError);

          // Fallback to schedules table with joins and pagination
          console.log('🔄 Falling back to schedules table with pagination...');

          let fallbackQuery = supabase
            .from('class_schedules')
            .select(`
              *,
              schedule_subjects (id, name, code, color),
              classes (id, name, level, grade),
              teachers (id, full_name, nip)
            `)
            .gte('academic_week', startWeek)
            .lte('academic_week', endWeek)
            .not('day_of_week', 'is', null)
            .order('academic_week', { ascending: true })
            .order('day_of_week', { ascending: true })
            .order('start_time', { ascending: true })
            .range(from, from + batchSize - 1);

          if (classId) {
            fallbackQuery = fallbackQuery.eq('class_id', classId);
          }

          const { data: fallbackData, error: fallbackError } = await fallbackQuery;

          if (fallbackError) {
            console.error('❌ Fallback query also failed:', fallbackError);
            throw fallbackError;
          }

          if (!fallbackData || fallbackData.length === 0) {
            hasMore = false;
            break;
          }

          allData = [...allData, ...fallbackData];
          totalFetched += fallbackData.length;

          // Check if we got less than batch size (end of data)
          if (fallbackData.length < batchSize) {
            hasMore = false;
          } else {
            from += batchSize;
          }

          continue;
        }

        if (!batchData || batchData.length === 0) {
          hasMore = false;
          break;
        }

        allData = [...allData, ...batchData];
        totalFetched += batchData.length;

        // Check if we got less than batch size (end of data)
        if (batchData.length < batchSize) {
          hasMore = false;
        } else {
          from += batchSize;
        }

        // Safety limit to prevent infinite loops
        if (totalFetched >= 50000) {
          console.log('⚠️ Reached safety limit of 50k records for calendar');
          hasMore = false;
        }
      }

      console.log(`✅ Calendar data fetched: ${allData.length} records for weeks ${startWeek}-${endWeek} (Total batches: ${Math.ceil(totalFetched / batchSize)})`);
      return allData;
    },
    enabled: true, // Always enabled, we handle invalid weeks internally
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for calendar)
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// 🚀 HOOK: Complete Schedule Data with Smart Pagination (SOLUSI UTAMA)
export const useSchedulesComplete = () => {
  return useQuery({
    queryKey: ['schedules-complete'],
    queryFn: async () => {
      console.log('🔄 Fetching ALL schedules with smart pagination...');

      let allData: any[] = [];
      let from = 0;
      const batchSize = 500; // Smaller batches for better performance
      let hasMore = true;
      let totalFetched = 0;

      while (hasMore) {
        console.log(`📦 Complete batch: ${from} to ${from + batchSize - 1} (Progress: ${totalFetched} rows)`);

        const { data: batchData, error: batchError } = await supabase
          .from('schedules_view')
          .select(`
            id,
            academic_week,
            day_of_week,
            start_time,
            end_time,
            subject_name,
            subject_color,
            class_name,
            class_id,
            subject_id,
            teacher_name,
            session_category_id,
            session_category_name,
            session_category_color,
            room,
            notes,
            tujuan_pembelajaran,
            materi_pembelajaran
          `)
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true })
          .range(from, from + batchSize - 1);

        if (batchError) {
          console.error('❌ Complete schedules batch error:', batchError);
          throw batchError;
        }

        if (!batchData || batchData.length === 0) {
          hasMore = false;
          break;
        }

        allData = [...allData, ...batchData];
        totalFetched += batchData.length;

        // Check if we got less than batch size (end of data)
        if (batchData.length < batchSize) {
          hasMore = false;
        } else {
          from += batchSize;
        }

        // Safety limit for production (500k records)
        if (totalFetched >= MAX_TOTAL_ITEMS) {
          console.log(`⚠️ Reached maximum limit of ${MAX_TOTAL_ITEMS} records`);
          hasMore = false;
        }
      }

      console.log(`✅ Complete schedules data fetched: ${allData.length} records (Total batches: ${Math.ceil(totalFetched / batchSize)})`);

      // Log distribution untuk debugging
      const weekDistribution = allData.reduce((acc: any, s: any) => {
        acc[s.academic_week] = (acc[s.academic_week] || 0) + 1;
        return acc;
      }, {});

      console.log('📊 Week distribution:', weekDistribution);
      console.log('📅 Available weeks:', Object.keys(weekDistribution).sort((a, b) => parseInt(a) - parseInt(b)));

      return allData;
    },
    // ✅ OPTIMIZED CACHING: Cache for longer since this is complete data
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false, // Don't refetch on focus for performance
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
  });
};

// 📊 UTILITY: Get total pages count
export const useSchedulesTotalPages = () => {
  return useQuery({
    queryKey: ['schedules-total-pages'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('schedules_view')
        .select('*', { count: 'exact', head: true })
        .not('day_of_week', 'is', null);

      if (error) {
        console.error('Error getting total count:', error);
        throw error;
      }

      const totalPages = Math.ceil((count || 0) / ITEMS_PER_PAGE);
      console.log(`📊 Total records: ${count}, Total pages: ${totalPages}`);

      return {
        totalRecords: count || 0,
        totalPages,
        itemsPerPage: ITEMS_PER_PAGE
      };
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// 🎯 HOOK: Efficient Single Week Data (untuk navigasi cepat)
export const useSchedulesSingleWeek = (weekNumber: number, classId?: string) => {
  return useQuery({
    queryKey: ['schedules-single-week', weekNumber, classId],
    queryFn: async () => {
      console.log(`📅 Fetching single week ${weekNumber} for class ${classId || 'all'}`);

      let query = supabase
        .from('schedules_view')
        .select(`
          id,
          academic_week,
          day_of_week,
          start_time,
          end_time,
          subject_name,
          subject_color,
          class_name,
          class_id,
          subject_id,
          teacher_name,
          session_category_id,
          session_category_name,
          session_category_color,
          room,
          notes,
          tujuan_pembelajaran,
          materi_pembelajaran
        `)
        .eq('academic_week', weekNumber)
        .not('day_of_week', 'is', null)
        .order('day_of_week', { ascending: true })
        .order('start_time', { ascending: true });

      // Add class filter if specified
      if (classId) {
        query = query.eq('class_id', classId);
      }

      const { data, error } = await query;

      if (error) {
        console.error(`Error fetching week ${weekNumber}:`, error);
        throw error;
      }

      console.log(`✅ Single week ${weekNumber} fetched: ${data?.length || 0} records`);
      return data || [];
    },
    enabled: weekNumber > 0 && weekNumber <= 24,
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// 🎯 EXAMPLE USAGE (as comments for reference):
/*
// ✅ SOLUSI UTAMA: Untuk mengambil SEMUA data dengan paginasi otomatis
const { data: allSchedules, isLoading, error } = useSchedulesComplete();
console.log('Semua Data Jadwal (1-24 minggu):', allSchedules);

// ✅ ALTERNATIF EFISIEN: Untuk kalender yang hanya perlu 1 minggu
const { data: weekData } = useSchedulesSingleWeek(14, 'class-id-123');
console.log('Data Minggu 14:', weekData);

// Untuk halaman pertama (pageNumber = 0)
const { data: firstPageData } = useSchedulePage(0);
console.log('Data Halaman 1:', firstPageData);

// Untuk halaman kedua (pageNumber = 1)
const { data: secondPageData } = useSchedulePage(1);
console.log('Data Halaman 2:', secondPageData);

// Untuk infinite scroll
const {
  data: infiniteData,
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage
} = useSchedulesInfinite();

// Untuk calendar (hanya minggu yang diperlukan) - ENHANCED dengan paginasi
const { data: calendarData } = useSchedulesCalendar(14, 'class-id-123');

// Untuk range minggu tertentu
const { data: weekRangeData } = useSchedulesWeekRange(14, 24);
console.log('Data Minggu 14-24:', weekRangeData);
*/
