-- =====================================================
-- MIGRATION: ADD SEMESTERS TABLE
-- =====================================================
-- Script untuk menambahkan tabel semesters ke database yang sudah ada

-- 1. Buat tabel semesters
CREATE TABLE IF NOT EXISTS semesters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL, -- 'Semester 1', 'Semester 2'
  semester_number INTEGER NOT NULL CHECK (semester_number IN (1, 2)),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT FALSE,
  
  -- <PERSON><PERSON>i
  academic_year_id UUID NOT NULL REFERENCES academic_years(id) ON DELETE CASCADE,
  school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(semester_number, academic_year_id, school_id),
  CHECK (start_date < end_date)
);

-- 2. Buat indexes
CREATE INDEX IF NOT EXISTS idx_semesters_academic_year ON semesters(academic_year_id);
CREATE INDEX IF NOT EXISTS idx_semesters_school ON semesters(school_id);
CREATE INDEX IF NOT EXISTS idx_semesters_active ON semesters(is_active) WHERE is_active = true;

-- 3. Enable RLS
ALTER TABLE semesters ENABLE ROW LEVEL SECURITY;

-- 4. Buat RLS policies
DO $$ 
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view semesters from their school" ON semesters;
  DROP POLICY IF EXISTS "Users can insert semesters for their school" ON semesters;
  DROP POLICY IF EXISTS "Users can update semesters from their school" ON semesters;
  DROP POLICY IF EXISTS "Users can delete semesters from their school" ON semesters;
  
  -- Create new policies
  CREATE POLICY "Users can view semesters from their school" ON semesters
    FOR SELECT USING (school_id = get_user_school_id(auth.uid()));

  CREATE POLICY "Users can insert semesters for their school" ON semesters
    FOR INSERT WITH CHECK (school_id = get_user_school_id(auth.uid()));

  CREATE POLICY "Users can update semesters from their school" ON semesters
    FOR UPDATE USING (school_id = get_user_school_id(auth.uid()));

  CREATE POLICY "Users can delete semesters from their school" ON semesters
    FOR DELETE USING (school_id = get_user_school_id(auth.uid()));
END $$;

-- 5. Buat function untuk auto-generate semester
CREATE OR REPLACE FUNCTION create_default_semesters()
RETURNS TRIGGER AS $$
DECLARE
  semester1_start DATE;
  semester1_end DATE;
  semester2_start DATE;
  semester2_end DATE;
BEGIN
  -- Hitung tanggal semester berdasarkan tahun ajaran Indonesia
  -- Semester 1: Juli - Desember
  -- Semester 2: Januari - Juni (tahun berikutnya)
  
  semester1_start := NEW.start_date;
  semester1_end := DATE(EXTRACT(YEAR FROM NEW.start_date) || '-12-31');
  semester2_start := DATE((EXTRACT(YEAR FROM NEW.start_date) + 1) || '-01-01');
  semester2_end := NEW.end_date;
  
  -- Insert Semester 1
  INSERT INTO semesters (
    name, 
    semester_number, 
    start_date, 
    end_date, 
    academic_year_id, 
    school_id,
    is_active
  ) VALUES (
    'Semester 1',
    1,
    semester1_start,
    semester1_end,
    NEW.id,
    NEW.school_id,
    NEW.is_active -- Semester 1 aktif jika tahun ajaran aktif
  );
  
  -- Insert Semester 2
  INSERT INTO semesters (
    name, 
    semester_number, 
    start_date, 
    end_date, 
    academic_year_id, 
    school_id,
    is_active
  ) VALUES (
    'Semester 2',
    2,
    semester2_start,
    semester2_end,
    NEW.id,
    NEW.school_id,
    FALSE -- Semester 2 tidak aktif secara default
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Buat trigger untuk auto-generate semester
DROP TRIGGER IF EXISTS trigger_create_default_semesters ON academic_years;
CREATE TRIGGER trigger_create_default_semesters
  AFTER INSERT ON academic_years
  FOR EACH ROW
  EXECUTE FUNCTION create_default_semesters();

-- 7. Function untuk update active semester
CREATE OR REPLACE FUNCTION update_active_semester()
RETURNS TRIGGER AS $$
BEGIN
  -- Jika academic year diaktifkan, aktifkan semester 1 secara default
  IF NEW.is_active = TRUE AND OLD.is_active = FALSE THEN
    -- Nonaktifkan semua semester di sekolah ini
    UPDATE semesters 
    SET is_active = FALSE 
    WHERE school_id = NEW.school_id;
    
    -- Aktifkan semester 1 dari tahun ajaran yang baru diaktifkan
    UPDATE semesters 
    SET is_active = TRUE 
    WHERE academic_year_id = NEW.id 
      AND semester_number = 1 
      AND school_id = NEW.school_id;
  END IF;
  
  -- Jika academic year dinonaktifkan, nonaktifkan semua semesternya
  IF NEW.is_active = FALSE AND OLD.is_active = TRUE THEN
    UPDATE semesters 
    SET is_active = FALSE 
    WHERE academic_year_id = NEW.id 
      AND school_id = NEW.school_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. Buat trigger untuk update active semester
DROP TRIGGER IF EXISTS trigger_update_active_semester ON academic_years;
CREATE TRIGGER trigger_update_active_semester
  AFTER UPDATE ON academic_years
  FOR EACH ROW
  EXECUTE FUNCTION update_active_semester();

-- 9. Generate semester untuk academic years yang sudah ada
DO $$
DECLARE
  academic_year_record RECORD;
BEGIN
  -- Loop through existing academic years and create semesters
  FOR academic_year_record IN 
    SELECT * FROM academic_years 
    WHERE id NOT IN (SELECT DISTINCT academic_year_id FROM semesters)
  LOOP
    -- Call the function to create semesters for this academic year
    PERFORM create_default_semesters() FROM (SELECT academic_year_record.*) AS NEW;
  END LOOP;
END $$;

-- 10. Comments untuk dokumentasi
COMMENT ON TABLE semesters IS 'Tabel semester untuk sistem pendidikan Indonesia (Semester 1: Juli-Desember, Semester 2: Januari-Juni)';
COMMENT ON COLUMN semesters.semester_number IS 'Nomor semester: 1 atau 2';
COMMENT ON COLUMN semesters.is_active IS 'Semester yang sedang aktif (hanya satu per sekolah)';

-- 11. Buat function untuk trigger updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12. Buat trigger untuk updated_at
DROP TRIGGER IF EXISTS update_semesters_updated_at ON semesters;
CREATE TRIGGER update_semesters_updated_at 
  BEFORE UPDATE ON semesters 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Migration completed successfully
SELECT 'Semesters table migration completed successfully!' as status;
