import React, { useState, useMemo } from 'react';
import { createRoot } from 'react-dom/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Filter, Printer } from 'lucide-react';
import OverviewPrintLayout from './OverviewPrintLayout';
import { OverviewFilters } from './OverviewFilters';
import { YearlyCalendarView } from './YearlyCalendarView';
import { OverviewSummary } from './OverviewSummary';
import { useSchedulesComplete } from '@/hooks/useSchedulesPaginated';
import { useSchedulesSimple } from '@/hooks/useSchedulesSimple';
import { useActiveAcademicYear } from '@/hooks/useAcademicYears';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useScheduleSubjects } from '@/hooks/useScheduleSubjects';
import { useExtracurriculars } from '@/hooks/useExtracurriculars';

const ScheduleOverview = () => {
  // Filter states
  const [selectedClassId, setSelectedClassId] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [selectedSubjectId, setSelectedSubjectId] = useState<string | null>(null);
  const [selectedSemester, setSelectedSemester] = useState<string | null>(null);

  // Data hooks - use Complete hook because it has session_category_id field
  const { data: schedules = [], isLoading, error } = useSchedulesComplete();


  const { data: activeAcademicYear } = useActiveAcademicYear();
  const { academicWeeks } = useAcademicWeeks();
  const { data: scheduleSubjects = [] } = useScheduleSubjects();
  const { data: extracurriculars = [] } = useExtracurriculars();

  // Combine all subjects like in OverviewFilters
  const allSubjects = React.useMemo(() => {
    return [
      ...scheduleSubjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        type: 'subject' as const
      })),
      ...extracurriculars.map(extracurricular => ({
        id: extracurricular.id,
        name: extracurricular.name,
        type: 'extracurricular' as const
      }))
    ];
  }, [scheduleSubjects, extracurriculars]);

  // Get selected subject name
  const selectedSubjectName = selectedSubjectId
    ? allSubjects.find(s => s.id === selectedSubjectId)?.name || 'Mata Pelajaran Terpilih'
    : null;

  // Print Overview function with optimized layout
  const handlePrintOverview = async () => {
    try {
      // Import html2canvas dynamically
      const html2canvas = (await import('html2canvas')).default;

      // Create a temporary container for print layout
      const printContainer = document.createElement('div');
      printContainer.style.position = 'fixed';
      printContainer.style.top = '-9999px';
      printContainer.style.left = '-9999px';
      printContainer.style.width = '1200px';
      printContainer.style.height = '800px';
      printContainer.style.backgroundColor = '#ffffff';
      document.body.appendChild(printContainer);

      // Create React root and render print layout
      const root = createRoot(printContainer);

      // Render the optimized print layout
      root.render(
        <OverviewPrintLayout
          schedules={filteredSchedules}
          activeAcademicYear={activeAcademicYear}
          academicWeeks={academicWeeks}
          selectedSubjectName={selectedSubjectName}
        />
      );

      // Wait for render to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create canvas from the print layout
      const canvas = await html2canvas(printContainer, {
        scale: 2, // Higher quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 1200,
        height: 800
      });

      // Convert canvas to image and download
      const link = document.createElement('a');
      link.download = `Overview-Jadwal-${activeAcademicYear?.year_name || 'Unknown'}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();

      // Cleanup
      root.unmount();
      document.body.removeChild(printContainer);
    } catch (error) {
      console.error('Error printing overview:', error);
    }
  };

  // Filter schedules based on selected filters
  const filteredSchedules = useMemo(() => {
    console.log('🔍 Filtering schedules with:', {
      selectedClassId,
      selectedCategoryId,
      selectedSubjectId,
      selectedSemester,
      totalSchedules: schedules?.length || 0,
      sampleSchedule: schedules?.[0] // Show structure of first schedule
    });

    if (!schedules || schedules.length === 0) {
      console.log('❌ No schedules data available');
      return [];
    }

    return schedules.filter(schedule => {
      // Filter by class
      if (selectedClassId && schedule.class_id !== selectedClassId) {

        return false;
      }

      // Filter by category
      if (selectedCategoryId && schedule.session_category_id !== selectedCategoryId) {
        return false;
      }

      // Filter by subject
      if (selectedSubjectId && schedule.subject_id !== selectedSubjectId) {
        return false;
      }

      // Filter by semester
      if (selectedSemester && academicWeeks.length > 0) {
        const academicWeek = academicWeeks.find(week => week.weekNumber === schedule.academic_week);
        if (academicWeek) {
          const scheduleDate = new Date(academicWeek.startDate);
          const month = scheduleDate.getMonth() + 1; // 1-based month

          if (selectedSemester === '1') {
            // Semester 1: Juli-Desember (months 7-12)
            if (month < 7 || month > 12) {
              return false;
            }
          } else if (selectedSemester === '2') {
            // Semester 2: Januari-Juni (months 1-6)
            if (month < 1 || month > 6) {
              return false;
            }
          }
        }
      }

      return true;
    });
  }, [schedules, selectedClassId, selectedCategoryId, selectedSubjectId, selectedSemester, academicWeeks]);

  // Debug filtered results
  console.log('📊 Filtered schedules result:', {
    originalCount: schedules?.length || 0,
    filteredCount: filteredSchedules?.length || 0,
    filteredSchedules: filteredSchedules?.slice(0, 3) // Show first 3 for debugging
  });





  return (
    <div id="overview-container" className="space-y-6">
      {/* Header */}
      <Card className="bg-card border-border shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold text-foreground flex items-center gap-3">
              <Calendar className="h-8 w-8 text-primary" />
              Overview Jadwal
              {activeAcademicYear && (
                <span className="text-lg font-normal text-muted-foreground">
                  - {activeAcademicYear.year_name}
                </span>
              )}
            </CardTitle>

            {/* Print Button */}
            <Button
              onClick={handlePrintOverview}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              Print Overview
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Filters */}
      <Card className="bg-card border-border shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Filter className="h-5 w-5 text-primary" />
            Filter Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <OverviewFilters
            selectedClassId={selectedClassId}
            onClassChange={setSelectedClassId}
            selectedCategoryId={selectedCategoryId}
            onCategoryChange={setSelectedCategoryId}
            selectedSubjectId={selectedSubjectId}
            onSubjectChange={setSelectedSubjectId}
            selectedSemester={selectedSemester}
            onSemesterChange={setSelectedSemester}
          />
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      <OverviewSummary
        schedules={filteredSchedules}
        isLoading={isLoading}
      />

      {/* Yearly Calendar View */}
      <YearlyCalendarView
        schedules={filteredSchedules}
        isLoading={isLoading}
        selectedSemester={selectedSemester}
      />
    </div>
  );
};

export default ScheduleOverview;
