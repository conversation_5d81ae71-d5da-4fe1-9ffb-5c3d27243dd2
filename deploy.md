# 🚀 PANDUAN DEPLOY APLIKASI INDOJADWAL

## 📋 PERSIAPAN SEBELUM DEPLOY

### 1. Environment Variables yang Dibutuhkan
Hosting provider Anda harus menyediakan:
```bash
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_APP_NAME=IndoJadwal
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production
```

### 2. Build Commands
```bash
# Install dependencies
npm install

# Build untuk production
npm run build

# Preview build (optional)
npm run preview
```

## 🗄️ SETUP DATABASE DI HOSTING

### 1. Migrasi Data dari Development ke Production
Jika hosting menyediakan Supabase baru, Anda perlu:

#### A. Export Data dari Development
```sql
-- Export semua tabel penting
SELECT * FROM academic_years;
SELECT * FROM academic_weeks;
SELECT * FROM schools;
SELECT * FROM classes;
SELECT * FROM subjects;
SELECT * FROM schedule_subjects;
SELECT * FROM session_categories;
SELECT * FROM teachers;
SELECT * FROM class_schedules;
```

#### B. Buat Schema di Production Database
```sql
-- Jalankan semua migration scripts
-- Buat semua tabel, views, dan functions
-- Setup Row Level Security (RLS)
-- Enable Realtime untuk tabel yang diperlukan
```

### 2. Tabel yang Harus Ada di Production:
- ✅ `academic_years`
- ✅ `academic_weeks`
- ✅ `schools`
- ✅ `classes`
- ✅ `subjects`
- ✅ `schedule_subjects`
- ✅ `session_categories`
- ✅ `teachers`
- ✅ `schedules` (tabel utama jadwal)
- ✅ `schedules_view` (VIEW)

### 3. Enable Realtime
Di Supabase Dashboard production:
- Buka Database > Replication
- Enable Realtime untuk tabel: `schedules`

### 4. Production Performance Settings
Aplikasi dioptimasi untuk menangani:
- ✅ **500,000+ rows** dengan pagination
- ✅ **Batch size 10,000** untuk performa optimal
- ✅ **Progress logging** setiap 50,000 rows
- ✅ **Enterprise-grade error handling**

## 🔧 KONFIGURASI HOSTING

### 1. Build Settings
```bash
# Build Command
npm run build

# Output Directory
dist

# Install Command
npm install
```

### 2. Environment Variables di Hosting
Set di hosting dashboard:
```
VITE_SUPABASE_URL = [URL dari hosting Supabase]
VITE_SUPABASE_ANON_KEY = [Anon key dari hosting Supabase]
VITE_APP_NAME = IndoJadwal
VITE_APP_VERSION = 1.0.0
VITE_APP_ENV = production
```

## 🚀 LANGKAH DEPLOY

### 1. Upload Code
- Upload semua file project ke hosting
- Pastikan `package.json` dan `vite.config.ts` ter-upload

### 2. Set Environment Variables
- Masukkan credentials Supabase dari hosting
- Set semua environment variables yang diperlukan

### 3. Build & Deploy
- Jalankan build command
- Deploy ke production

### 4. Test Production
- Akses URL production
- Test semua fitur utama:
  - ✅ Login/Authentication
  - ✅ Calendar display
  - ✅ CRUD operations
  - ✅ Real-time sync
  - ✅ Data pagination (minggu 1-24)

## 🔍 TROUBLESHOOTING

### Masalah Umum:
1. **Blank page** → Check console errors, environment variables
2. **Database connection error** → Verify Supabase credentials
3. **Data tidak muncul** → Check RLS policies, table permissions
4. **Real-time tidak berfungsi** → Enable Realtime di Supabase

### Debug Commands:
```bash
# Check build
npm run build

# Check preview
npm run preview

# Check environment
console.log(import.meta.env)
```

## 📞 SUPPORT
Jika ada masalah saat deploy, periksa:
- Console browser untuk errors
- Network tab untuk failed requests
- Supabase logs untuk database errors
