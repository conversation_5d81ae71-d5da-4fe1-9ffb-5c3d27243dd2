import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { useClasses } from '@/hooks/useClasses';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useCreateSubject } from '@/hooks/useSubjects';
import { useCreateClassSubject } from '@/hooks/useClassSubjects';
import { toast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface AddSubjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCategoryId?: string;
}

const SUBJECT_COLORS = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
  '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
  '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF',
  '#FAD7A0', '#E8DAEF', '#D1F2EB', '#FCF3CF', '#FADBD8'
];

export const AddSubjectModal: React.FC<AddSubjectModalProps> = ({
  isOpen,
  onClose,
  selectedCategoryId
}) => {
  const { data: classes = [] } = useClasses();
  const { data: sessionCategories = [] } = useSessionCategories();
  const createSubjectMutation = useCreateSubject(false); // Disable auto toast
  const createClassSubjectMutation = useCreateClassSubject(false); // Disable auto toast
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    category_id: selectedCategoryId || '',
    color: SUBJECT_COLORS[0],
    total_hours_per_year: 0
  });

  const [classMode, setClassMode] = useState<'single' | 'multiple' | 'all'>('single');
  const [selectedClasses, setSelectedClasses] = useState<string[]>([]);

  const sortedClasses = [...classes].sort((a, b) => {
    if (a.level !== b.level) return a.level.localeCompare(b.level);
    if (a.grade !== b.grade) return a.grade - b.grade;
    return a.name.localeCompare(b.name);
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClassSelection = (classId: string, checked: boolean) => {
    if (checked) {
      setSelectedClasses(prev => [...prev, classId]);
    } else {
      setSelectedClasses(prev => prev.filter(id => id !== classId));
    }
  };

  const handleSelectAllClasses = () => {
    setSelectedClasses(classes.map(cls => cls.id));
  };

  const handleDeselectAllClasses = () => {
    setSelectedClasses([]);
  };

  // Function to check if code is unique
  const checkCodeUniqueness = async (code: string): Promise<boolean> => {
    if (!code.trim()) return true; // Empty code will be auto-generated

    try {
      const { data, error } = await supabase
        .from('subjects')
        .select('id')
        .eq('code', code.trim())
        .limit(1);

      if (error) {
        console.error('Error checking code uniqueness:', error);
        return true; // Allow if we can't check
      }

      return data.length === 0; // True if no existing subjects with this code
    } catch (error) {
      console.error('Error in checkCodeUniqueness:', error);
      return true; // Allow if we can't check
    }
  };

  const handleSubmit = async () => {
    console.log('=== STARTING SUBMIT PROCESS ===');
    console.log('Form data:', formData);
    console.log('Class mode:', classMode);
    console.log('Selected classes:', selectedClasses);

    // Validation (following reference pattern)
    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Nama pelajaran harus diisi",
        variant: "destructive"
      });
      return;
    }

    // Validasi panjang nama pelajaran (updated to 2 characters minimum)
    if (formData.name.trim().length < 2) {
      toast({
        title: "Error",
        description: "Nama pelajaran minimal 2 karakter",
        variant: "destructive"
      });
      return;
    }

    if (!formData.category_id) {
      toast({
        title: "Error",
        description: "Kategori harus dipilih",
        variant: "destructive"
      });
      return;
    }

    let targetClasses: string[] = [];
    if (classMode === 'all') {
      targetClasses = classes.map(cls => cls.id);
    } else if (classMode === 'multiple') {
      targetClasses = selectedClasses;
    } else {
      targetClasses = selectedClasses.slice(0, 1);
    }

    if (targetClasses.length === 0) {
      toast({
        title: "Error",
        description: "Minimal pilih satu kelas",
        variant: "destructive"
      });
      return;
    }

    console.log('Target classes:', targetClasses);

    try {
      const now = new Date().toISOString();

      // Get current user's school_id and active academic year (required by database) - once outside loop
      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      const { data: activeYear } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile?.school_id)
        .eq('is_active', true)
        .single();

      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Data sekolah atau tahun akademik tidak ditemukan');
      }

      // Create ONE subject first (better approach to avoid duplicate constraint)
      const timestamp = Date.now().toString();
      const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
      const nameCode = formData.name.trim().replace(/[^a-zA-Z]/g, '').substring(0, 3).toUpperCase();
      const uniqueCode = formData.code?.trim() || `${nameCode}${timestamp.slice(-4)}${randomSuffix}`;

      console.log('Generating unique code:', uniqueCode);

      // Siapkan data untuk disimpan ke Supabase (following reference structure + required fields)
      const subjectDataForSupabase = {
        name: formData.name.trim(),
        code: uniqueCode,
        color: formData.color,
        total_hours_per_year: formData.total_hours_per_year || 0,
        category_id: formData.category_id,
        school_id: profile.school_id, // Required by database
        academic_year_id: activeYear.id, // Required by database
        created_at: now,
        updated_at: now
      };

      console.log('=== CREATING SUBJECT ===');
      console.log('Subject data:', subjectDataForSupabase);

      // Simpan ke Supabase (direct approach like reference)
      const { data: savedSubject, error: saveError } = await supabase
        .from('subjects')
        .insert(subjectDataForSupabase)
        .select()
        .single();

      if (saveError) {
        console.error('Error saving subject:', saveError);
        throw new Error(`Gagal menyimpan mata pelajaran: ${saveError.message}`);
      }

      console.log('Subject saved:', savedSubject);

      // Now create class relations for each target class
      console.log('=== CREATING CLASS RELATIONS ===');
      for (let i = 0; i < targetClasses.length; i++) {
        const classId = targetClasses[i];

        // Simpan relasi pelajaran-kelas ke tabel class_subjects (following reference)
        const classSubjectData = {
          class_id: classId,
          subject_id: savedSubject.id,
          hours_per_week: 0, // Default 0 (from reference)
          hours_per_year: formData.total_hours_per_year || 0,
          created_at: now,
          updated_at: now
        };

        console.log(`Creating class subject relation ${i + 1}/${targetClasses.length}:`, classSubjectData);

        const { data: savedClassSubject, error: classSubjectError } = await supabase
          .from('class_subjects')
          .insert(classSubjectData)
          .select()
          .single();

        if (classSubjectError) {
          console.error('Error saving class subject:', classSubjectError);
          throw new Error(`Gagal menyimpan relasi kelas: ${classSubjectError.message}`);
        }

        console.log('Class subject relation saved:', savedClassSubject);
      }

      // Force refresh all related queries
      console.log('=== REFRESHING QUERIES ===');
      await queryClient.invalidateQueries({ queryKey: ['subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] });
      await queryClient.invalidateQueries({ queryKey: ['class-subjects-by-category'] });

      toast({
        title: "Berhasil",
        description: `Mata pelajaran berhasil ditambahkan ke ${targetClasses.length} kelas`
      });

      // Reset form
      setFormData({
        name: '',
        code: '',
        category_id: selectedCategoryId || '',
        color: SUBJECT_COLORS[0],
        total_hours_per_year: 0
      });
      setClassMode('single');
      setSelectedClasses([]);

      console.log('=== SUBMIT COMPLETED SUCCESSFULLY ===');
      onClose();
    } catch (error) {
      console.error('=== ERROR IN SUBMIT PROCESS ===');
      console.error('Error details:', error);
      console.error('Error message:', error?.message);
      console.error('Error stack:', error?.stack);

      toast({
        title: "Error",
        description: `Gagal menambahkan mata pelajaran: ${error?.message || 'Unknown error'}`,
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg bg-gray-900 border-gray-700 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-white">Tambah Mata Pelajaran</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Nama Pelajaran */}
          <div>
            <Label htmlFor="name" className="text-white">Nama Pelajaran</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="bg-gray-800 border-gray-600 text-white"
              placeholder="Contoh: Matematika"
            />
          </div>

          {/* Kode Pelajaran */}
          <div>
            <Label htmlFor="code" className="text-white">Kode Pelajaran (Opsional)</Label>
            <Input
              id="code"
              value={formData.code}
              onChange={(e) => handleInputChange('code', e.target.value)}
              className="bg-gray-800 border-gray-600 text-white"
              placeholder="Contoh: MTK"
            />
          </div>

          {/* Kategori */}
          <div>
            <Label className="text-white">Kategori</Label>
            <Select value={formData.category_id} onValueChange={(value) => handleInputChange('category_id', value)}>
              <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                <SelectValue placeholder="Pilih kategori" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {sessionCategories.map(category => (
                  <SelectItem key={category.id} value={category.id} className="text-white hover:bg-gray-700">
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Mode Pemilihan Kelas */}
          <div>
            <Label className="text-white">Mode Pemilihan Kelas</Label>
            <RadioGroup value={classMode} onValueChange={(value: 'single' | 'multiple' | 'all') => setClassMode(value)} className="mt-2 space-y-2">
              <div className={`flex items-start space-x-3 p-3 rounded-lg border transition-all ${
                classMode === 'single'
                  ? 'border-blue-400 bg-blue-500/20 shadow-md'
                  : 'border-gray-500 bg-gray-700/50 hover:bg-gray-700/70'
              }`}>
                <RadioGroupItem value="single" id="single" className="mt-1 border-white data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500" />
                <div className="flex-1">
                  <Label htmlFor="single" className="text-white font-medium cursor-pointer">Satu Kelas</Label>
                  <p className="text-sm text-gray-300 mt-1">Pelajaran hanya akan ditambahkan ke satu kelas yang dipilih</p>
                </div>
              </div>
              <div className={`flex items-start space-x-3 p-3 rounded-lg border transition-all ${
                classMode === 'multiple'
                  ? 'border-blue-400 bg-blue-500/20 shadow-md'
                  : 'border-gray-500 bg-gray-700/50 hover:bg-gray-700/70'
              }`}>
                <RadioGroupItem value="multiple" id="multiple" className="mt-1 border-white data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500" />
                <div className="flex-1">
                  <Label htmlFor="multiple" className="text-white font-medium cursor-pointer">Beberapa Kelas</Label>
                  <p className="text-sm text-gray-300 mt-1">Pilih beberapa kelas tertentu untuk ditambahkan pelajaran</p>
                </div>
              </div>
              <div className={`flex items-start space-x-3 p-3 rounded-lg border transition-all ${
                classMode === 'all'
                  ? 'border-blue-400 bg-blue-500/20 shadow-md'
                  : 'border-gray-500 bg-gray-700/50 hover:bg-gray-700/70'
              }`}>
                <RadioGroupItem value="all" id="all" className="mt-1 border-white data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500" />
                <div className="flex-1">
                  <Label htmlFor="all" className="text-white font-medium cursor-pointer">Semua Kelas</Label>
                  <p className="text-sm text-gray-300 mt-1">Pelajaran akan ditambahkan ke semua 6 kelas sekaligus</p>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Pemilihan Kelas */}
          {classMode !== 'all' && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-white">Kelas *</Label>
                {classMode === 'multiple' && (
                  <div className="space-x-2">
                    <Button size="sm" variant="outline" onClick={handleSelectAllClasses} type="button">
                      Pilih Semua
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleDeselectAllClasses} type="button">
                      Hapus Semua
                    </Button>
                  </div>
                )}
              </div>
              <div className="bg-gray-700/50 rounded-lg p-4 border border-gray-500">
                <p className="text-sm text-gray-200 mb-3">Pilih kelas untuk pelajaran ini</p>
                <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                  {sortedClasses.map(cls => (
                    <div key={cls.id} className={`flex items-center space-x-3 p-2 rounded border transition-all ${
                      selectedClasses.includes(cls.id)
                        ? 'bg-blue-500/20 border-blue-400'
                        : 'bg-gray-600/30 border-gray-500 hover:bg-gray-600/50'
                    }`}>
                      <Checkbox
                        id={cls.id}
                        checked={selectedClasses.includes(cls.id)}
                        onCheckedChange={(checked) => {
                          if (classMode === 'single') {
                            setSelectedClasses(checked ? [cls.id] : []);
                          } else {
                            handleClassSelection(cls.id, !!checked);
                          }
                        }}
                        className="border-white data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                      />
                      <Label htmlFor={cls.id} className="text-white text-sm cursor-pointer flex-1">
                        {cls.name} ({cls.level} {cls.grade})
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* JP Per Tahun */}
          <div>
            <Label htmlFor="hours" className="text-white">JP Per Tahun</Label>
            <Input
              id="hours"
              type="number"
              value={formData.total_hours_per_year}
              onChange={(e) => handleInputChange('total_hours_per_year', parseInt(e.target.value) || 0)}
              className="bg-gray-800 border-gray-600 text-white"
              placeholder="0"
              min="0"
            />
          </div>

          {/* Warna */}
          <div>
            <Label className="text-white">Warna</Label>
            <div className="grid grid-cols-10 gap-2 mt-2">
              {SUBJECT_COLORS.map(color => (
                <button
                  key={color}
                  type="button"
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    formData.color === color ? 'border-white scale-110' : 'border-gray-600'
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => handleInputChange('color', color)}
                />
              ))}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={createSubjectMutation.isPending}
              className="bg-lime-500 hover:bg-lime-600"
            >
              {createSubjectMutation.isPending ? 'Menyimpan...' : 'Simpan'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
