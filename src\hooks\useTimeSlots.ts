
import { useTimeSessions } from '@/hooks/useTimeSessions';

export const useTimeSlots = () => {
  const { data: timeSessions } = useTimeSessions();

  const generateTimeSlots = () => {
    let startHour = 3; // Default fallback start
    let endHour = 22; // Default fallback end

    // DEBUG: Log time sessions data
    console.log('📊 Time Sessions Debug:', {
      timeSessionsCount: timeSessions?.length || 0,
      timeSessions: timeSessions?.map(session => ({
        id: session.id,
        session_name: session.session_name,
        start_time: session.start_time,
        end_time: session.end_time,
        session_number: session.session_number
      }))
    });

    if (timeSessions?.length) {
      // Get earliest start time and latest end time from sessions
      const startTimes = timeSessions.map(session => {
        const time = session.start_time?.slice(0, 5) || '03:00';
        return parseInt(time.split(':')[0]);
      });

      const endTimes = timeSessions.map(session => {
        const time = session.end_time?.slice(0, 5) || '22:00';
        return parseInt(time.split(':')[0]);
      });

      startHour = Math.min(...startTimes);
      endHour = Math.max(...endTimes);

      // DEBUG: Log calculated hours
      console.log('⏰ Calculated Hours:', {
        startTimes,
        endTimes,
        calculatedStartHour: startHour,
        calculatedEndHour: endHour
      });
    }

    // Generate hourly slots from start to end
    const slots = [];
    for (let hour = startHour; hour <= endHour; hour++) {
      const timeString = `${hour.toString().padStart(2, '0')}:00`;
      
      slots.push({
        id: `slot-${hour}`,
        time: timeString,
        displayTime: timeString,
        hour: hour
      });
    }

    return {
      slots,
      startHour,
      endHour,
      totalHours: endHour - startHour + 1
    };
  };

  return generateTimeSlots();
};
