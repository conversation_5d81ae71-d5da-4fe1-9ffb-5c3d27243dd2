import React, { useRef, useMemo, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin, { Draggable } from '@fullcalendar/interaction';
import idLocale from '@fullcalendar/core/locales/id';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { ExpandableCategorySidebar } from './ExpandableCategorySidebar';
import { AddScheduleModal } from './AddScheduleModal';
import { EditScheduleModal } from './EditScheduleModal';
import { ScheduleHeaderControls } from './ScheduleHeaderControls';
import { WeeklyScrollableNavigation } from './WeeklyScrollableNavigation';
import { WeeklyActivityList } from './WeeklyActivityList';

import { useDynamicLayout } from '@/hooks/useScreenSize';
import { useSchedules, useCreateSchedule, useUpdateSchedule } from '@/hooks/useSchedules';
import { useSchedulesSimple } from '@/hooks/useSchedulesSimple';
import { useSchedulesCalendar, useSchedulesComplete } from '@/hooks/useSchedulesPaginated';

import { useScheduleCalendarState } from '@/hooks/useScheduleCalendarState';
import { useTimeSlots } from '@/hooks/useTimeSlots';
import { getSubjectColorWithCategory } from '@/utils/subjectColors';
import { useRealTimeSync, useScheduleRealTime } from '@/hooks/useRealTimeSync';
import {
  snapToFiveMinutes,
  snapDateToFiveMinutes,
  addDurationAndSnap,
  snapResizeToFiveMinutes,
  snapDragToFiveMinutes,
  calculateDurationMinutes,
  hasTimeConflict,
  logTimeOperation
} from '@/utils/timeUtils';
import { useToast } from '@/hooks/use-toast';
import { useActiveAcademicYear } from '@/hooks/useAcademicYears';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { supabase } from '@/integrations/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import './calendar.css';

export const ScheduleCalendar: React.FC = () => {
  const calendarRef = useRef<FullCalendar>(null);
  const draggableRef = useRef<Draggable | null>(null);
  const resizingEventRef = useRef<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // ✅ REAL-TIME SYNC: Initialize comprehensive real-time synchronization
  useRealTimeSync();
  const { forceRefresh } = useScheduleRealTime();

  // ✅ ENHANCED: Listen for force calendar refresh events (especially for day copy)
  useEffect(() => {
    const handleForceCalendarRefresh = () => {
      console.log('📅 Force calendar refresh event received');
      const calendarApi = calendarRef.current?.getApi();
      if (calendarApi) {
        calendarApi.refetchEvents();
        console.log('📅 Calendar events refetched via custom event');
      }
    };

    window.addEventListener('forceCalendarRefresh', handleForceCalendarRefresh);
    return () => {
      window.removeEventListener('forceCalendarRefresh', handleForceCalendarRefresh);
    };
  }, []);

  // 🔧 FIRST: Get state variables before using them
  const {
    isAddModalOpen,
    isEditModalOpen,
    selectedTimeSlot,
    selectedWeek,
    selectedDate,
    selectedClassId,
    activeSchedule,
    editingSchedule,
    draggedSubject,
    setSelectedClassId,
    setActiveSchedule,
    setDraggedSubject,
    setSelectedTimeSlot,
    setIsAddModalOpen,
    getCurrentWeekDate,
    handleTimeSlotClick,
    handleWeekSelect,
    handleScheduleEdit,
    closeModal,
    closeEditModal,
  } = useScheduleCalendarState();

  // � SOLUSI UTAMA: Use complete schedules with smart pagination
  const { data: schedules, isLoading: schedulesLoading, error: schedulesError } = useSchedulesComplete();

  console.log('📊 Schedule Data Status (PAGINATED):', {
    selectedWeek,
    selectedClassId,
    schedulesCount: schedules?.length || 0,
    isLoading: schedulesLoading,
    error: schedulesError,
    usingPagination: true
  });

  const createScheduleMutation = useCreateSchedule();
  const updateScheduleMutation = useUpdateSchedule();
  const { startHour, endHour, totalHours } = useTimeSlots();
  const { data: activeAcademicYear } = useActiveAcademicYear();
  const { academicWeeks } = useAcademicWeeks();
  const { data: sessionCategories = [] } = useSessionCategories();

  // Get dynamic layout dimensions for advanced responsiveness
  const {
    containerWidth,
    isCompact
  } = useDynamicLayout();

  // Initialize external draggable for sidebar subjects
  useEffect(() => {
    const initializeDraggable = () => {
      const sidebarContainer = document.querySelector('.expandable-category-sidebar') as HTMLElement;
      if (sidebarContainer && !draggableRef.current) {
        draggableRef.current = new Draggable(sidebarContainer, {
          itemSelector: '[data-subject]',
          eventData: function(eventEl) {
            const subjectData = eventEl.dataset.subject;
            if (subjectData) {
              try {
                const subject = JSON.parse(subjectData);

                // ✅ PERBAIKAN: Gunakan warna yang benar dari subject
                const subjectColor = subject.color || '#6B7280';

                console.log('🎨 Draggable Event Data:', {
                  name: subject.name,
                  id: subject.id,
                  color: subjectColor,
                  type: subject.type,
                  source: subject.source
                });

                return {
                  title: subject.name,
                  id: subject.id,
                  duration: '00:45:00', // Default 45-minute duration
                  backgroundColor: subjectColor,
                  borderColor: subjectColor,
                  textColor: '#FFFFFF',
                  extendedProps: {
                    subject: subject,
                    type: 'external'
                  }
                };
              } catch (error) {
                console.error('Error parsing subject data:', error);
              }
            }
            return {
              title: eventEl.textContent || 'Unknown Subject',
              duration: '00:45:00',
              backgroundColor: '#6B7280',
              borderColor: '#6B7280',
              textColor: '#FFFFFF'
            };
          }
        });
        console.log('✅ External draggable initialized');
      }
    };

    // Initialize after a short delay to ensure DOM is ready
    const timer = setTimeout(initializeDraggable, 100);

    return () => {
      clearTimeout(timer);
      if (draggableRef.current) {
        draggableRef.current.destroy();
        draggableRef.current = null;
      }
    };
  }, [selectedClassId]); // Re-initialize when class changes

  // Calculate dynamic height for CONSISTENT 30px slots
  const calendarHeight = useMemo(() => {
    // Base height for header and controls
    const headerHeight = 140;
    // CONSISTENT: 30px per 30-minute slot (matches CSS)
    const slotHeight = 30;
    // Calculate total 30-minute slots (2 per hour)
    const total30MinSlots = totalHours * 2;
    // Total height = header + (slots × height) + padding
    const calculatedHeight = headerHeight + (total30MinSlots * slotHeight);
    // Ensure minimum height for usability
    const minHeight = 500; // Reduced minimum
    // Add padding to ensure all slots are visible
    const extraPadding = 40; // Reduced padding
    return Math.max(calculatedHeight + extraPadding, minHeight);
  }, [totalHours]);

  // Dynamic time range for calendar
  const timeRange = useMemo(() => {
    const slotMinTime = `${startHour.toString().padStart(2, '0')}:00:00`;
    const slotMaxTime = `${(endHour + 1).toString().padStart(2, '0')}:00:00`;

    // DEBUG: Log time range calculation
    console.log('🕐 Calendar Time Range Debug:', {
      startHour,
      endHour,
      totalHours,
      slotMinTime,
      slotMaxTime,
      calendarHeight: calendarHeight,
      slotHeight: 30, // 30px per 30-minute slot
      total30MinSlots: totalHours * 2,
      expectedGridHeight: totalHours * 2 * 30 // Total expected grid height
    });

    return { slotMinTime, slotMaxTime };
  }, [startHour, endHour, totalHours, calendarHeight]);

  // Calculate current calendar date based on selected week
  const currentCalendarDate = useMemo(() => {
    const weekDate = getCurrentWeekDate();

    console.log('📅 Calendar Date Update:', {
      selectedWeek,
      weekDate: weekDate.toISOString().split('T')[0],
      academicYear: activeAcademicYear?.year_name
    });

    return weekDate;
  }, [selectedWeek, getCurrentWeekDate, activeAcademicYear]);

  // Update calendar view when selectedWeek changes
  useEffect(() => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi && currentCalendarDate) {
      console.log('🔄 Updating calendar to week:', selectedWeek, 'date:', currentCalendarDate);
      calendarApi.gotoDate(currentCalendarDate);
    }
  }, [selectedWeek, currentCalendarDate]);

  // ✅ AGGRESSIVE REAL-TIME CALENDAR REFRESH: Force calendar to re-render when schedules data changes
  useEffect(() => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi && schedules) {
      console.log('📡 AGGRESSIVE Real-time calendar refresh triggered by schedules change:', schedules.length, 'schedules');
      console.log('📡 Schedules data:', schedules);

      // AGGRESSIVE: Multiple refresh methods
      calendarApi.refetchEvents();

      // Force re-render after short delay
      setTimeout(() => {
        calendarApi.refetchEvents();
      }, 100);

      // Additional force refresh
      setTimeout(() => {
        calendarApi.refetchEvents();
      }, 500);
    }
  }, [schedules]); // Trigger when schedules data changes

  // ✅ REAL-TIME SESSION CATEGORIES REFRESH: Update when session categories change
  useEffect(() => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi && sessionCategories) {
      console.log('📡 Real-time calendar refresh triggered by session categories change:', sessionCategories.length, 'categories');

      // Force calendar to refresh its events to update colors
      calendarApi.refetchEvents();
    }
  }, [sessionCategories]); // Trigger when session categories change

  // Convert schedules to FullCalendar events
  const calendarEvents = useMemo(() => {
    console.log('🔥 CALENDAR EVENTS PROCESSING:', {
      schedulesLength: schedules?.length || 0,
      selectedWeek,
      selectedClassId,
      rawSchedules: schedules
    });

    // 🚨 PERINGATAN: Jika belum pilih kelas, jangan tampilkan jadwal
    if (!selectedClassId) {
      console.log('⚠️ No class selected - showing empty calendar to prevent UI overlap');
      return [];
    }

    if (!schedules) {
      console.log('❌ No schedules data available');
      return [];
    }

    const filteredSchedules = schedules.filter((schedule: any) => {
      // 🔧 PERBAIKAN: Pastikan type matching yang benar
      const scheduleWeek = parseInt(schedule.academic_week);
      const selectedWeekInt = parseInt(selectedWeek.toString());
      const weekMatch = scheduleWeek === selectedWeekInt;
      const classMatch = !selectedClassId || schedule.class_id === selectedClassId;

      // 🔍 ENHANCED DEBUG: Log filtering process untuk SEMUA minggu
      if (scheduleWeek === selectedWeekInt) {
        console.log('🔍 FILTERING DEBUG - WEEK MATCH FOUND:', {
          scheduleId: schedule.id,
          scheduleWeek: schedule.academic_week,
          scheduleWeekInt: scheduleWeek,
          selectedWeek,
          selectedWeekInt,
          weekMatch,
          scheduleClassId: schedule.class_id,
          selectedClassId,
          classMatch,
          startTime: schedule.start_time,
          endTime: schedule.end_time,
          subjectName: schedule.subject_name,
          className: schedule.class_name,
          finalResult: weekMatch && classMatch
        });
      }

      return weekMatch && classMatch;
    });

    console.log('🔥 FILTERED SCHEDULES:', {
      totalSchedules: schedules.length,
      filteredCount: filteredSchedules.length,
      selectedWeek,
      selectedClassId,
      filteredSchedules: filteredSchedules.slice(0, 3) // Show first 3 for debugging
    });

    // 🔍 ENHANCED DEBUG: Analisis untuk SEMUA minggu
    if (filteredSchedules.length === 0) {
      console.log('🚨 TIDAK ADA HASIL untuk minggu:', selectedWeek);
      console.log('🔍 Raw schedules sample:', schedules?.slice(0, 5));
      console.log('🔍 Schedules untuk minggu ini:', schedules?.filter(s => s.academic_week === selectedWeek));
      console.log('🔍 Schedules untuk kelas ini:', schedules?.filter(s => s.class_id === selectedClassId));

      // 🔍 ANALISIS DISTRIBUSI MINGGU
      const weekDistribution = schedules?.reduce((acc: any, s: any) => {
        acc[s.academic_week] = (acc[s.academic_week] || 0) + 1;
        return acc;
      }, {});
      console.log('🔍 DISTRIBUSI MINGGU:', weekDistribution);

      // 🔍 SAMPLE DATA SEMUA MINGGU
      const sampleAllWeeks = schedules?.filter(s => s.academic_week >= 1 && s.academic_week <= 24).slice(0, 5);
      console.log('🔍 SAMPLE SEMUA MINGGU 1-24:', sampleAllWeeks);
    }

    const calendarEvents = filteredSchedules
      .map((schedule: any) => {
        // ✅ ENHANCED: Handle both schedules_view and manual join data structures
        let subject, category;

        if (schedule.subject_name) {
          // Data from schedules_view (flattened structure)
          subject = {
            id: schedule.subject_id,
            name: schedule.subject_name || schedule.schedule_subject_name,
            code: schedule.subject_code || schedule.schedule_subject_code,
            color: schedule.subject_color || schedule.schedule_subject_color
          };
          category = {
            id: schedule.session_category_id,
            name: schedule.session_category_name,
            color: schedule.session_category_color
          };
        } else {
          // ✅ ENHANCED: Data from manual joins (nested structure) - handle KBM, EKSKUL, and other subjects

          // ✅ DIAGNOSTIC: Log raw schedule data structure first
          console.log(`🔍 RAW SCHEDULE DATA ANALYSIS:`, {
            schedule_id: schedule.id,
            subject_id: schedule.subject_id,
            hasSubjects: !!schedule.subjects,
            hasScheduleSubjects: !!schedule.schedule_subjects,
            hasExtracurriculars: !!schedule.extracurriculars,
            subjectsData: schedule.subjects,
            scheduleSubjectsData: schedule.schedule_subjects,
            extracurricularsData: schedule.extracurriculars,
            allKeys: Object.keys(schedule)
          });

          const subjectData = schedule.subjects || schedule.schedule_subjects || schedule.extracurriculars;
          const categoryData = subjectData?.session_categories || subjectData?.session_category;

          // ✅ FIXED: Determine subject type for better debugging
          let subjectType = 'unknown';
          if (schedule.subjects) subjectType = 'subjects';
          else if (schedule.schedule_subjects) subjectType = 'schedule_subjects';
          else if (schedule.extracurriculars) subjectType = 'extracurriculars';

          console.log(`🔍 Processing ${subjectType} data for schedule:`, {
            schedule_id: schedule.id,
            subject_id: schedule.subject_id,
            subjectType,
            subjectData,
            categoryData,
            subjectDataExists: !!subjectData,
            subjectName: subjectData?.name
          });

          subject = {
            id: schedule.subject_id,
            name: subjectData?.name || `Unknown ${subjectType}`,
            code: subjectData?.code,
            color: subjectData?.color || '#6B7280'
          };
          category = {
            id: categoryData?.id,
            name: categoryData?.name,
            color: categoryData?.color
          };

          console.log(`✅ Processed subject data:`, {
            subject,
            category,
            originalType: subjectType
          });
        }

        const colors = getSubjectColorWithCategory(subject, category);

        console.log(`🎨 Calendar Event "${subject?.name}" - Colors:`, {
          subjectColor: subject?.color,
          categoryColor: category?.color,
          finalColors: colors,
          scheduleStructure: schedule.subject_name ? 'schedules_view' : 'manual_joins',
          rawScheduleData: {
            id: schedule.id,
            subject_id: schedule.subject_id,
            subjects: schedule.subjects,
            schedule_subjects: schedule.schedule_subjects,
            extracurriculars: schedule.extracurriculars
          }
        });

        const weekDate = getCurrentWeekDate();
        const scheduleDate = new Date(weekDate);
        scheduleDate.setDate(scheduleDate.getDate() + (schedule.day_of_week - 1));

        const startDateTime = new Date(scheduleDate);
        const [startHour, startMinute] = schedule.start_time.split(':');
        startDateTime.setHours(parseInt(startHour), parseInt(startMinute));

        const endDateTime = new Date(scheduleDate);
        const [endHour, endMinute] = schedule.end_time.split(':');
        endDateTime.setHours(parseInt(endHour), parseInt(endMinute));

        // DEBUG: Log event duration calculation
        const durationMinutes = (endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60);
        const expectedSlots = durationMinutes / 30; // 30 minutes per slot
        const expectedHeightPx = expectedSlots * 30; // 30px per slot

        console.log(`📏 Event "${subject?.name}" Duration Debug:`, {
          startTime: schedule.start_time,
          endTime: schedule.end_time,
          durationMinutes,
          expectedSlots,
          expectedHeightPx: `${expectedHeightPx}px`,
          dayOfWeek: schedule.day_of_week
        });

        const calendarEvent = {
          id: schedule.id,
          title: subject?.name || 'Mata Pelajaran',
          start: startDateTime.toISOString(),
          end: endDateTime.toISOString(),
          backgroundColor: colors.background,
          borderColor: colors.border,
          textColor: colors.text,
          extendedProps: {
            schedule,
            teacher: { id: schedule.teacher_id, full_name: schedule.teacher_name },
            class: { id: schedule.class_id, name: schedule.class_name },
            subject: subject,
            category: category,
            originalColor: colors.background // Store original color for consistency
          }
        };

        console.log('🔥 CREATED CALENDAR EVENT:', calendarEvent);
        return calendarEvent;
      });

    console.log('🔥 FINAL CALENDAR EVENTS:', {
      eventsCount: calendarEvents.length,
      events: calendarEvents
    });

    return calendarEvents;
  }, [schedules, selectedWeek, selectedClassId, getCurrentWeekDate, sessionCategories]);

  const handleEventClick = (info: any) => {
    const schedule = info.event.extendedProps.schedule;
    handleScheduleEdit(schedule);
  };

  const handleDateSelect = (selectInfo: any) => {
    if (!selectedClassId) {
      toast({
        title: "❌ Gagal",
        description: "Pilih kelas terlebih dahulu",
        variant: "destructive",
      });
      const calendarApi = calendarRef.current?.getApi();
      if (calendarApi) {
        calendarApi.unselect();
      }
      return;
    }

    const selectedDate = new Date(selectInfo.start);
    const dayOfWeek = selectedDate.getDay();
    const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
    
    // ✅ NEW: Snap selected time to 5-minute intervals
    const rawStartTime = selectInfo.start.toTimeString().slice(0, 5);
    const rawEndTime = selectInfo.end.toTimeString().slice(0, 5);
    const snappedStartTime = snapToFiveMinutes(rawStartTime);
    const snappedEndTime = snapToFiveMinutes(rawEndTime);

    const timeSlot = {
      dayIndex: adjustedDayOfWeek - 1,
      date: selectedDate.toISOString().split('T')[0],
      academicWeek: selectedWeek,
      time: snappedStartTime,
      start_time: snappedStartTime,
      end_time: snappedEndTime,
      // Additional context for better auto-population
      source: 'calendar_select'
    };

    logTimeOperation('Calendar Selection Time Snapping', {
      rawStartTime,
      rawEndTime,
      snappedStartTime,
      snappedEndTime
    });
    
    setSelectedTimeSlot(timeSlot);
    setIsAddModalOpen(true);
    
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.unselect();
    }
  };

  const handleEventDrop = async (info: any) => {
    const schedule = info.event.extendedProps.schedule;
    const newDate = new Date(info.event.start);
    const dayOfWeek = newDate.getDay();
    const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;

    // ✅ 5-MINUTE SNAPPING: Calculate duration and apply snapping
    const originalDuration = calculateDurationMinutes(schedule.start_time, schedule.end_time);
    const rawNewStartTime = info.event.start.toTimeString().slice(0, 5);

    // Apply 5-minute snapping to drag operation
    const snappedTimes = snapDragToFiveMinutes(originalDuration, rawNewStartTime);
    const newStartTime = snappedTimes.startTime;
    const newEndTime = snappedTimes.endTime;

    logTimeOperation('Event Drag - 5-Minute Snapping', {
      originalStartTime: schedule.start_time,
      originalEndTime: schedule.end_time,
      originalDuration,
      rawNewStartTime,
      snappedStartTime: newStartTime,
      snappedEndTime: newEndTime,
      snapDelta: `${rawNewStartTime} → ${newStartTime}`
    });

    // Check for conflicts
    const conflictData = {
      class_id: schedule.class_id,
      day_of_week: adjustedDayOfWeek,
      start_time: newStartTime,
      end_time: newEndTime,
      academic_week: selectedWeek
    };

    if (hasTimeConflict(schedules || [], conflictData, schedule.id)) {
      info.revert();
      toast({
        title: "⚠️ Konflik Jadwal",
        description: `Jadwal bertabrakan dengan jadwal lain pada ${newStartTime}-${newEndTime}`,
        variant: "destructive",
      });
      return;
    }

    try {
      await updateScheduleMutation.mutateAsync({
        id: schedule.id,
        day_of_week: adjustedDayOfWeek,
        start_time: newStartTime,
        end_time: newEndTime,
        schedule_date: newDate.toISOString().split('T')[0],
        academic_week: selectedWeek
      });

      toast({
        title: "✅ Berhasil",
        description: "Jadwal berhasil dipindahkan",
      });
    } catch (error) {
      console.error('Error moving schedule:', error);
      info.revert();
      toast({
        title: "❌ Gagal",
        description: "Gagal memindahkan jadwal",
        variant: "destructive",
      });
    }
  };

  // ✅ PERBAIKAN: Handler resize yang benar
  const handleEventResize = async (info: any) => {
    const schedule = info.event.extendedProps.schedule;
    const rawNewEndTime = info.event.end.toTimeString().slice(0, 5);

    console.log('🔧 Event Resize Debug:', {
      originalStartTime: schedule.start_time,
      originalEndTime: schedule.end_time,
      rawNewEndTime,
      eventId: schedule.id,
      eventTitle: info.event.title
    });

    // ✅ Apply 5-minute snapping to resize operation
    const snappedTimes = snapResizeToFiveMinutes(
      schedule.start_time,
      schedule.end_time,
      rawNewEndTime
    );

    const newStartTime = snappedTimes.startTime;
    const newEndTime = snappedTimes.endTime;
    const newDuration = snappedTimes.duration;

    // ✅ Minimum duration check (5 minutes)
    if (newDuration < 5) {
      info.revert();
      toast({
        title: "⚠️ Durasi Terlalu Pendek",
        description: "Durasi minimum adalah 5 menit",
        variant: "destructive",
      });
      return;
    }

    logTimeOperation('Event Resize - 5-Minute Snapping', {
      originalStartTime: schedule.start_time,
      originalEndTime: schedule.end_time,
      rawNewEndTime,
      snappedStartTime: newStartTime,
      snappedEndTime: newEndTime,
      newDuration,
      snapDelta: `${rawNewEndTime} → ${newEndTime}`
    });

    // Check for conflicts
    const conflictData = {
      class_id: schedule.class_id,
      day_of_week: schedule.day_of_week,
      start_time: newStartTime,
      end_time: newEndTime,
      academic_week: selectedWeek
    };

    if (hasTimeConflict(schedules || [], conflictData, schedule.id)) {
      info.revert();
      toast({
        title: "⚠️ Konflik Jadwal",
        description: `Jadwal bertabrakan dengan jadwal lain pada ${newStartTime}-${newEndTime}`,
        variant: "destructive",
      });
      return;
    }

    try {
      await updateScheduleMutation.mutateAsync({
        id: schedule.id,
        start_time: newStartTime,
        end_time: newEndTime,
      });

      toast({
        title: "✅ Berhasil",
        description: `Jadwal berhasil diubah menjadi ${newStartTime}-${newEndTime}`,
      });
    } catch (error) {
      console.error('Error resizing schedule:', error);
      info.revert();
      toast({
        title: "❌ Gagal",
        description: "Gagal mengubah durasi jadwal",
        variant: "destructive",
      });
    }
  };

  // ✅ Handler untuk visual feedback saat resize
  const handleEventResizeStart = (info: any) => {
    console.log('🎯 Resize Start:', info.event.title);
    // Tambahkan class untuk visual feedback
    const eventEl = info.el;
    eventEl.classList.add('fc-event-resizing-preview');
  };

  const handleEventResizeStop = (info: any) => {
    console.log('🎯 Resize Stop:', info.event.title);
    // Hapus class visual feedback
    const eventEl = info.el;
    eventEl.classList.remove('fc-event-resizing-preview');
  };

  // ✅ CUSTOM: Render a circular highlight for days with events in month view
  const handleDayCellDidMount = (arg: any) => {
    if (arg.view.type === 'dayGridMonth' && arg.events.length > 0) {
      const dayNumberEl = arg.el.querySelector('.fc-daygrid-day-number');
      if (dayNumberEl) {
        // Ensure the parent is positioned to contain the absolute child
        (dayNumberEl as HTMLElement).style.position = 'relative';
        (dayNumberEl as HTMLElement).style.zIndex = '1';

        const highlightEl = document.createElement('div');
        highlightEl.style.width = '26px';
        highlightEl.style.height = '26px';
        highlightEl.style.backgroundColor = '#EC4899'; // Pink
        highlightEl.style.borderRadius = '50%';
        highlightEl.style.position = 'absolute';
        highlightEl.style.top = '50%';
        highlightEl.style.left = '50%';
        highlightEl.style.transform = 'translate(-50%, -50%)';
        highlightEl.style.zIndex = '-1'; // Behind the number

        dayNumberEl.appendChild(highlightEl);
      }
    }
  };

  // ✅ REMOVED: All custom event handlers to restore FullCalendar default drag and drop behavior

  const handleCurrentWeekClick = () => {
    const currentWeek = academicWeeks.find(week => week.isCurrentWeek);
    if (currentWeek) {
      handleWeekSelect(currentWeek.weekNumber);
    }
  };

  const handleDrop = async (info: any) => {
    if (!selectedClassId) {
      toast({
        title: "❌ Gagal",
        description: "Pilih kelas terlebih dahulu",
        variant: "destructive",
      });
      return;
    }

    // Enhanced data extraction with priority order
    let subjectData: any = null;

    console.log('🎯 Drop event data:', {
      event: info.event,
      draggedEl: info.draggedEl,
      extendedProps: info.event?.extendedProps,
      dataset: info.draggedEl?.dataset
    });

    // Priority 1: FullCalendar Draggable extendedProps (NEW SYSTEM)
    if (info.event?.extendedProps?.subject) {
      subjectData = info.event.extendedProps.subject;
      console.log('📋 Using FullCalendar Draggable data:', subjectData);
    }
    // Priority 2: HTML5 drag data (FALLBACK)
    else if (info.draggedEl?.dataset?.subject) {
      try {
        subjectData = JSON.parse(info.draggedEl.dataset.subject);
        console.log('📋 Using HTML5 drag data:', subjectData);

        // ✅ ENHANCED: Ensure subject data has proper structure for database insertion
        // Map schedule_subjects data to expected format
        if (subjectData.type === 'schedule_subject' || subjectData.session_category_id) {
          subjectData = {
            ...subjectData,
            // Ensure we have the correct ID for database insertion
            id: subjectData.id,
            name: subjectData.name,
            color: subjectData.color,
            type: 'schedule_subject',
            source: 'sidebar'
          };
        }
      } catch (e) {
        console.error('❌ Failed to parse subject data:', e);
      }
    }
    // Priority 3: Basic event data (LAST RESORT)
    else if (info.event) {
      subjectData = {
        id: info.event.id,
        name: info.event.title,
        color: info.event.backgroundColor
      };
      console.log('📋 Using basic event data:', subjectData);
    }

    if (!subjectData?.id) {
      console.error('❌ No valid subject data found');
      toast({
        title: "❌ Gagal",
        description: "Data mata pelajaran tidak valid",
        variant: "destructive",
      });
      return;
    }

    console.log('✅ Subject data extracted:', {
      id: subjectData.id,
      name: subjectData.name,
      type: subjectData.type || 'unknown',
      source: subjectData.source || 'unknown'
    });
    
    const selectedDate = new Date(info.dateStr || info.date);
    const dayOfWeek = selectedDate.getDay();
    const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
    
    // ✅ 5-MINUTE SNAPPING: Apply snapping to external drop
    const dropTime = info.date || new Date(info.dateStr);
    const rawDropTime = dropTime.toTimeString().slice(0, 5);
    const defaultDuration = 45; // 45 minutes default duration

    // Apply 5-minute snapping to external drag operation
    const snappedTimes = snapDragToFiveMinutes(defaultDuration, rawDropTime);
    const startTime = snappedTimes.startTime;
    const endTime = snappedTimes.endTime;

    logTimeOperation('External Drag & Drop - 5-Minute Snapping', {
      rawDropTime,
      snappedStartTime: startTime,
      snappedEndTime: endTime,
      defaultDuration,
      subject: subjectData.name,
      type: subjectData.type,
      source: subjectData.source,
      snapDelta: `${rawDropTime} → ${startTime}`
    });
    
    try {
      // ✅ ENHANCED: Determine the correct subject_id based on data type and source
      let actualSubjectId = subjectData.id;
      let actualSubjectName = subjectData.name;
      let actualSubjectColor = subjectData.color || '#6B7280';

      console.log('🔍 Analyzing subject data for correct ID and styling:', {
        id: subjectData.id,
        name: actualSubjectName,
        color: actualSubjectColor,
        type: subjectData.type,
        source: subjectData.source,
        hasSessionCategoryId: !!subjectData.session_category_id
      });

      // ✅ ENHANCED: Handle different subject types correctly
      if (subjectData.type === 'schedule_subject' || subjectData.session_category_id) {
        // This is from the new schedule_subjects system
        actualSubjectId = subjectData.id;
        console.log('✅ Using schedule_subject ID:', actualSubjectId);
      } else if (subjectData.type === 'subject' || (!subjectData.type && !subjectData.session_category_id)) {
        // This is from the legacy subjects system
        actualSubjectId = subjectData.id;
        console.log('✅ Using legacy subject ID:', actualSubjectId);
      }

      // Subject ID is already validated in sidebar, use directly

      // Handle different subject types properly
      if (subjectData.type === 'mata_pelajaran' || subjectData.type === 'schedule_subject') {
        // For schedule_subjects, use the ID directly since it's already validated in sidebar
        actualSubjectId = subjectData.id;
        console.log('✅ Using schedule_subject ID:', actualSubjectId);
      } else if (subjectData.type === 'ekstrakurikuler') {
        // For ekstrakurikuler, use the ID directly (should be from extracurriculars table)
        actualSubjectId = subjectData.id;
        console.log('✅ Using ekstrakurikuler ID directly:', actualSubjectId);
      } else {
        // For regular subjects or unknown types, try to use the ID directly
        actualSubjectId = subjectData.id;
        console.log('✅ Using direct subject ID:', actualSubjectId);
      }

      // ✅ PERBAIKAN: Pastikan menggunakan subject_id yang benar untuk schedule_subjects
      let finalSubjectId = actualSubjectId;

      // Jika ini adalah schedule_subject, gunakan ID asli dari schedule_subjects
      if (subjectData.type === 'schedule_subject' || subjectData.source === 'schedule_subjects') {
        finalSubjectId = subjectData.id; // Gunakan ID asli dari schedule_subjects
        console.log('✅ Using original schedule_subject ID for schedule:', finalSubjectId);
      }

      // ✅ 5-MINUTE SNAPPING: Check for conflicts before creating schedule
      const scheduleData = {
        subject_id: finalSubjectId,
        class_id: selectedClassId,
        day_of_week: adjustedDayOfWeek,
        start_time: startTime,
        end_time: endTime,
        academic_week: selectedWeek,
        schedule_date: selectedDate.toISOString().split('T')[0],
        teacher_id: null,
        room: '',
        notes: ''
      };

      // Check for time conflicts
      if (hasTimeConflict(schedules || [], scheduleData)) {
        toast({
          title: "⚠️ Konflik Jadwal",
          description: `Jadwal bertabrakan dengan jadwal lain pada ${startTime}-${endTime}`,
          variant: "destructive",
        });
        return;
      }

      console.log('🚀 Creating schedule with enhanced data:', scheduleData);

      try {
        await createScheduleMutation.mutateAsync(scheduleData);

        toast({
          title: "✅ Berhasil",
          description: `Jadwal ${actualSubjectName} berhasil ditambahkan`,
        });

        console.log('✅ Schedule created successfully via drag and drop');

        // ✅ OPTIMIZED: Let React Query handle cache invalidation automatically
        // The createSchedule mutation already invalidates the cache
      } catch (error) {
        console.error('❌ Error creating schedule via drag and drop:', error);
        toast({
          title: "❌ Gagal",
          description: `Gagal menambahkan jadwal: ${error.message}`,
          variant: "destructive",
        });
        return;
      }

      // Remove the dragged element from sidebar (optional)
      if (info.draggedEl && info.draggedEl.parentNode) {
        info.draggedEl.style.opacity = '0.5';
        setTimeout(() => {
          if (info.draggedEl.parentNode) {
            info.draggedEl.style.opacity = '1';
          }
        }, 1000);
      }
    } catch (error) {
      console.error('Error creating schedule from drop:', error);
      toast({
        title: "❌ Gagal",
        description: `Gagal menambahkan jadwal: ${error.message || 'Unknown error'}`,
        variant: "destructive",
      });
    }
  };

  return (
    <div
      className="bg-card/40 backdrop-blur-sm border border-border rounded-2xl overflow-hidden mx-auto"
      style={{
        maxWidth: containerWidth,
        transition: 'max-width 0.3s ease-in-out'
      }}
    >
      <div className="relative z-10 flex flex-col" style={{ minHeight: `${calendarHeight}px` }}>
        <ScheduleHeaderControls
          selectedClassId={selectedClassId}
          onClassChange={setSelectedClassId}
          selectedWeek={selectedWeek}
          onWeekSelect={handleWeekSelect}
          onCurrentWeekClick={handleCurrentWeekClick}
        />

        <WeeklyScrollableNavigation
          selectedWeek={selectedWeek}
          onWeekSelect={handleWeekSelect}
        />

        {/* 🚀 LOADING STATE: Show loading indicator while fetching paginated data */}
        {schedulesLoading && (
          <div className="flex items-center justify-center py-8 bg-muted/50 rounded-lg mx-4 mb-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span className="text-muted-foreground text-sm">Memuat data jadwal dengan paginasi...</span>
            </div>
          </div>
        )}

        {/* 🚨 ERROR STATE: Show error if data fetching fails */}
        {schedulesError && (
          <div className="flex items-center justify-center py-8 bg-red-900/20 border border-red-500/30 rounded-lg mx-4 mb-4">
            <div className="text-center">
              <div className="text-red-400 text-sm mb-2">❌ Gagal memuat data jadwal</div>
              <div className="text-muted-foreground text-xs">{schedulesError.message}</div>
            </div>
          </div>
        )}

        <div className="flex-1">
          <ResizablePanelGroup direction="horizontal" className="h-full">
            <ResizablePanel
              defaultSize={isCompact ? 35 : 22}
              minSize={isCompact ? 30 : 18}
              maxSize={isCompact ? 50 : 35}
              className="my-0 transition-all duration-300"
            >
              <ExpandableCategorySidebar
                selectedClassId={selectedClassId}
                selectedWeek={selectedWeek}
                onSubjectDrag={() => {}}
              />
            </ResizablePanel>

            <ResizableHandle withHandle className="bg-border hover:bg-accent transition-colors duration-300 w-1 my-0" />

            <ResizablePanel
              defaultSize={isCompact ? 65 : 78}
              className="transition-all duration-300"
            >
              <div className="h-full flex flex-col px-2 py-2 relative">
                {/* 🚨 PERINGATAN: Tampilkan pesan hanya di area kalender grid */}
                {!selectedClassId && (
                  <div className="absolute inset-10 z-50 p-32 flex items-top justify-center -mr-7 mx-0 ml-3 bg-gray-900/80 backdrop-blur-sm rounded-lg">
                    <div className="h-40 bg-white dark:bg-gray-800 rounded-lg p-4 max-w-sm mx-4 text-center shadow-2xl border border-gray-200 dark:border-gray-700">
                      <div className="mb-3">
                        <div className="w-12 h-12 mx-auto bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
                          <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                        </div>
                      </div>
                      <h3 className="text-base font-semibold text-gray-900 dark:text-white mb-2">
                        Pilih Kelas Terlebih Dahulu
                      </h3>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        💡 Gunakan dropdown "Filter Kelas"
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex-1 calendar-container" style={{
                  minHeight: `${calendarHeight - 140}px`,
                  height: 'auto',
                  opacity: selectedClassId ? 1 : 0.3
                }}>
                  <FullCalendar
                    ref={calendarRef}
                    plugins={[timeGridPlugin, dayGridPlugin, interactionPlugin]}
                    initialView="timeGridWeek"
                    initialDate={currentCalendarDate}
                    headerToolbar={false}
                    events={calendarEvents}
                    editable={true}
                    eventStartEditable={true}
                    eventDurationEditable={true}
                    droppable={true}
                    selectable={true}
                    selectMirror={true}
                    dayMaxEvents={true}
                    weekends={true}
                    height="auto"
                    locale={idLocale}
                    slotMinTime={timeRange.slotMinTime}
                    slotMaxTime={timeRange.slotMaxTime}
                    slotDuration="00:30:00"
                    slotLabelInterval="01:00:00"
                    snapDuration="00:01:00"
                    allDaySlot={false}
                    eventMinHeight={15}
                    eventShortHeight={30}
                    slotEventOverlap={false}
                    expandRows={true}
                    stickyHeaderDates={false}
                    viewDidMount={(info) => {
                      // WORLD-CLASS DEBUG: Comprehensive calendar analysis
                      console.log('🚀 ELITE Calendar Mount Analysis:', {
                        view: info.view.type,
                        timeRange: timeRange,
                        totalSlots: totalHours,
                        expectedEndTime: `${endHour}:00`,
                        calendarDimensions: {
                          width: info.el.offsetWidth,
                          height: info.el.offsetHeight,
                          scrollHeight: info.el.scrollHeight
                        }
                      });

                      // CRITICAL FIX: Force FullCalendar to recalculate with correct slot height
                      const slots = info.el.querySelectorAll('.fc-timegrid-slot');
                      slots.forEach((slot: any) => {
                        slot.style.height = '30px';
                        slot.style.minHeight = '30px';
                        slot.style.maxHeight = '30px';
                      });

                      // PRECISION: Fix any existing events
                      const events = info.el.querySelectorAll('.fc-timegrid-event');
                      events.forEach((event: any) => {
                        event.style.margin = '0';
                        event.style.boxSizing = 'border-box';
                      });

                      // MASTER-LEVEL: Verify grid completeness
                      const timegridSlots = info.el.querySelectorAll('.fc-timegrid-slot');
                      const lastSlot = timegridSlots[timegridSlots.length - 1] as HTMLElement;

                      console.log('🎯 Grid Completeness Check:', {
                        totalSlotsRendered: timegridSlots.length,
                        expectedSlots: totalHours,
                        lastSlotVisible: lastSlot ? lastSlot.offsetHeight > 0 : false,
                        gridComplete: timegridSlots.length >= totalHours
                      });

                      // LEGENDARY: Performance metrics
                      const scroller = info.el.querySelector('.fc-scroller') as HTMLElement;
                      if (scroller) {
                        console.log('⚡ Performance Metrics:', {
                          scrollerOverflow: getComputedStyle(scroller).overflow,
                          scrollerHeight: scroller.offsetHeight,
                          contentHeight: scroller.scrollHeight,
                          isContentClipped: scroller.scrollHeight > scroller.offsetHeight
                        });
                      }
                    }}
                    eventClick={handleEventClick}
                    select={handleDateSelect}
                    eventDrop={handleEventDrop}
                    eventResize={handleEventResize}
                    eventResizeStart={handleEventResizeStart}
                    eventResizeStop={handleEventResizeStop}
                    dayCellDidMount={handleDayCellDidMount}

                    eventTimeFormat={{
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: false
                    }}
                    slotLabelFormat={{
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: false
                    }}
                    dayHeaderFormat={{
                      weekday: 'short',
                      month: 'numeric',
                      day: 'numeric'
                    }}
                    businessHours={{
                      daysOfWeek: [1, 2, 3, 4, 5, 6, 0], // Include Sunday (0)
                      startTime: timeRange.slotMinTime.slice(0, 5),
                      endTime: timeRange.slotMaxTime.slice(0, 5)
                    }}
                    selectConstraint="businessHours"
                    eventConstraint="businessHours"
                    dragScroll={true}
                    eventOverlap={false}
                    selectOverlap={false}

                    drop={handleDrop}
                  />
                </div>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>



        {/* Weekly Activity List */}
        <div className="px-4 sm:px-6">
          <WeeklyActivityList
            selectedWeek={selectedWeek}
            selectedClassId={selectedClassId}
            selectedDate={getCurrentWeekDate()}
          />
        </div>
      </div>

      <AddScheduleModal
        isOpen={isAddModalOpen}
        onClose={closeModal}
        selectedTimeSlot={selectedTimeSlot}
        selectedDate={getCurrentWeekDate()}
        selectedClassId={selectedClassId}
        onScheduleCreated={() => {
          console.log('✅ Schedule created');
          closeModal();
        }}
      />
      
      <EditScheduleModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        schedule={editingSchedule}
        onScheduleUpdated={() => {
          console.log('✅ Schedule updated');
        }}
      />
    </div>
  );
};
