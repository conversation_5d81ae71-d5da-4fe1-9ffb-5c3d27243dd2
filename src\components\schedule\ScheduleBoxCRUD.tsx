import React, { useState } from 'react';
import { Edit, Trash2, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useUpdateSchedule, useDeleteSchedule } from '@/hooks/useSchedules';
import { useToast } from '@/hooks/use-toast';
import { useScheduleRealTime } from '@/hooks/useRealTimeSync';

interface ScheduleBoxCRUDProps {
  schedule: any;
  onUpdate?: () => void;
  onDelete?: () => void;
}

export const ScheduleBoxCRUD: React.FC<ScheduleBoxCRUDProps> = ({
  schedule,
  onUpdate,
  onDelete
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    start_time: schedule.start_time || '',
    end_time: schedule.end_time || '',
    room: schedule.room || '',
    notes: schedule.notes || ''
  });

  const updateScheduleMutation = useUpdateSchedule();
  const deleteScheduleMutation = useDeleteSchedule();
  const { toast } = useToast();
  const { forceRefresh } = useScheduleRealTime();

  const handleEdit = () => {
    setIsEditing(true);
    setEditData({
      start_time: schedule.start_time || '',
      end_time: schedule.end_time || '',
      room: schedule.room || '',
      notes: schedule.notes || ''
    });
  };

  const handleSave = async () => {
    try {
      await updateScheduleMutation.mutateAsync({
        id: schedule.id,
        ...editData
      });

      toast({
        title: "✅ Berhasil",
        description: "Jadwal berhasil diperbarui",
      });

      setIsEditing(false);
      onUpdate?.();
    } catch (error) {
      console.error('Error updating schedule:', error);
      toast({
        title: "❌ Gagal",
        description: "Gagal memperbarui jadwal",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!confirm('Apakah Anda yakin ingin menghapus jadwal ini?')) {
      return;
    }

    try {
      await deleteScheduleMutation.mutateAsync(schedule.id);

      toast({
        title: "✅ Berhasil",
        description: "Jadwal berhasil dihapus",
      });

      // 🚀 ENHANCED: Force immediate UI refresh
      console.log('🔄 ScheduleBoxCRUD: Force refreshing UI after delete...');
      forceRefresh();

      onDelete?.();
    } catch (error) {
      console.error('Error deleting schedule:', error);
      toast({
        title: "❌ Gagal",
        description: "Gagal menghapus jadwal",
        variant: "destructive",
      });
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditData({
      start_time: schedule.start_time || '',
      end_time: schedule.end_time || '',
      room: schedule.room || '',
      notes: schedule.notes || ''
    });
  };

  if (isEditing) {
    return (
      <div className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-lg p-2 z-10">
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-1">
            <Input
              value={editData.start_time}
              onChange={(e) => setEditData(prev => ({ ...prev, start_time: e.target.value }))}
              placeholder="Mulai"
              className="h-6 text-xs bg-white/20 border-white/30 text-white placeholder:text-white/60"
              type="time"
            />
            <Input
              value={editData.end_time}
              onChange={(e) => setEditData(prev => ({ ...prev, end_time: e.target.value }))}
              placeholder="Selesai"
              className="h-6 text-xs bg-white/20 border-white/30 text-white placeholder:text-white/60"
              type="time"
            />
          </div>
          <Input
            value={editData.room}
            onChange={(e) => setEditData(prev => ({ ...prev, room: e.target.value }))}
            placeholder="Ruangan"
            className="h-6 text-xs bg-white/20 border-white/30 text-white placeholder:text-white/60"
          />
          <Input
            value={editData.notes}
            onChange={(e) => setEditData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Catatan"
            className="h-6 text-xs bg-white/20 border-white/30 text-white placeholder:text-white/60"
          />
          <div className="flex gap-1">
            <Button
              onClick={handleSave}
              disabled={updateScheduleMutation.isPending}
              className="h-6 px-2 text-xs bg-white/20 hover:bg-white/30 text-white border-white/30"
              variant="outline"
            >
              <Save className="w-3 h-3" />
            </Button>
            <Button
              onClick={handleCancel}
              className="h-6 px-2 text-xs bg-white/20 hover:bg-white/30 text-white border-white/30"
              variant="outline"
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1 z-10">
      <Button
        onClick={handleEdit}
        className="h-6 w-6 p-0 bg-white/20 hover:bg-white/30 text-white border-white/30"
        variant="outline"
        size="sm"
      >
        <Edit className="w-3 h-3" />
      </Button>
      <Button
        onClick={handleDelete}
        disabled={deleteScheduleMutation.isPending}
        className="h-6 w-6 p-0 bg-white/20 hover:bg-red-500/50 text-white border-white/30"
        variant="outline"
        size="sm"
      >
        <Trash2 className="w-3 h-3" />
      </Button>
    </div>
  );
};
