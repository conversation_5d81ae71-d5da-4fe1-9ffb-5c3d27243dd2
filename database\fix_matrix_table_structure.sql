-- =====================================================
-- FIX MATRIX TABLE STRUCTURE
-- =====================================================
-- This script fixes the database structure for the matrix table
-- by properly separating subjects from class-subject relationships

-- 1. Clean up the schedule_subjects table structure
-- Remove class-specific columns that shouldn't be in the subjects table
ALTER TABLE schedule_subjects 
DROP COLUMN IF EXISTS subject_id,
DROP COLUMN IF EXISTS class_id,
DROP COLUMN IF EXISTS hours_per_week;

-- 2. Create a proper schedule_class_subjects table if it doesn't exist
CREATE TABLE IF NOT EXISTS schedule_class_subjects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  schedule_subject_id UUID NOT NULL REFERENCES schedule_subjects(id) ON DELETE CASCADE,
  class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
  hours_per_week INTEGER DEFAULT 0,
  hours_per_year INTEGER DEFAULT 0,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraint: one subject per class
  UNIQUE(schedule_subject_id, class_id)
);

-- 3. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_schedule_class_subjects_schedule_subject 
ON schedule_class_subjects(schedule_subject_id);

CREATE INDEX IF NOT EXISTS idx_schedule_class_subjects_class 
ON schedule_class_subjects(class_id);

-- 4. Enable RLS on schedule_class_subjects
ALTER TABLE schedule_class_subjects ENABLE ROW LEVEL SECURITY;

-- 5. Create RLS policies for schedule_class_subjects
DROP POLICY IF EXISTS "Users can view schedule_class_subjects from their school" ON schedule_class_subjects;
CREATE POLICY "Users can view schedule_class_subjects from their school" ON schedule_class_subjects
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM classes c 
      WHERE c.id = schedule_class_subjects.class_id 
      AND c.school_id = get_user_school_id(auth.uid())
    )
  );

DROP POLICY IF EXISTS "Admin can manage schedule_class_subjects" ON schedule_class_subjects;
CREATE POLICY "Admin can manage schedule_class_subjects" ON schedule_class_subjects
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM classes c 
      WHERE c.id = schedule_class_subjects.class_id 
      AND c.school_id = get_user_school_id(auth.uid()) 
      AND (
        has_role(auth.uid(), 'superadmin'::user_role) OR 
        has_role(auth.uid(), 'kepsek'::user_role) OR 
        has_role(auth.uid(), 'kesiswaan'::user_role)
      )
    )
  );

-- 6. Create trigger for updated_at on schedule_class_subjects
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_schedule_class_subjects_updated_at ON schedule_class_subjects;
CREATE TRIGGER update_schedule_class_subjects_updated_at 
  BEFORE UPDATE ON schedule_class_subjects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Clean up duplicate subjects in schedule_subjects
-- Keep only the subjects without class_id (the master subjects)
DELETE FROM schedule_subjects 
WHERE id IN (
  SELECT s1.id 
  FROM schedule_subjects s1
  INNER JOIN schedule_subjects s2 ON s1.name = s2.name 
    AND s1.session_category_id = s2.session_category_id 
    AND s1.school_id = s2.school_id 
    AND s1.academic_year_id = s2.academic_year_id
  WHERE s1.id != s2.id 
    AND s1.created_at > s2.created_at
);

-- 8. Add comments for documentation
COMMENT ON TABLE schedule_class_subjects IS 'Tabel relasi antara mata pelajaran jadwal dan kelas';
COMMENT ON COLUMN schedule_class_subjects.schedule_subject_id IS 'Foreign key ke schedule_subjects';
COMMENT ON COLUMN schedule_class_subjects.class_id IS 'Foreign key ke classes';
COMMENT ON COLUMN schedule_class_subjects.hours_per_week IS 'Jam pelajaran per minggu untuk kelas ini';
COMMENT ON COLUMN schedule_class_subjects.hours_per_year IS 'Jam pelajaran per tahun untuk kelas ini';

-- 9. Create a view for easy matrix data retrieval
CREATE OR REPLACE VIEW matrix_subjects_view AS
SELECT 
  s.id,
  s.name,
  s.code,
  s.color,
  s.total_hours_per_year,
  s.standard_duration,
  s.session_category_id,
  s.school_id,
  s.academic_year_id,
  s.created_at,
  s.updated_at,
  sc.name as category_name,
  sc.color as category_color
FROM schedule_subjects s
LEFT JOIN session_categories sc ON s.session_category_id = sc.id
ORDER BY s.created_at DESC;

-- 10. Create a view for matrix class subjects
CREATE OR REPLACE VIEW matrix_class_subjects_view AS
SELECT 
  scs.id,
  scs.schedule_subject_id as subject_id,
  scs.class_id,
  scs.hours_per_week,
  scs.hours_per_year,
  scs.created_at,
  scs.updated_at,
  s.name as subject_name,
  s.code as subject_code,
  s.color as subject_color,
  c.name as class_name,
  c.level as class_level
FROM schedule_class_subjects scs
INNER JOIN schedule_subjects s ON scs.schedule_subject_id = s.id
INNER JOIN classes c ON scs.class_id = c.id
ORDER BY scs.created_at DESC;

COMMENT ON VIEW matrix_subjects_view IS 'View untuk menampilkan mata pelajaran dengan kategori untuk matrix table';
COMMENT ON VIEW matrix_class_subjects_view IS 'View untuk menampilkan relasi mata pelajaran-kelas untuk matrix table';

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Matrix table structure has been fixed successfully!';
  RAISE NOTICE 'schedule_subjects table now only contains subject definitions';
  RAISE NOTICE 'schedule_class_subjects table handles class-subject relationships';
  RAISE NOTICE 'Views created for easy data retrieval';
END $$;
