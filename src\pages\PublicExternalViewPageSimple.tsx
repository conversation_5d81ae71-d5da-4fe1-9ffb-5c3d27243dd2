import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Calendar, Users, Clock, MapPin } from 'lucide-react';
import { useSchedules } from '@/hooks/useSchedules';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useTimeSessions } from '@/hooks/useTimeSessions';
import { useClasses } from '@/hooks/useClasses';
import { useSchools } from '@/hooks/useSchools';
import PublicScheduleCalendar from '@/components/schedule/PublicScheduleCalendar';

interface TokenData {
  classId: string;
  schoolId: string;
}

const PublicExternalViewPageSimple: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const [tokenData, setTokenData] = useState<TokenData | null>(null);
  const [selectedWeek, setSelectedWeek] = useState<number>(2); // Start with week 2 for testing
  const [error, setError] = useState<string | null>(null);

  // ✅ DECODE TOKEN
  useEffect(() => {
    if (!token) {
      setError('Token tidak valid');
      return;
    }

    try {
      const decoded = atob(token);
      const [classId, schoolId] = decoded.split(':');
      
      if (!classId || !schoolId) {
        setError('Token format tidak valid');
        return;
      }

      setTokenData({ classId, schoolId });
      console.log('✅ Token decoded successfully:', { classId, schoolId });
    } catch (decodeError) {
      console.error('❌ Token decode error:', decodeError);
      setError('Token tidak valid atau rusak');
    }
  }, [token]);

  // ✅ FETCH DATA DIRECTLY WITHOUT AUTHENTICATION
  const [schedules, setSchedules] = useState<any[]>([]);
  const [academicWeeks, setAcademicWeeks] = useState<any[]>([]);
  const [timeSessions, setTimeSessions] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [school, setSchool] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // ✅ FETCH ALL DATA DIRECTLY
  useEffect(() => {
    const fetchAllData = async () => {
      if (!tokenData) return;

      try {
        setIsLoading(true);
        console.log('🔄 Fetching all data directly for public view...');

        // Fetch schedules using schedules_view
        const { data: schedulesData, error: schedulesError } = await supabase
          .from('schedules_view')
          .select('*')
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true });

        if (schedulesError) {
          console.error('❌ Schedules error:', schedulesError);
        } else {
          setSchedules(schedulesData || []);
          console.log('✅ Schedules loaded:', schedulesData?.length);
        }

        // Fetch school data
        const { data: schoolData, error: schoolError } = await supabase
          .from('schools')
          .select('*')
          .eq('id', tokenData.schoolId)
          .single();

        if (!schoolError) {
          setSchool(schoolData);
          console.log('✅ School loaded:', schoolData.name);
        }

        // Fetch classes
        const { data: classesData, error: classesError } = await supabase
          .from('classes')
          .select('*')
          .eq('school_id', tokenData.schoolId);

        if (!classesError) {
          setClasses(classesData || []);
          console.log('✅ Classes loaded:', classesData?.length);
        }

        // Fetch time sessions
        const { data: timeData, error: timeError } = await supabase
          .from('time_sessions')
          .select('*')
          .eq('school_id', tokenData.schoolId)
          .order('start_time', { ascending: true });

        if (!timeError) {
          setTimeSessions(timeData || []);
          console.log('✅ Time sessions loaded:', timeData?.length);
        }

        // Generate academic weeks (fallback) - FIXED for proper week calculation
        const weeks = Array.from({ length: 52 }, (_, i) => {
          const weekNumber = i + 1;

          // ✅ FIXED: Use exact same base date as database expects
          // Week 1 should start from Monday, Jan 6, 2025
          const baseDate = new Date(2025, 0, 6); // Jan 6, 2025 (Monday)
          const startDate = new Date(baseDate);
          startDate.setDate(baseDate.getDate() + (i * 7));

          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6); // Sunday

          console.log(`Week ${weekNumber}: ${startDate.toDateString()} - ${endDate.toDateString()}`);

          return {
            weekNumber,
            startDate,
            endDate,
            label: `Minggu ${weekNumber}`,
            dateRange: `${startDate.getDate()}/${startDate.getMonth() + 1} - ${endDate.getDate()}/${endDate.getMonth() + 1}`,
            isCurrentWeek: false
          };
        });

        setAcademicWeeks(weeks);
        console.log('✅ Academic weeks generated:', weeks.length);

      } catch (error) {
        console.error('❌ Error fetching data:', error);
        setError('Gagal memuat data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllData();
  }, [tokenData]);

  // ✅ FIND CLASS DATA
  const selectedClass = classes.find(c => c.id === tokenData?.classId);

  // ✅ LOADING STATE
  if (!tokenData || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Memuat jadwal...</p>
        </div>
      </div>
    );
  }

  // ✅ ERROR STATE
  if (error || !selectedClass) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                {error || 'Kelas tidak ditemukan'}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  // ✅ DETAILED DEBUG: Check specific week data
  const weekData = schedules.filter(s => s.academic_week === selectedWeek && s.class_id === tokenData.classId);
  const week1Data = schedules.filter(s => s.academic_week === 1 && s.class_id === tokenData.classId);
  const week2Data = schedules.filter(s => s.academic_week === 2 && s.class_id === tokenData.classId);

  console.log('🔥 PUBLIC PAGE DETAILED DEBUG:', {
    totalSchedules: schedules.length,
    academicWeeksCount: academicWeeks.length,
    selectedClass: selectedClass.name,
    selectedWeek,
    selectedClassId: tokenData.classId,

    // Week-specific data
    currentWeekData: {
      count: weekData.length,
      data: weekData.map(s => ({
        day: s.day_of_week,
        subject: s.subject_name,
        time: `${s.start_time}-${s.end_time}`
      }))
    },

    week1Data: {
      count: week1Data.length,
      days: week1Data.map(s => s.day_of_week).sort(),
      subjects: week1Data.map(s => s.subject_name)
    },

    week2Data: {
      count: week2Data.length,
      days: week2Data.map(s => s.day_of_week).sort(),
      subjects: week2Data.map(s => s.subject_name)
    },

    // Sample data
    sampleSchedules: schedules.slice(0, 3).map(s => ({
      week: s.academic_week,
      day: s.day_of_week,
      subject: s.subject_name,
      time: `${s.start_time}-${s.end_time}`,
      class: s.class_name
    }))
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* ✅ HEADER: School and Class Info */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {school?.logo_url && (
                <img 
                  src={school.logo_url} 
                  alt="Logo Sekolah" 
                  className="h-12 w-12 object-contain"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {school?.name || 'SMA IT HSI'}
                </h1>
                <p className="text-gray-600">
                  Jadwal Kelas {selectedClass.name}
                </p>
              </div>
            </div>
            
            <div className="text-right text-sm text-gray-500">
              <p className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Tahun Akademik 2025/2026
              </p>
              <p className="flex items-center mt-1">
                <Users className="h-4 w-4 mr-1" />
                Kelas {selectedClass.name}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* ✅ MAIN CONTENT: Calendar */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <Card>
          <CardContent className="p-6">
            <PublicScheduleCalendar
              schedules={schedules}
              academicWeeks={academicWeeks}
              timeSessions={timeSessions}
              selectedWeek={selectedWeek}
              onWeekChange={setSelectedWeek}
              selectedClassId={tokenData.classId}
              className="w-full"
            />
          </CardContent>
        </Card>
      </div>

      {/* ✅ FOOTER: School Info */}
      <div className="bg-white border-t border-gray-200 mt-8">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="text-center text-sm text-gray-500">
            <p className="flex items-center justify-center">
              <MapPin className="h-4 w-4 mr-1" />
              {school?.address || 'Purworejo, Jawa Tengah'}
            </p>
            {(school?.phone || school?.email) && (
              <p className="mt-1">
                {school?.phone && (
                  <span className="mr-4">📞 {school.phone}</span>
                )}
                {school?.email && (
                  <span>📧 {school.email}</span>
                )}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* ✅ DEBUG INFO (remove in production) */}
      <div className="bg-gray-100 p-4 text-xs text-gray-600">
        <p><strong>Debug Info:</strong></p>
        <p>Schedules: {schedules.length} | Academic Weeks: {academicWeeks.length} | Selected Week: {selectedWeek}</p>
        <p>Class: {selectedClass.name} ({tokenData.classId})</p>
        <p>Sample Schedule: {schedules[0] ? `${schedules[0].subject_name} - Week ${schedules[0].academic_week}` : 'None'}</p>
      </div>
    </div>
  );
};

export default PublicExternalViewPageSimple;
