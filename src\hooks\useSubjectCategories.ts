
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface SubjectCategory {
  id: string;
  name: string;
  school_id: string;
  academic_year_id: string;
  created_at: string;
  updated_at: string;
}

export const useSubjectCategories = () => {
  return useQuery({
    queryKey: ['curriculum-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('curriculum_categories')
        .select('*')
        .order('name', { ascending: true });

      if (error) throw error;
      return data || [];
    },
  });
};

export const useCreateSubjectCategory = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (categoryData: { name: string }) => {
      // Get current user's school_id and active academic year
      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      const { data: activeYear } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile?.school_id)
        .eq('is_active', true)
        .single();

      const { data, error } = await supabase
        .from('curriculum_categories')
        .insert({
          ...categoryData,
          school_id: profile?.school_id,
          academic_year_id: activeYear?.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['curriculum-categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori mata pelajaran berhasil ditambahkan",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menambahkan kategori mata pelajaran: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateSubjectCategory = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...categoryData }: { id: string; name: string }) => {
      const { data, error } = await supabase
        .from('curriculum_categories')
        .update(categoryData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['curriculum-categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori mata pelajaran berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal memperbarui kategori mata pelajaran: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteSubjectCategory = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('curriculum_categories')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['curriculum-categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori mata pelajaran berhasil dihapus",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menghapus kategori mata pelajaran: " + error.message,
        variant: "destructive",
      });
    },
  });
};
