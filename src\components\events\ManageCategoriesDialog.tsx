
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Edit2, Trash2, Palette } from 'lucide-react';
import { useEventCategories } from '@/hooks/useEventCategories';
import { EventCategory, DEFAULT_CATEGORIES } from '@/types/event';

interface ManageCategoriesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ManageCategoriesDialog: React.FC<ManageCategoriesDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryColor, setNewCategoryColor] = useState('#3b82f6');
  const [editingCategory, setEditingCategory] = useState<EventCategory | null>(null);
  const [editName, setEditName] = useState('');
  const [editColor, setEditColor] = useState('');

  const { customCategories, createCategory, updateCategory, deleteCategory, isCreating, isUpdating, isDeleting } = useEventCategories();

  const handleCreateCategory = () => {
    if (!newCategoryName.trim()) return;

    createCategory({
      name: newCategoryName.trim(),
      color: newCategoryColor,
    });

    setNewCategoryName('');
    setNewCategoryColor('#3b82f6');
  };

  const handleEditCategory = (category: EventCategory) => {
    setEditingCategory(category);
    setEditName(category.name);
    setEditColor(category.color);
  };

  const handleUpdateCategory = () => {
    if (!editingCategory || !editName.trim()) return;

    updateCategory({
      id: editingCategory.id,
      name: editName.trim(),
      color: editColor,
    });

    setEditingCategory(null);
    setEditName('');
    setEditColor('');
  };

  const handleDeleteCategory = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus kategori ini?')) {
      deleteCategory(id);
    }
  };

  const cancelEdit = () => {
    setEditingCategory(null);
    setEditName('');
    setEditColor('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Kelola Kategori Event</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add New Category */}
          <div className="p-4 bg-muted/50 rounded-lg border">
            <h3 className="font-medium mb-3">Tambah Kategori Baru</h3>
            <div className="space-y-3">
              <div>
                <Label htmlFor="new-name">Nama Kategori</Label>
                <Input
                  id="new-name"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  placeholder="Masukkan nama kategori"
                  
                />
              </div>
              <div>
                <Label htmlFor="new-color">Warna</Label>
                <div className="flex gap-2">
                  <input
                    id="new-color"
                    type="color"
                    value={newCategoryColor}
                    onChange={(e) => setNewCategoryColor(e.target.value)}
                    className="w-12 h-10 border rounded cursor-pointer"
                  />
                  <Input
                    value={newCategoryColor}
                    onChange={(e) => setNewCategoryColor(e.target.value)}
                    placeholder="#3b82f6"
                    
                  />
                </div>
              </div>
              <Button
                onClick={handleCreateCategory}
                disabled={!newCategoryName.trim() || isCreating}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                {isCreating ? 'Menambahkan...' : 'Tambah Kategori'}
              </Button>
            </div>
          </div>

          {/* Default Categories */}
          <div>
            <h3 className="font-medium mb-3">Kategori Default</h3>
            <div className="space-y-2">
              {DEFAULT_CATEGORIES.map(category => (
                <div
                  key={category.id}
                  className="flex items-center justify-between p-3 bg-muted/30 rounded border border-muted/30"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="text-muted-foreground">{category.name}</span>
                  </div>
                  <span className="text-muted-foreground text-sm">Default</span>
                </div>
              ))}
            </div>
          </div>

          {/* Custom Categories */}
          {customCategories.length > 0 && (
            <div>
              <h3 className="font-medium mb-3">Kategori Kustom</h3>
              <div className="space-y-2">
                {customCategories.map(category => (
                  <div
                    key={category.id}
                    className="flex items-center justify-between p-3 bg-muted/50 rounded border"
                  >
                    {editingCategory?.id === category.id ? (
                      <div className="flex-1 flex items-center gap-2">
                        <input
                          type="color"
                          value={editColor}
                          onChange={(e) => setEditColor(e.target.value)}
                          className="w-8 h-8 border rounded cursor-pointer"
                        />
                        <Input
                          value={editName}
                          onChange={(e) => setEditName(e.target.value)}
                          className="flex-1"
                        />
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            onClick={handleUpdateCategory}
                            disabled={!editName.trim() || isUpdating}
                          >
                            Simpan
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={cancelEdit}
                          >
                            Batal
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center gap-3">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: category.color }}
                          />
                          <span className="text-white">{category.name}</span>
                        </div>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditCategory(category)}
                          >
                            <Edit2 className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeleteCategory(category.id)}
                            disabled={isDeleting}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Tutup
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
