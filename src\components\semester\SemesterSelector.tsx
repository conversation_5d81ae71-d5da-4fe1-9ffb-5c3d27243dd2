import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock } from 'lucide-react';
import { useSemesters, useActiveSemester, useUpdateActiveSemester } from '@/hooks/useSemesters';
import { getSemesterDescription } from '@/types/semester';

interface SemesterSelectorProps {
  className?: string;
  showDescription?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const SemesterSelector: React.FC<SemesterSelectorProps> = ({
  className = '',
  showDescription = true,
  size = 'md'
}) => {
  const { data: semesters, isLoading } = useSemesters();
  const { data: activeSemester } = useActiveSemester();
  const updateActiveSemester = useUpdateActiveSemester();

  const handleSemesterChange = (semesterId: string) => {
    updateActiveSemester.mutate(semesterId);
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-8 text-sm';
      case 'lg':
        return 'h-14 text-lg';
      default:
        return 'h-12';
    }
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse bg-muted/50 rounded-xl ${getSizeClasses()} ${className}`}>
        <div className="h-full bg-muted rounded-xl"></div>
      </div>
    );
  }

  if (!semesters || semesters.length === 0) {
    return (
      <div className={`bg-card border border-border rounded-xl ${getSizeClasses()} ${className} flex items-center justify-center`}>
        <span className="text-muted-foreground text-sm">Tidak ada semester</span>
      </div>
    );
  }

  return (
    <div className={className}>
      <Select 
        value={activeSemester?.id || ''} 
        onValueChange={handleSemesterChange}
        disabled={updateActiveSemester.isPending}
      >
        <SelectTrigger className={`bg-background border-border text-foreground focus:border-ring focus:ring-ring focus:ring-2 rounded-xl ${getSizeClasses()}`}>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-primary" />
            <SelectValue placeholder="Pilih semester">
              {activeSemester && (
                <div className="flex items-center gap-2">
                  <span className="font-medium">{activeSemester.name}</span>
                  {showDescription && (
                    <Badge variant="secondary" className="bg-primary/10 text-primary text-xs">
                      {getSemesterDescription(activeSemester.semester_number)}
                    </Badge>
                  )}
                </div>
              )}
            </SelectValue>
          </div>
        </SelectTrigger>
        <SelectContent className="bg-popover border-border backdrop-blur-xl">
          {semesters.map((semester) => (
            <SelectItem
              key={semester.id}
              value={semester.id}
              className="text-foreground hover:bg-accent focus:bg-accent"
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{semester.name}</span>
                    {semester.is_active && (
                      <Badge variant="default" className="bg-green-500 text-white text-xs">
                        Aktif
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{getSemesterDescription(semester.semester_number)}</span>
                    <span>•</span>
                    <span>{semester.academic_year?.year_name}</span>
                  </div>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default SemesterSelector;
