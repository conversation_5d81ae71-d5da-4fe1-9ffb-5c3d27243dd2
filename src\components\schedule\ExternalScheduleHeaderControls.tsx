import React from 'react';
import { Button } from '@/components/ui/button';
import { Calendar, ChevronLeft, ChevronRight, School, Share2 } from 'lucide-react';
import { ClassFilter } from './ClassFilter';
import { useSchools } from '@/hooks/useSchools';
import { useActiveAcademicYear } from '@/hooks/useAcademicYears';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';

interface ExternalScheduleHeaderControlsProps {
  selectedClassId: string | null;
  onClassChange: (classId: string | null) => void;
  selectedWeek: number;
  onWeekSelect: (week: number) => void;
  onCurrentWeekClick: () => void;
  onCopyLink: () => void;
}

export const ExternalScheduleHeaderControls: React.FC<ExternalScheduleHeaderControlsProps> = ({
  selectedClassId,
  onClassChange,
  selectedWeek,
  onWeekSelect,
  onCurrentWeekClick,
  onCopyLink
}) => {
  const { data: school } = useSchools();
  const { data: activeAcademicYear } = useActiveAcademicYear();
  const { academicWeeks } = useAcademicWeeks();

  const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);

  const goToPreviousWeek = () => {
    if (selectedWeek > 1) {
      onWeekSelect(selectedWeek - 1);
    }
  };

  const goToNextWeek = () => {
    if (selectedWeek < academicWeeks.length) {
      onWeekSelect(selectedWeek + 1);
    }
  };

  return (
    <div className="p-2 space-y-2 bg-transparent relative">
      <div className="flex items-center justify-between p-2 rounded-lg">
        {/* Left Section: Filter Only */}
        <div className="flex items-center gap-4 flex-1">
          <ClassFilter selectedClassId={selectedClassId} onClassChange={onClassChange} />
        </div>

        {/* Center Section: School Info with Logo */}
        <div className="flex items-center gap-6 flex-1 justify-center">
          <div className="flex items-center justify-center w-14 h-14 rounded-none overflow-hidden">
            {school?.logo_url ? (
              <img
                src={school.logo_url}
                alt="Logo Sekolah"
                className="w-full h-full object-cover"
              />
            ) : (
              <School className="h-6 w-6 text-foreground" />
            )}
          </div>
          <div className="flex flex-col text-center">
            <div className="text-foreground font-semibold text-base leading-tight">
              {school?.name || 'SMA Negeri 1 Jakarta'}
            </div>
            <div className="text-muted-foreground text-sm leading-tight">
              {school?.address || 'Jl. Pendidikan No. 123, Jakarta Pusat'}
            </div>
            <div className="text-muted-foreground text-xs">
              Tahun Akademik {activeAcademicYear?.year_name || '2025/2026'}
            </div>
            <div className="text-primary text-xs mt-1">
              External View - Generate Public Link
            </div>
          </div>
        </div>

        {/* Right Section: Weekly Navigation + Copy Link Button */}
        <div className="flex gap-3 flex-1 justify-end items-center">
          {/* Weekly Navigation */}
          <Button
            variant="outline"
            size="sm"
            onClick={goToPreviousWeek}
            disabled={selectedWeek <= 1}
            className="bg-background border-border text-foreground hover:bg-primary hover:text-primary-foreground flex items-center justify-center"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="text-center">
            <div className="text-bold text-foreground px-2 py-1 text-sm">
              Minggu {selectedWeek}
            </div>
            {currentWeek && (
              <div className="text-xs text-muted-foreground whitespace-nowrap">
                {currentWeek.dateRange}
              </div>
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={goToNextWeek}
            disabled={selectedWeek >= academicWeeks.length}
            className="bg-background border-border text-foreground hover:bg-primary hover:text-primary-foreground flex items-center justify-center"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Minggu Ini Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onCurrentWeekClick}
            className="w-10 h-10 rounded-full bg-background border border-blue-500 text-blue-500 hover:bg-blue-500/10 hover:border-blue-500/70 hover:text-blue-600 transition-all duration-300 transform hover:scale-105"
            title="Minggu Ini"
          >
            <Calendar className="h-4 w-4" />
          </Button>

          {/* Copy Link Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onCopyLink}
            className="w-10 h-10 rounded-full bg-background border border-green-500 text-green-500 hover:bg-green-500/10 hover:border-green-500/70 hover:text-green-600 transition-all duration-300 transform hover:scale-105"
            title="Copy Link"
          >
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
