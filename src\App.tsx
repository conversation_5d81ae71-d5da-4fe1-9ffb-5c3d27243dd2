
import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "next-themes";
import MainLayout from "./components/MainLayout";
import Index from "./pages/Index";
import SubjectsPage from "./pages/SubjectsPage";
import SubjectClassMatrixPage from "./pages/SubjectClassMatrixPage";
import EventsPage from "./pages/EventsPage";
import SchedulesPage from "./pages/SchedulesPage";
import ClassesPage from "./pages/ClassesPage";
import TeachersPage from "./pages/TeachersPage";
import TimeSessionsPage from "./pages/TimeSessionsPage";
import GeneralSettingsPage from "./pages/GeneralSettingsPage";
import SchoolInfoPage from "./pages/SchoolInfoPage";
import PublicSchedulePage from "./pages/PublicSchedulePage";
import PublicExternalViewSimpleCopy from "./pages/PublicExternalViewSimpleCopy";
import PublicExternalViewTest from "./pages/PublicExternalViewTest";
import PublicExternalViewPage from "./pages/PublicExternalViewPage";
import PublicExternalViewOverviewPage from "./pages/PublicExternalViewOverviewPage";
import PublicEventsPage from "./pages/PublicEventsPage";
import NotFound from "./pages/NotFound";

// ✅ ENHANCED: Configure QueryClient for aggressive real-time updates
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // ✅ Force fresh data for real-time updates
      staleTime: 0, // Always consider data stale
      gcTime: 0, // Don't cache data
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchOnReconnect: true,
      retry: 2,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

const App: React.FC = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider
      attribute="class"
      defaultTheme="light"
      enableSystem={false}
      disableTransitionOnChange={false}
    >
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              {/* Routes with Main Layout (Sidebar, Header, etc.) */}
              <Route element={<MainLayout />}>
                <Route path="/" element={<Index />} />
                <Route path="/subjects" element={<SubjectsPage />} />
                <Route path="/subjects-matrix" element={<SubjectClassMatrixPage />} />
                <Route path="/events" element={<EventsPage />} />
                <Route path="/classes" element={<ClassesPage />} />
                <Route path="/teachers" element={<TeachersPage />} />
                <Route path="/time-sessions" element={<TimeSessionsPage />} />
                <Route path="/settings" element={<GeneralSettingsPage />} />
                <Route path="/school-info" element={<SchoolInfoPage />} />
                <Route path="/schedules/*" element={<SchedulesPage />} />
              </Route>

              {/* Public routes without the main layout */}
              <Route path="/public/schedule" element={<PublicSchedulePage />} />
              <Route path="/external/:token" element={<PublicExternalViewPage />} />
              <Route path="/external/:token/overview" element={<PublicExternalViewOverviewPage />} />
              <Route path="/external/:token/events" element={<PublicEventsPage />} />
              <Route path="/test-external" element={<PublicExternalViewTest />} />

              {/* Catch-all Not Found route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
