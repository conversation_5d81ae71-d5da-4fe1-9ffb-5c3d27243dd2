
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useAcademicYears } from '@/hooks/useAcademicYears';

export const useExtracurriculars = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useQuery({
    queryKey: ['extracurriculars', profile?.school_id, activeYear?.id],
    queryFn: async () => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      console.log('🎭 Fetching extracurriculars for:', {
        school_id: profile.school_id,
        academic_year_id: activeYear.id,
        profile: !!profile,
        activeYear: !!activeYear
      });

      const { data, error } = await supabase
        .from('extracurriculars')
        .select(`
          *,
          session_categories (
            id,
            name,
            color
          )
        `)
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeYear.id)
        .order('name', { ascending: true });

      if (error) {
        console.error('Error fetching extracurriculars:', error);
        throw error;
      }
      console.log('🎭 Extracurriculars fetched:', {
        count: data?.length || 0,
        data: data?.map(e => ({ name: e.name, session_category_id: e.session_category_id }))
      });
      return data || [];
    },
    enabled: !!profile?.school_id && !!activeYear?.id,
    staleTime: 0,
    gcTime: 0,
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

export const useCreateExtracurricular = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useMutation({
    mutationFn: async (extracurricularData: any) => {
      console.log('Creating extracurricular with data:', extracurricularData);

      try {
        if (!profile?.school_id || !activeYear?.id) {
          throw new Error('Data sekolah atau tahun akademik tidak ditemukan');
        }

        // Get EKSKUL category for this school and academic year
        const { data: ekstrakurikulerCategory } = await supabase
          .from('session_categories')
          .select('id, name')
          .eq('school_id', profile.school_id)
          .eq('academic_year_id', activeYear.id)
          .eq('name', 'Ekskul')
          .single();

        console.log('🎯 Found ekstrakurikuler category:', ekstrakurikulerCategory);

        const cleanExtracurricularData = {
          name: extracurricularData.name,
          description: extracurricularData.description || `Ekstrakurikuler ${extracurricularData.name}`,
          hours_per_year: extracurricularData.hours_per_year,
          color: extracurricularData.color || '#F97316',
          session_category_id: ekstrakurikulerCategory?.id || null, // ✅ FIXED: Use session_category_id
          school_id: profile.school_id,
          academic_year_id: activeYear.id,
        };

        console.log('Clean extracurricular data:', cleanExtracurricularData);

        const { data, error } = await supabase
          .from('extracurriculars')
          .insert(cleanExtracurricularData)
          .select()
          .single();

        if (error) {
          console.error('Error creating extracurricular:', error);
          throw error;
        }

        console.log('Extracurricular created:', data);
        return data;
      } catch (error) {
        console.error('Full error in createExtracurricular:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['extracurriculars'] });
      toast({
        title: "Berhasil",
        description: "Data ekstrakurikuler berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      console.error('Error in onError:', error);
      toast({
        title: "Gagal",
        description: "Gagal menambahkan data ekstrakurikuler: " + (error.message || 'Unknown error'),
        variant: "destructive",
      });
    },
  });
};

export const useUpdateExtracurricular = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...extracurricularData }: any) => {
      console.log('Updating extracurricular:', id, extracurricularData);

      const cleanExtracurricularData = {
        name: extracurricularData.name,
        hours_per_year: extracurricularData.hours_per_year || 0,
        color: extracurricularData.color || '#F97316',
        session_category_id: extracurricularData.session_category_id || null, // ✅ FIXED: Include session_category_id
      };

      const { data, error } = await supabase
        .from('extracurriculars')
        .update(cleanExtracurricularData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating extracurricular:', error);
        throw error;
      }

      console.log('Extracurricular updated:', data);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['extracurriculars'] });
      toast({
        title: "Berhasil",
        description: "Data ekstrakurikuler berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      console.error('Error updating extracurricular:', error);
      toast({
        title: "Gagal",
        description: "Gagal memperbarui data ekstrakurikuler: " + (error.message || 'Unknown error'),
        variant: "destructive",
      });
    },
  });
};

export const useDeleteExtracurricular = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      console.log('Deleting extracurricular:', id);

      const { error } = await supabase
        .from('extracurriculars')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting extracurricular:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['extracurriculars'] });
      toast({
        title: "Berhasil",
        description: "Data ekstrakurikuler berhasil dihapus",
      });
    },
    onError: (error: any) => {
      console.error('Error deleting extracurricular:', error);
      toast({
        title: "Gagal",
        description: "Gagal menghapus data ekstrakurikuler: " + (error.message || 'Unknown error'),
        variant: "destructive",
      });
    },
  });
};
