import React, { useState, useRef, useMemo, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import dayGridPlugin from '@fullcalendar/daygrid';
import idLocale from '@fullcalendar/core/locales/id';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Calendar, Users, MapPin, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';

interface TokenData {
  classId: string;
  schoolId: string;
}

const PublicExternalViewExact: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const [tokenData, setTokenData] = useState<TokenData | null>(null);
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [error, setError] = useState<string | null>(null);
  const calendarRef = useRef<FullCalendar>(null);

  // ✅ DECODE TOKEN
  useEffect(() => {
    if (!token) {
      setError('Token tidak valid');
      return;
    }

    try {
      const decoded = atob(token);
      const [classId, schoolId] = decoded.split(':');
      
      if (!classId || !schoolId) {
        setError('Token format tidak valid');
        return;
      }

      setTokenData({ classId, schoolId });
      console.log('✅ Token decoded successfully:', { classId, schoolId });
    } catch (decodeError) {
      console.error('❌ Token decode error:', decodeError);
      setError('Token tidak valid atau rusak');
    }
  }, [token]);

  // ✅ FETCH DATA DIRECTLY (NO AUTH REQUIRED)
  const [schedules, setSchedules] = useState<any[]>([]);
  const [academicWeeks, setAcademicWeeks] = useState<any[]>([]);
  const [timeSessions, setTimeSessions] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [school, setSchool] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // ✅ FETCH ALL DATA DIRECTLY USING EXACT SAME QUERIES AS HOOKS
  useEffect(() => {
    const fetchAllData = async () => {
      if (!tokenData) return;

      try {
        setIsLoading(true);
        console.log('🔄 Fetching all data with EXACT SAME queries as hooks...');

        // 1. Fetch schedules using EXACT SAME logic as useSchedules hook
        let schedulesData, schedulesError;

        try {
          const viewResult = await supabase
            .from('schedules_view')
            .select('*')
            .not('day_of_week', 'is', null)
            .order('academic_week', { ascending: true })
            .order('day_of_week', { ascending: true })
            .order('start_time', { ascending: true });

          if (viewResult.error) {
            console.log('⚠️ schedules_view failed, trying manual joins...');

            const manualResult = await supabase
              .from('class_schedules')
              .select(`
                id,
                academic_week,
                day_of_week,
                start_time,
                end_time,
                teacher_id,
                room,
                classes!inner (
                  id,
                  name,
                  level,
                  grade
                ),
                schedule_subjects!inner (
                  id,
                  name,
                  color,
                  session_categories (
                    id,
                    name,
                    color
                  )
                ),
                teachers (
                  id,
                  full_name
                )
              `)
              .not('day_of_week', 'is', null)
              .order('academic_week', { ascending: true })
              .order('day_of_week', { ascending: true })
              .order('start_time', { ascending: true });

            if (manualResult.error) {
              throw manualResult.error;
            }

            schedulesData = manualResult.data?.map(schedule => ({
              id: schedule.id,
              academic_week: schedule.academic_week,
              day_of_week: schedule.day_of_week,
              start_time: schedule.start_time,
              end_time: schedule.end_time,
              teacher_id: schedule.teacher_id,
              teacher_name: schedule.teachers?.full_name,
              room: schedule.room,
              class_id: schedule.classes?.id,
              class_name: schedule.classes?.name,
              class_level: schedule.classes?.level,
              class_grade: schedule.classes?.grade,
              subject_id: schedule.schedule_subjects?.id,
              subject_name: schedule.schedule_subjects?.name,
              subject_color: schedule.schedule_subjects?.color,
              category_id: schedule.schedule_subjects?.session_categories?.id,
              category_name: schedule.schedule_subjects?.session_categories?.name,
              category_color: schedule.schedule_subjects?.session_categories?.color
            }));
          } else {
            schedulesData = viewResult.data;
          }
        } catch (error) {
          console.error('❌ All schedule fetch methods failed:', error);
          schedulesError = error;
        }

        if (!schedulesError) {
          setSchedules(schedulesData || []);
          console.log('✅ Schedules loaded:', schedulesData?.length);
        }

        // 2. Fetch academic years and generate weeks using EXACT SAME logic as useAcademicWeeks
        const { data: academicYears } = await supabase
          .from('academic_years')
          .select('*')
          .eq('school_id', tokenData.schoolId)
          .eq('is_active', true);

        const activeAcademicYear = academicYears?.find(year => year.is_active);

        if (activeAcademicYear) {
          console.log('✅ Using active academic year:', activeAcademicYear.year_name);

          // Use exact same logic as useAcademicWeeks hook with date-fns
          const startDate = new Date(activeAcademicYear.start_date);
          const endDate = new Date(activeAcademicYear.end_date);
          const weeks = [];

          let currentWeek = new Date(startDate);
          // Set to Monday of the week (startOfWeek equivalent)
          currentWeek.setDate(currentWeek.getDate() - currentWeek.getDay() + 1);
          let weekNumber = 1;

          while (currentWeek <= endDate && weekNumber <= 52) {
            const weekEnd = new Date(currentWeek);
            weekEnd.setDate(currentWeek.getDate() + 6); // Sunday (endOfWeek equivalent)

            weeks.push({
              weekNumber,
              startDate: new Date(currentWeek),
              endDate: weekEnd,
              label: `Minggu ${weekNumber}`,
              dateRange: `${currentWeek.getDate()}/${currentWeek.getMonth() + 1} - ${weekEnd.getDate()}/${weekEnd.getMonth() + 1}`,
              isCurrentWeek: false
            });

            currentWeek.setDate(currentWeek.getDate() + 7); // Next week
            weekNumber++;
          }

          setAcademicWeeks(weeks);
          console.log('✅ Academic weeks generated from active year:', weeks.length);
        } else {
          console.log('⚠️ No active academic year found');
          setAcademicWeeks([]);
        }

        // 3. Fetch other data
        const { data: schoolData } = await supabase
          .from('schools')
          .select('*')
          .eq('id', tokenData.schoolId)
          .single();

        if (schoolData) {
          setSchool(schoolData);
          console.log('✅ School loaded:', schoolData.name);
        }

        const { data: classesData } = await supabase
          .from('classes')
          .select('*')
          .eq('school_id', tokenData.schoolId);

        if (classesData) {
          setClasses(classesData);
          console.log('✅ Classes loaded:', classesData.length);
        }

        const { data: timeData } = await supabase
          .from('time_sessions')
          .select('*')
          .eq('school_id', tokenData.schoolId)
          .order('start_time', { ascending: true });

        if (timeData) {
          setTimeSessions(timeData);
          console.log('✅ Time sessions loaded:', timeData.length);
        }

      } catch (error) {
        console.error('❌ Error fetching data:', error);
        setError('Gagal memuat data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllData();
  }, [tokenData]);

  // ✅ FIND CLASS DATA
  const selectedClass = classes.find(c => c.id === tokenData?.classId);

  // ✅ EXACT SAME FILTERING LOGIC AS MAIN SCHEDULE
  const filteredSchedules = useMemo(() => {
    console.log('🔥 EXACT FILTERING (SAME AS MAIN SCHEDULE):', {
      schedulesLength: schedules?.length || 0,
      selectedWeek,
      selectedClassId: tokenData?.classId,
      isLoading: schedulesLoading
    });

    if (!schedules || !tokenData?.classId) {
      console.log('❌ No schedules or class ID');
      return [];
    }

    const filtered = schedules.filter((schedule: any) => {
      const scheduleWeek = parseInt(schedule.academic_week);
      const selectedWeekInt = parseInt(selectedWeek.toString());
      const weekMatch = scheduleWeek === selectedWeekInt;
      const classMatch = schedule.class_id === tokenData.classId;
      
      const hasRequiredFields = schedule.start_time && 
                               schedule.end_time && 
                               (schedule.subject_name || schedule.schedule_subjects?.name) &&
                               schedule.day_of_week;

      const isValidTimeFormat = /^\d{2}:\d{2}:\d{2}$/.test(schedule.start_time) && 
                               /^\d{2}:\d{2}:\d{2}$/.test(schedule.end_time);

      const result = weekMatch && classMatch && hasRequiredFields && isValidTimeFormat;

      if (result) {
        console.log('✅ Schedule passed filters:', {
          id: schedule.id,
          subject_name: schedule.subject_name || schedule.schedule_subjects?.name,
          academic_week: schedule.academic_week,
          day_of_week: schedule.day_of_week,
          start_time: schedule.start_time,
          end_time: schedule.end_time
        });
      }

      return result;
    });

    console.log('✅ Filtered schedules (EXACT SAME LOGIC):', {
      originalCount: schedules.length,
      filteredCount: filtered.length,
      selectedWeek,
      selectedClassId: tokenData.classId
    });

    return filtered;
  }, [schedules, selectedWeek, tokenData?.classId, schedulesLoading]);

  // ✅ EXACT SAME TIME RANGE LOGIC AS MAIN SCHEDULE
  const timeRange = useMemo(() => {
    if (!timeSessions || timeSessions.length === 0) {
      return {
        slotMinTime: '06:00:00',
        slotMaxTime: '22:00:00'
      };
    }

    const startTimes = timeSessions.map(session => session.start_time).filter(Boolean);
    const endTimes = timeSessions.map(session => session.end_time).filter(Boolean);

    if (startTimes.length === 0 || endTimes.length === 0) {
      return {
        slotMinTime: '06:00:00',
        slotMaxTime: '22:00:00'
      };
    }

    const earliestStart = startTimes.reduce((earliest, current) => 
      current < earliest ? current : earliest
    );
    const latestEnd = endTimes.reduce((latest, current) => 
      current > latest ? current : latest
    );

    // Add buffer
    const startHour = Math.max(0, parseInt(earliestStart.split(':')[0]) - 1);
    const endHour = Math.min(23, parseInt(latestEnd.split(':')[0]) + 1);

    return {
      slotMinTime: `${startHour.toString().padStart(2, '0')}:00:00`,
      slotMaxTime: `${endHour.toString().padStart(2, '0')}:00:00`
    };
  }, [timeSessions]);

  // ✅ EXACT SAME CURRENT WEEK DATE CALCULATION AS MAIN SCHEDULE
  const getCurrentWeekDate = () => {
    const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);
    if (currentWeek) {
      return new Date(currentWeek.startDate);
    }
    
    // Fallback calculation
    const baseDate = new Date(2025, 6, 7); // July 7, 2025 (Monday) - Academic year start
    const weekOffset = (selectedWeek - 1) * 7;
    const calculatedDate = new Date(baseDate);
    calculatedDate.setDate(baseDate.getDate() + weekOffset);
    return calculatedDate;
  };

  const currentCalendarDate = getCurrentWeekDate();

  // ✅ EXACT SAME CALENDAR EVENTS CONVERSION AS MAIN SCHEDULE
  const calendarEvents = useMemo(() => {
    console.log('🔥 EXACT CALENDAR EVENTS CONVERSION (SAME AS MAIN):', {
      filteredCount: filteredSchedules?.length || 0,
      selectedWeek,
      academicWeeksCount: academicWeeks?.length || 0,
      currentCalendarDate: currentCalendarDate.toISOString()
    });

    if (!filteredSchedules || filteredSchedules.length === 0) {
      console.log('❌ No filtered schedules to convert');
      return [];
    }

    const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);
    if (!currentWeek) {
      console.log('❌ Current week not found:', selectedWeek);
      return [];
    }

    const weekStartDate = new Date(currentWeek.startDate);
    console.log('📅 Week start date:', weekStartDate.toISOString());

    const events = filteredSchedules
      .map((schedule: any) => {
        try {
          // Handle both schedules_view and manual join data structures
          let subject, category;
          
          if (schedule.subject_name) {
            subject = {
              id: schedule.subject_id,
              name: schedule.subject_name,
              color: schedule.subject_color
            };
            category = {
              id: schedule.category_id,
              name: schedule.category_name,
              color: schedule.category_color
            };
          } else if (schedule.schedule_subjects) {
            subject = schedule.schedule_subjects;
            category = schedule.schedule_subjects.session_categories;
          } else {
            console.warn('⚠️ Schedule missing subject data:', schedule);
            return null;
          }

          // Date calculation logic
          const dayOfWeek = schedule.day_of_week;
          const dayOffset = dayOfWeek - 1; // Convert to 0-based (Monday = 0)
          
          const scheduleDate = new Date(weekStartDate);
          scheduleDate.setDate(weekStartDate.getDate() + dayOffset);
          
          // Time parsing and datetime creation
          const [startHour, startMinute, startSecond] = schedule.start_time.split(':').map(Number);
          const [endHour, endMinute, endSecond] = schedule.end_time.split(':').map(Number);
          
          const startDateTime = new Date(scheduleDate);
          startDateTime.setHours(startHour, startMinute, startSecond || 0, 0);
          
          const endDateTime = new Date(scheduleDate);
          endDateTime.setHours(endHour, endMinute, endSecond || 0, 0);

          // Color logic
          const colors = {
            background: subject?.color || category?.color || '#6b7280',
            border: '#ffffff',
            text: '#ffffff'
          };

          const calendarEvent = {
            id: schedule.id,
            title: subject?.name || 'Mata Pelajaran',
            start: startDateTime.toISOString(),
            end: endDateTime.toISOString(),
            backgroundColor: colors.background,
            borderColor: colors.border,
            textColor: colors.text,
            extendedProps: {
              schedule,
              teacher: { id: schedule.teacher_id, full_name: schedule.teacher_name },
              class: { id: schedule.class_id, name: schedule.class_name },
              subject: subject,
              category: category,
              originalColor: colors.background,
              isPublic: true
            }
          };

          console.log('🔥 EXACT CREATED CALENDAR EVENT:', calendarEvent);
          return calendarEvent;
        } catch (error) {
          console.error('❌ Error converting schedule to event:', error, schedule);
          return null;
        }
      })
      .filter(Boolean);

    console.log('🔥 EXACT FINAL CALENDAR EVENTS:', {
      eventsCount: events.length,
      events: events
    });

    return events;
  }, [filteredSchedules, academicWeeks, selectedWeek, currentCalendarDate]);

  // ✅ WEEK NAVIGATION
  const handlePrevWeek = () => {
    if (selectedWeek > 1) {
      setSelectedWeek(selectedWeek - 1);
    }
  };

  const handleNextWeek = () => {
    if (selectedWeek < 52) {
      setSelectedWeek(selectedWeek + 1);
    }
  };

  const currentWeekData = academicWeeks.find(w => w.weekNumber === selectedWeek);

  // ✅ LOADING STATE
  if (!tokenData || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Memuat jadwal...</p>
        </div>
      </div>
    );
  }

  // ✅ ERROR STATE
  if (error || !selectedClass) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                {error || 'Kelas tidak ditemukan'}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  console.log('🔥 EXACT PUBLIC PAGE DATA:', {
    schedulesCount: schedules.length,
    academicWeeksCount: academicWeeks.length,
    timeSessionsCount: timeSessions.length,
    selectedClass: selectedClass.name,
    selectedWeek,
    currentCalendarDate: currentCalendarDate.toISOString(),
    timeRange,
    filteredSchedulesCount: filteredSchedules.length,
    calendarEventsCount: calendarEvents.length
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* ✅ HEADER */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {school?.logo_url && (
                <img 
                  src={school.logo_url} 
                  alt="Logo Sekolah" 
                  className="h-12 w-12 object-contain"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {school?.name || 'SMA IT HSI'}
                </h1>
                <p className="text-gray-600">
                  Jadwal Kelas {selectedClass.name}
                </p>
              </div>
            </div>
            
            <div className="text-right text-sm text-gray-500">
              <p className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Tahun Akademik 2025/2026
              </p>
              <p className="flex items-center mt-1">
                <Users className="h-4 w-4 mr-1" />
                Kelas {selectedClass.name}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* ✅ MAIN CONTENT */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <Card>
          <CardContent className="p-6">
            {/* Week Navigation */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevWeek}
                  disabled={selectedWeek <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Minggu {selectedWeek}
                  </h3>
                  {currentWeekData && (
                    <p className="text-sm text-gray-600">
                      {currentWeekData.dateRange}
                    </p>
                  )}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextWeek}
                  disabled={selectedWeek >= 52}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="text-sm text-gray-500">
                {filteredSchedules.length} kegiatan
              </div>
            </div>

            {/* ✅ EXACT SAME FULLCALENDAR AS MAIN SCHEDULE */}
            <div className="bg-white rounded-lg border border-gray-200">
              <FullCalendar
                ref={calendarRef}
                plugins={[timeGridPlugin, dayGridPlugin]}
                initialView="timeGridWeek"
                initialDate={currentCalendarDate}
                headerToolbar={false}
                events={calendarEvents}
                
                // ✅ DISABLE INTERACTIONS for public view
                editable={false}
                selectable={false}
                selectMirror={false}
                dayMaxEvents={true}
                weekends={true}
                height="auto"
                locale={idLocale}
                
                // ✅ EXACT SAME TIME SETTINGS AS MAIN SCHEDULE
                slotMinTime={timeRange.slotMinTime}
                slotMaxTime={timeRange.slotMaxTime}
                slotDuration="00:30:00"
                slotLabelInterval="01:00:00"
                allDaySlot={false}
                eventMinHeight={15}
                eventShortHeight={30}
                
                // ✅ EXACT SAME STYLING AS MAIN SCHEDULE
                dayHeaderFormat={{
                  weekday: 'short',
                  month: 'numeric',
                  day: 'numeric'
                }}
                businessHours={{
                  daysOfWeek: [1, 2, 3, 4, 5, 6, 0], // Include Sunday (0)
                  startTime: timeRange.slotMinTime.slice(0, 5),
                  endTime: timeRange.slotMaxTime.slice(0, 5)
                }}
                
                // ✅ NO INTERACTIONS for public view
                eventClick={() => {}}
                select={() => {}}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ✅ FOOTER */}
      <div className="bg-white border-t border-gray-200 mt-8">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="text-center text-sm text-gray-500">
            <p className="flex items-center justify-center">
              <MapPin className="h-4 w-4 mr-1" />
              {school?.address || 'Purworejo, Jawa Tengah'}
            </p>
            <p className="mt-1">📅 Jadwal ini adalah tampilan publik - hanya untuk melihat</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicExternalViewExact;
