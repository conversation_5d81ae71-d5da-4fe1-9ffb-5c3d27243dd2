import React from 'react';
import { ArrowRight, Shield, Clock, Users, Zap, Calendar, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import LoadingPill from './LoadingPill';
import CalendarPreview from './CalendarPreview';
interface LandingPageProps {
  onLoginClick: () => void;
}
const LandingPage = ({
  onLoginClick
}: LandingPageProps) => {
  const features = [{
    icon: Calendar,
    title: 'Manajemen Jadwal Cerdas',
    description: 'Sistem otomatis untuk mengatur jadwal pelajaran tanpa bentrok',
    color: 'lime'
  }, {
    icon: Users,
    title: 'Koordinasi Multi-User',
    description: 'Guru, admin, dan siswa dapat mengakses jadwal secara real-time',
    color: 'blue'
  }, {
    icon: Clock,
    title: 'Notifikasi Real-time',
    description: 'Pemberitahuan otomatis untuk perubahan jadwal dan event penting',
    color: 'purple'
  }, {
    icon: Shield,
    title: 'Sistem Aman & Terpercaya',
    description: 'Data terlindungi dengan enkripsi tingkat enterprise',
    color: 'cyan'
  }];
  const benefits = ['Mengurangi bentrok jadwal hingga 95%', 'Menghemat waktu penjadwalan hingga 80%', 'Meningkatkan koordinasi antar guru', 'Dashboard analytics yang comprehensive', 'Support untuk tahun ajaran multiple', 'Export jadwal ke berbagai format'];
  return <div className="min-h-screen bg-background">
      {/* Background glow effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-lime-400/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-3/4 left-1/2 w-64 h-64 bg-purple-400/5 rounded-full blur-3xl animate-pulse"></div>
      </div>

      {/* Hero Section */}
      <section className="relative pt-32 pb-20 px-6">
        <div className="max-w-7xl mx-auto text-center">
          <LoadingPill />
          
          <h1 className="text-5xl md:text-7xl font-bold text-foreground mb-6 tracking-tight">
            Jadwal Pelajaran
            <span className="block bg-gradient-to-r from-lime-400 to-blue-400 bg-clip-text text-transparent">
              Masa Depan
            </span>
          </h1>

          <p className="text-xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed tracking-wide">
            Revolusikan cara sekolah dan pondok pesantren di Indonesia mengelola jadwal akademik
            dengan teknologi AI yang cerdas dan intuitif.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16">
            <Button onClick={onLoginClick} className="bg-lime-500 hover:bg-lime-600 text-gray-900 font-semibold px-8 py-4 rounded-xl text-lg transition-all duration-300 shadow-lg transform hover:scale-105">
              Mulai Sekarang
              <ArrowRight className="ml-2" size={20} />
            </Button>
            
            <Button variant="outline" className="border-border hover:bg-accent/50 px-8 py-4 rounded-xl text-lg backdrop-blur-sm">
              Lihat Demo
            </Button>
          </div>

          {/* Calendar Preview */}
          <CalendarPreview />
        </div>
      </section>

      {/* Features Section */}
      <section className="relative py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">
              Fitur <span className="text-lime-400">Unggulan</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Solusi lengkap untuk semua kebutuhan penjadwalan institusi pendidikan modern
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => <div key={index} className="group">
                <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-600/30 rounded-2xl p-6 h-full hover:bg-gray-800/60 transition-all duration-300 hover:border-gray-500/50 hover:transform hover:scale-105">
                  <div className={`w-12 h-12 bg-${feature.color}-400/20 rounded-xl flex items-center justify-center mb-4 group-hover:bg-${feature.color}-400/30 transition-colors duration-300`}>
                    <feature.icon className={`text-${feature.color}-400`} size={24} />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                  <p className="text-gray-400 leading-relaxed">{feature.description}</p>
                </div>
              </div>)}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="relative py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl font-bold text-white mb-6">
                Mengapa Memilih <span className="text-blue-400">Indo Jadwal</span>?
              </h2>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Dipercaya oleh ribuan institusi pendidikan di seluruh Indonesia untuk 
                mengelola jadwal akademik dengan efisiensi maksimal.
              </p>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="text-lime-400 flex-shrink-0" size={20} />
                    <span className="text-gray-200">{benefit}</span>
                  </div>)}
              </div>

              <Button onClick={onLoginClick} className="mt-8 bg-blue-500 hover:bg-blue-600 text-white font-semibold px-8 py-4 rounded-xl text-lg transition-all duration-300 shadow-lg">
                Coba Gratis Sekarang
                <Zap className="ml-2" size={20} />
              </Button>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-3xl blur-xl"></div>
              <div className="relative bg-gray-800/40 backdrop-blur-sm border border-gray-600/30 rounded-3xl p-8">
                <div className="text-center">
                  <div className="text-5xl font-bold text-white mb-2">1000+</div>
                  <div className="text-gray-400 mb-6">Sekolah & Pesantren</div>
                  
                  <div className="grid grid-cols-2 gap-6 mt-8">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-lime-400">95%</div>
                      <div className="text-sm text-gray-400">Efisiensi Jadwal</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-400">24/7</div>
                      <div className="text-sm text-gray-400">Support</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-r from-lime-400/10 to-blue-400/10 backdrop-blur-sm border border-gray-600/30 rounded-3xl p-12">
            <h2 className="text-4xl font-bold text-white mb-6">
              Siap Mengoptimalkan Jadwal Anda?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Bergabunglah dengan revolusi digital pendidikan Indonesia
            </p>
            
            <Button onClick={onLoginClick} className="bg-lime-500 hover:bg-lime-600 text-gray-900 font-bold px-12 py-5 rounded-xl text-xl transition-all duration-300 shadow-lg transform hover:scale-105">
              Mulai Transformasi Digital
              <ArrowRight className="ml-3" size={24} />
            </Button>
          </div>
        </div>
      </section>
    </div>;
};
export default LandingPage;