import { useMemo } from 'react';
import { useSchedulesComplete } from './useSchedulesPaginated';
import { useSubjects } from './useSubjects';
import { useSessionCategories } from './useSessionCategories';
import { useAcademicWeeks } from './useAcademicWeeks';
import { useScheduleSubjectsByCategory } from './useScheduleSubjectsByCategory';
import { useActiveAcademicYear } from './useAcademicYears';

export type PeriodType = 'weekly' | 'monthly' | 'all';

export interface JPSubjectDetail {
  id: string;
  name: string;
  jp: number;
  hours: number;
  minutes: number;
  color: string;
}

export interface JPCategoryDetail {
  id: string;
  name: string;
  totalJP: number;
  totalHours: number;
  totalMinutes: number;
  subjects: JPSubjectDetail[];
  color: string;
  isExpanded: boolean;
}

export interface JPPeriodCalculation {
  period: PeriodType;
  dateRange: string;
  categories: JPCategoryDetail[];
  grandTotal: {
    totalJP: number;
    totalHours: number;
    totalMinutes: number;
  };
}

interface UseJPPeriodCalculationProps {
  selectedClassId?: string;
  selectedWeek?: number;
  periodType: PeriodType;
  customDateRange?: { start: Date; end: Date };
}

export const useJPPeriodCalculation = ({
  selectedClassId,
  selectedWeek,
  periodType,
  customDateRange
}: UseJPPeriodCalculationProps) => {
  // 🚀 FIXED: Use paginated hook to get ALL data including weeks 14-24
  const { data: schedules = [], isLoading: schedulesLoading, error: schedulesError } = useSchedulesComplete();
  const { data: subjects = [] } = useSubjects();
  const { data: sessionCategories = [] } = useSessionCategories();
  const { academicWeeks } = useAcademicWeeks();
  const { data: activeAcademicYear } = useActiveAcademicYear();

  // ✅ PERBAIKAN: Gunakan data yang sama dengan sidebar
  const { data: scheduleSubjectsByCategory = [] } = useScheduleSubjectsByCategory(selectedClassId);

  const calculation = useMemo(() => {
    console.log('🔍 JP Period Calculation Debug (PAGINATED):', {
      selectedClassId,
      selectedWeek,
      periodType,
      totalSchedules: schedules.length,
      totalSubjects: subjects.length,
      totalSessionCategories: sessionCategories.length,
      scheduleSubjectsByCategory: scheduleSubjectsByCategory.length,
      isLoading: schedulesLoading,
      error: schedulesError,
      activeAcademicYear: activeAcademicYear?.year_name
    });

    if (!selectedClassId) {
      console.log('⚠️ No selectedClassId provided');
      return {
        period: periodType,
        dateRange: '',
        categories: [],
        grandTotal: { totalJP: 0, totalHours: 0, totalMinutes: 0 }
      };
    }

    if (!scheduleSubjectsByCategory.length) {
      console.log('⚠️ No scheduleSubjectsByCategory data available');
      return {
        period: periodType,
        dateRange: '',
        categories: [],
        grandTotal: { totalJP: 0, totalHours: 0, totalMinutes: 0 }
      };
    }

    // Filter schedules berdasarkan periode
    let filteredSchedules = schedules.filter(schedule =>
      schedule.class_id === selectedClassId
    );

    console.log('📅 Filtered schedules by class (PAGINATED):', {
      classId: selectedClassId,
      filteredCount: filteredSchedules.length,
      schedules: filteredSchedules.map(s => ({
        id: s.id,
        subject_id: s.subject_id,
        academic_week: s.academic_week,
        start_time: s.start_time,
        end_time: s.end_time
      }))
    });

    // 🔍 ENHANCED DEBUG: Analisis khusus untuk minggu 14+ di JP calculation
    if (selectedWeek && selectedWeek >= 14) {
      console.log(`🔍 JP CALCULATION MINGGU ${selectedWeek} ANALYSIS:`, {
        totalSchedulesInData: schedules?.length || 0,
        schedulesForThisWeek: schedules?.filter(s => s.academic_week === selectedWeek).length || 0,
        schedulesForThisClass: schedules?.filter(s => s.class_id === selectedClassId).length || 0,
        schedulesForThisWeekAndClass: filteredSchedules.length,
        weekDistribution: schedules?.reduce((acc: any, s: any) => {
          acc[s.academic_week] = (acc[s.academic_week] || 0) + 1;
          return acc;
        }, {}),
        availableWeeks: [...new Set(schedules?.map(s => s.academic_week))].sort((a, b) => a - b)
      });
    }

    let dateRange = '';

    switch (periodType) {
      case 'weekly':
        if (selectedWeek) {
          filteredSchedules = filteredSchedules.filter(schedule => 
            schedule.academic_week === selectedWeek
          );
          const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);
          dateRange = currentWeek?.dateRange || `Minggu ${selectedWeek}`;
        }
        break;
      
      case 'monthly':
        // Implementasi filter bulanan berdasarkan schedule_date atau academic_week
        if (selectedWeek) {
          const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);
          if (currentWeek) {
            const weekDate = new Date(currentWeek.startDate);
            const month = weekDate.getMonth();
            const year = weekDate.getFullYear();

            // Filter berdasarkan bulan dari minggu yang dipilih
            const weeksInMonth = academicWeeks.filter(week => {
              const wDate = new Date(week.startDate);
              return wDate.getMonth() === month && wDate.getFullYear() === year;
            });

            const weekNumbers = weeksInMonth.map(w => w.weekNumber);
            filteredSchedules = filteredSchedules.filter(schedule =>
              weekNumbers.includes(schedule.academic_week)
            );

            const monthNames = ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                              'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
            dateRange = `${monthNames[month]} ${year}`;
          }
        }
        break;
      
      case 'all':
        // 🚀 FIXED: Use active academic year instead of hardcoded
        dateRange = activeAcademicYear?.year_name || 'Tahun Akademik';
        break;
      
      default:
        break;
    }

    // Fungsi helper untuk menghitung JP dari menit
    const calculateJP = (minutes: number) => Math.round((minutes / 45) * 10) / 10;
    const calculateHours = (minutes: number) => Math.round((minutes / 60) * 10) / 10;

    // ✅ PERBAIKAN: Gunakan data dari sidebar yang sudah difilter berdasarkan kelas
    const categoriesMap = new Map<string, JPCategoryDetail>();

    console.log('📚 Processing categories from sidebar data:', {
      categoriesCount: scheduleSubjectsByCategory.length,
      categories: scheduleSubjectsByCategory.map(cat => ({
        name: cat.name,
        subjectsCount: cat.subjects.length,
        subjects: cat.subjects.map(s => s.name)
      }))
    });

    // Process setiap kategori dari sidebar
    scheduleSubjectsByCategory.forEach(category => {
      console.log(`🔍 Processing category: ${category.name} with ${category.subjects.length} subjects`);

      if (category.subjects.length === 0) {
        console.log(`⚠️ Skipping empty category: ${category.name}`);
        return;
      }

      const categoryDetail: JPCategoryDetail = {
        id: category.id,
        name: category.name.toUpperCase(),
        totalJP: 0,
        totalHours: 0,
        totalMinutes: 0,
        subjects: [],
        color: category.color || '#3B82F6',
        isExpanded: true
      };

      // Process setiap subject dalam kategori
      category.subjects.forEach(subject => {
        console.log(`📖 Processing subject: ${subject.name} (${subject.id})`);

        const subjectSchedules = filteredSchedules.filter(schedule => {
          // Match berdasarkan subject_id
          const isMatch = schedule.subject_id === subject.id;
          console.log(`🔍 Schedule check for ${subject.name}:`, {
            scheduleSubjectId: schedule.subject_id,
            subjectId: subject.id,
            isMatch,
            scheduleTime: `${schedule.start_time}-${schedule.end_time}`
          });
          return isMatch;
        });

        console.log(`📅 Found ${subjectSchedules.length} schedules for ${subject.name}`);

        // ✅ ENHANCED DEBUG: Special logging for Aqidatuna and Nabiyuna
        if (subject.name === 'Aqidatuna' || subject.name === 'Nabiyuna') {
          console.log(`🔍 JP PERIOD CALCULATION DEBUG for ${subject.name}:`, {
            subject_id: subject.id,
            total_filtered_schedules: filteredSchedules.length,
            subject_schedules_count: subjectSchedules.length,
            subject_schedules_detail: subjectSchedules.map(s => ({
              id: s.id,
              subject_id: s.subject_id,
              start_time: s.start_time,
              end_time: s.end_time,
              academic_week: s.academic_week,
              day_of_week: s.day_of_week
            }))
          });
        }

        const totalMinutes = subjectSchedules.reduce((total, schedule) => {
          const [startHour, startMinute] = schedule.start_time.split(':').map(Number);
          const [endHour, endMinute] = schedule.end_time.split(':').map(Number);
          const startTotalMinutes = startHour * 60 + startMinute;
          const endTotalMinutes = endHour * 60 + endMinute;
          const duration = endTotalMinutes - startTotalMinutes;

          console.log(`⏰ Schedule duration for ${subject.name}:`, {
            startTime: schedule.start_time,
            endTime: schedule.end_time,
            duration
          });

          return total + duration;
        }, 0);

        // Selalu tambahkan subject, bahkan jika totalMinutes = 0
        const jp = calculateJP(totalMinutes);
        const hours = calculateHours(totalMinutes);

        console.log(`📊 JP calculation for ${subject.name}:`, {
          totalMinutes,
          jp,
          hours
        });

        categoryDetail.subjects.push({
          id: subject.id,
          name: subject.name,
          jp,
          hours,
          minutes: totalMinutes,
          color: subject.color || category.color || '#3B82F6'
        });

        categoryDetail.totalJP += jp;
        categoryDetail.totalHours += hours;
        categoryDetail.totalMinutes += totalMinutes;
      });

      // Selalu tambahkan kategori, bahkan jika tidak ada schedule
      console.log(`✅ Adding category ${category.name} with ${categoryDetail.subjects.length} subjects, total JP: ${categoryDetail.totalJP}`);
      categoriesMap.set(category.id, categoryDetail);
    });

    // ✅ SELESAI: Semua kategori sudah diproses dari sidebar data

    // Calculate grand total
    const categories = Array.from(categoriesMap.values());
    const grandTotal = categories.reduce(
      (total, category) => ({
        totalJP: total.totalJP + category.totalJP,
        totalHours: total.totalHours + category.totalHours,
        totalMinutes: total.totalMinutes + category.totalMinutes
      }),
      { totalJP: 0, totalHours: 0, totalMinutes: 0 }
    );

    // Round grand total
    grandTotal.totalJP = Math.round(grandTotal.totalJP * 10) / 10;
    grandTotal.totalHours = Math.round(grandTotal.totalHours * 10) / 10;

    return {
      period: periodType,
      dateRange,
      categories,
      grandTotal
    };
  }, [schedules, subjects, sessionCategories, academicWeeks, selectedClassId, selectedWeek, periodType, customDateRange, scheduleSubjectsByCategory]);

  return calculation;
};
