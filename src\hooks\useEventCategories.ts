
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useAcademicYears } from './useAcademicYears';
import { EventCategory, DEFAULT_CATEGORIES } from '@/types/event';
import { toast } from '@/hooks/use-toast';

export const useEventCategories = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const queryClient = useQueryClient();
  
  const activeAcademicYear = academicYears?.find(year => year.is_active);

  const { data: customCategories = [], isLoading } = useQuery({
    queryKey: ['event-categories', profile?.school_id, activeAcademicYear?.id],
    queryFn: async () => {
      if (!profile?.school_id || !activeAcademicYear?.id) return [];
      
      const { data, error } = await supabase
        .from('event_categories')
        .select('*')
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeAcademicYear.id)
        .order('name');

      if (error) {
        console.error('Error fetching event categories:', error);
        return [];
      }

      return data || [];
    },
    enabled: !!profile?.school_id && !!activeAcademicYear?.id,
  });

  const allCategories = [...DEFAULT_CATEGORIES, ...customCategories];

  const createCategoryMutation = useMutation({
    mutationFn: async (category: Omit<EventCategory, 'id'>) => {
      if (!profile?.school_id || !activeAcademicYear?.id) {
        throw new Error('Missing school or academic year data');
      }

      const { data, error } = await supabase
        .from('event_categories')
        .insert([{
          name: category.name,
          color: category.color,
          school_id: profile.school_id,
          academic_year_id: activeAcademicYear.id,
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['event-categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori baru telah ditambahkan",
      });
    },
    onError: (error) => {
      console.error('Error creating category:', error);
      toast({
        title: "Error",
        description: "Gagal menambahkan kategori",
        variant: "destructive",
      });
    },
  });

  const updateCategoryMutation = useMutation({
    mutationFn: async ({ id, ...updates }: Partial<EventCategory> & { id: string }) => {
      const { data, error } = await supabase
        .from('event_categories')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['event-categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori telah diperbarui",
      });
    },
    onError: (error) => {
      console.error('Error updating category:', error);
      toast({
        title: "Error",
        description: "Gagal memperbarui kategori",
        variant: "destructive",
      });
    },
  });

  const deleteCategoryMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('event_categories')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['event-categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori telah dihapus",
      });
    },
    onError: (error) => {
      console.error('Error deleting category:', error);
      toast({
        title: "Error",
        description: "Gagal menghapus kategori",
        variant: "destructive",
      });
    },
  });

  return {
    categories: allCategories,
    customCategories,
    isLoading,
    createCategory: createCategoryMutation.mutate,
    updateCategory: updateCategoryMutation.mutate,
    deleteCategory: deleteCategoryMutation.mutate,
    isCreating: createCategoryMutation.isPending,
    isUpdating: updateCategoryMutation.isPending,
    isDeleting: deleteCategoryMutation.isPending,
  };
};
