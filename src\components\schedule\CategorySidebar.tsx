import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, GripVertical } from 'lucide-react';
import { useSubjects } from '@/hooks/useSubjects';
interface CategorySidebarProps {
  categories: any[];
  onSubjectDrag: (subject: any) => void;
}
export const CategorySidebar: React.FC<CategorySidebarProps> = ({
  categories,
  onSubjectDrag
}) => {
  const {
    data: subjects
  } = useSubjects();
  const getSubjectsByCategory = (categoryName: string) => {
    return subjects?.filter(subject => subject.category?.toLowerCase() === categoryName.toLowerCase()) || [];
  };
  const handleDragStart = (e: React.DragEvent, subject: any) => {
    e.dataTransfer.setData('application/json', JSON.stringify(subject));
    onSubjectDrag(subject);
  };
  return <div className="h-full bg-gradient-to-br from-gray-800/0 via-gray-800 to-gray-800/0 p-4 space-y-4 py-[22px] mx-[50px] px-[30px] my-0">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold bg-gradient-to-r from-white via-blue-100 to-blue-200 bg-clip-text text-transparent">
          Kategori
        </h3>
        <Button size="sm" className="bg-gradient-to-r from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600 text-white font-semibold shadow-lg hover:shadow-blue-400/25 transition-all duration-300">
          <Plus className="h-4 w-4 mr-1" />
          Mapel
        </Button>
      </div>

      <div className="space-y-3 overflow-y-auto h-[calc(100vh-200px)]">
        {categories.map(category => <Card key={category.id} className="bg-gradient-to-br from-gray-800/80 via-gray-800/60 to-gray-900/80 border border-gray-600/50 backdrop-blur-xl shadow-xl">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2" style={{
            color: category.color
          }}>
                <div className="w-3 h-3 rounded-full shadow-lg" style={{
              backgroundColor: category.color,
              boxShadow: `0 0 10px ${category.color}40`
            }} />
                <span className="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent font-semibold">
                  {category.name}
                </span>
                {category.name !== 'EKSTRAKURIKULER' && (
                  <span className="text-xs text-gray-400 ml-auto">
                    {getSubjectsByCategory(category.name).length} JP/Tahun
                  </span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 space-y-2">
              {getSubjectsByCategory(category.name).map(subject => <div key={subject.id} draggable onDragStart={e => handleDragStart(e, subject)} className="bg-gradient-to-r from-gray-700/40 via-gray-700/30 to-gray-800/40 border border-gray-600/50 rounded-lg p-2 cursor-move hover:from-gray-600/50 hover:via-gray-600/40 hover:to-gray-700/50 transition-all duration-300 group backdrop-blur-sm shadow-lg hover:shadow-xl">
                  <div className="flex items-center gap-2">
                    <GripVertical className="h-4 w-4 text-gray-400 group-hover:text-blue-400 transition-colors" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-white truncate">
                        {subject.name}
                      </div>
                      <div className="text-xs text-gray-400">
                        {subject.code}
                      </div>
                    </div>
                  </div>
                </div>)}
              
              {getSubjectsByCategory(category.name).length === 0 && <div className="text-center py-4">
                  <div className="text-xs text-gray-500 mb-2">Belum ada mata pelajaran</div>
                  <Button size="sm" variant="outline" className="border-gray-600/50 text-gray-300 hover:bg-gradient-to-r hover:from-gray-700/80 hover:to-gray-600/80 hover:text-white backdrop-blur-sm">
                    <Plus className="h-3 w-3 mr-1" />
                    Tambah
                  </Button>
                </div>}
            </CardContent>
          </Card>)}
      </div>
    </div>;
};