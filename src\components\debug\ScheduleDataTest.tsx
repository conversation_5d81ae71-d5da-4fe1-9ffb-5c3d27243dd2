import React from 'react';
import { useSchedules } from '@/hooks/useSchedules';
import { useSchedulesSimple } from '@/hooks/useSchedulesSimple';

export const ScheduleDataTest: React.FC = () => {
  const { data: schedules, isLoading, error } = useSchedules();
  const { data: schedulesSimple } = useSchedulesSimple();

  if (isLoading) return <div className="p-4 text-white">Loading schedules...</div>;
  if (error) return <div className="p-4 text-red-500">Error: {error.message}</div>;

  // Analyze week distribution
  const weekDistribution = schedules?.reduce((acc: any, schedule: any) => {
    const week = schedule.academic_week;
    if (!acc[week]) acc[week] = 0;
    acc[week]++;
    return acc;
  }, {}) || {};

  const availableWeeks = Object.keys(weekDistribution).map(Number).sort((a, b) => a - b);
  const missingWeeks = [];
  for (let i = 1; i <= 24; i++) {
    if (!weekDistribution[i]) {
      missingWeeks.push(i);
    }
  }

  return (
    <div className="p-6 bg-gray-800 text-white rounded-lg">
      <h2 className="text-xl font-bold mb-4">🔍 Schedule Data Test</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Main Hook Data */}
        <div>
          <h3 className="text-lg font-semibold mb-2">useSchedules() Hook</h3>
          <div className="bg-gray-700 p-4 rounded">
            <p><strong>Total Records:</strong> {schedules?.length || 0}</p>
            <p><strong>Available Weeks:</strong> {availableWeeks.join(', ')}</p>
            <p><strong>Missing Weeks:</strong> {missingWeeks.length > 0 ? missingWeeks.join(', ') : 'None'}</p>
            <p><strong>Weeks 1-13:</strong> {availableWeeks.filter(w => w <= 13).length}/13</p>
            <p><strong>Weeks 14-24:</strong> {availableWeeks.filter(w => w >= 14).length}/11</p>
          </div>
        </div>

        {/* Simple Hook Data */}
        <div>
          <h3 className="text-lg font-semibold mb-2">useSchedulesSimple() Hook</h3>
          <div className="bg-gray-700 p-4 rounded">
            <p><strong>Total Records:</strong> {schedulesSimple?.length || 0}</p>
            <p><strong>Status:</strong> {schedulesSimple ? 'Success' : 'No Data'}</p>
          </div>
        </div>
      </div>

      {/* Week Distribution Details */}
      <div className="mt-6">
        <h3 className="text-lg font-semibold mb-2">Week Distribution</h3>
        <div className="bg-gray-700 p-4 rounded max-h-40 overflow-y-auto">
          <div className="grid grid-cols-6 gap-2 text-sm">
            {Array.from({ length: 24 }, (_, i) => i + 1).map(week => (
              <div 
                key={week}
                className={`p-2 rounded text-center ${
                  weekDistribution[week] 
                    ? 'bg-green-600 text-white' 
                    : 'bg-red-600 text-white'
                }`}
              >
                W{week}: {weekDistribution[week] || 0}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Sample Data */}
      <div className="mt-6">
        <h3 className="text-lg font-semibold mb-2">Sample Data</h3>
        <div className="bg-gray-700 p-4 rounded max-h-60 overflow-y-auto">
          <div className="space-y-2 text-sm">
            {/* Week 1 Sample */}
            {schedules?.filter(s => s.academic_week === 1).slice(0, 2).map(schedule => (
              <div key={schedule.id} className="bg-blue-600 p-2 rounded">
                <strong>Week 1:</strong> {schedule.subject_name} - {schedule.start_time} to {schedule.end_time}
              </div>
            ))}
            
            {/* Week 14 Sample */}
            {schedules?.filter(s => s.academic_week === 14).slice(0, 2).map(schedule => (
              <div key={schedule.id} className="bg-green-600 p-2 rounded">
                <strong>Week 14:</strong> {schedule.subject_name} - {schedule.start_time} to {schedule.end_time}
              </div>
            ))}
            
            {/* Week 24 Sample */}
            {schedules?.filter(s => s.academic_week === 24).slice(0, 2).map(schedule => (
              <div key={schedule.id} className="bg-purple-600 p-2 rounded">
                <strong>Week 24:</strong> {schedule.subject_name} - {schedule.start_time} to {schedule.end_time}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
