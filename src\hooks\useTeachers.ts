
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const useTeachers = () => {
  return useQuery({
    queryKey: ['teachers'],
    queryFn: async () => {
      console.log('🔍 Fetching teachers data...');

      try {
        // Get current user's school_id
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) {
          throw new Error('User not authenticated');
        }

        const { data: profile } = await supabase
          .from('profiles')
          .select('school_id')
          .eq('id', user.user.id)
          .single();

        if (!profile?.school_id) {
          throw new Error('School ID not found in profile');
        }

        const { data, error } = await supabase
          .from('teachers')
          .select('*')
          .eq('school_id', profile.school_id)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('❌ Error fetching teachers:', error);
          throw error;
        }

        console.log('✅ Teachers data fetched successfully:', data);
        return data || [];
      } catch (error) {
        console.error('❌ Exception in useTeachers:', error);
        throw error;
      }
    },
  });
};

export const useCreateTeacher = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (teacherData: any) => {
      // Get current user's school_id and active academic year
      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      const { data: activeYear } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile?.school_id)
        .eq('is_active', true)
        .single();

      const { data, error } = await supabase
        .from('teachers')
        .insert({
          ...teacherData,
          school_id: profile?.school_id,
          academic_year_id: activeYear?.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Berhasil",
        description: "Data guru berhasil ditambahkan",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menambahkan data guru: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateTeacher = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...teacherData }: any) => {
      const { data, error } = await supabase
        .from('teachers')
        .update(teacherData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Berhasil",
        description: "Data guru berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal memperbarui data guru: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteTeacher = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('teachers')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Berhasil",
        description: "Data guru berhasil dihapus",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menghapus data guru: " + error.message,
        variant: "destructive",
      });
    },
  });
};
