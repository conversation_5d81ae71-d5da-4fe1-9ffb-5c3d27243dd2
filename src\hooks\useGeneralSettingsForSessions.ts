
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useGeneralSettingsForSessions = () => {
  return useQuery({
    queryKey: ['general_settings_for_sessions'],
    queryFn: async () => {
      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      const { data, error } = await supabase
        .from('general_settings')
        .select('lesson_duration_minutes')
        .eq('school_id', profile?.school_id)
        .single();

      if (error) throw error;
      return data;
    },
  });
};
