
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';

export const useSchools = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['schools', profile?.school_id],
    queryFn: async () => {
      if (!profile?.school_id) return null;
      
      const { data, error } = await supabase
        .from('schools')
        .select('*')
        .eq('id', profile.school_id)
        .maybeSingle();
      
      if (error) throw error;
      return data;
    },
    enabled: !!profile?.school_id,
  });
};

export const useUpdateSchool = () => {
  const queryClient = useQueryClient();
  const { profile } = useAuth();
  
  return useMutation({
    mutationFn: async (schoolData: {
      name?: string;
      principal_name?: string;
      address?: string;
      phone?: string;
      email?: string;
      logo_url?: string;
    }) => {
      if (!profile?.school_id) throw new Error('No school ID found');
      
      const { data, error } = await supabase
        .from('schools')
        .update(schoolData)
        .eq('id', profile.school_id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schools'] });
      toast({
        title: "Berhasil",
        description: "Informasi sekolah berhasil diperbarui",
      });
    },
    onError: (error) => {
      console.error('Error updating school:', error);
      toast({
        title: "Error",
        description: "Gagal memperbarui informasi sekolah",
        variant: "destructive",
      });
    },
  });
};

export const useUploadSchoolLogo = () => {
  const queryClient = useQueryClient();
  const { profile } = useAuth();
  const updateSchool = useUpdateSchool();
  
  return useMutation({
    mutationFn: async (file: File) => {
      if (!profile?.school_id) throw new Error('No school ID found');
      
      // Create unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${profile.school_id}-${Date.now()}.${fileExt}`;
      
      // Upload file to storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('school-logos')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        });
      
      if (uploadError) throw uploadError;
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('school-logos')
        .getPublicUrl(fileName);
      
      // Update school record with logo URL
      await updateSchool.mutateAsync({ logo_url: publicUrl });
      
      return publicUrl;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schools'] });
    },
    onError: (error) => {
      console.error('Error uploading logo:', error);
      toast({
        title: "Error",
        description: "Gagal mengupload logo sekolah",
        variant: "destructive",
      });
    },
  });
};
