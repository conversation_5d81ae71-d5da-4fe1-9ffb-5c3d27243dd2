import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface JPProgress {
  subject_id: string;
  subject_name: string;
  subject_code?: string;
  subject_color?: string;
  class_id: string;
  class_name: string;
  target_jp: number; // From class_schedules.hours_per_year
  realisasi_jp: number; // From schedules_view (calculated)
  progress_percentage: number;
  category: string;
}

export const useJPProgress = (classId?: string) => {
  return useQuery({
    queryKey: ['jp-progress', classId],
    queryFn: async (): Promise<JPProgress[]> => {
      if (!classId) return [];

      // Get current user's school_id and active academic year
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', user.id)
        .single();

      if (!profile?.school_id) throw new Error('School not found');

      const { data: activeYear } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile.school_id)
        .eq('is_active', true)
        .single();

      if (!activeYear?.id) throw new Error('Active academic year not found');

      // Get target JP from class_schedules (unscheduled subjects)
      // Note: We'll get all academic years for now since data might be in different years
      const { data: targets, error: targetError } = await supabase
        .from('class_schedules')
        .select(`
          subject_id,
          hours_per_year,
          academic_year_id,
          schedule_subjects (
            id,
            name,
            code,
            color,
            session_categories (
              name
            )
          ),
          classes (
            id,
            name,
            level
          )
        `)
        .eq('class_id', classId)
        .eq('school_id', profile.school_id)
        .is('day_of_week', null); // Only unscheduled subjects (targets)

      if (targetError) {
        console.error('❌ useJPProgress error:', targetError);
        console.log('⚠️ Returning empty array to prevent errors');
        // Return empty array instead of throwing
        return [] as JPProgressData[];
      }

      // Get realisasi JP from schedules_view (scheduled subjects)
      // Note: We'll get all academic years for now since data might be in different years
      const { data: realisasi, error: realisasiError } = await supabase
        .from('schedules_view')
        .select(`
          subject_id,
          jp_realisasi,
          academic_year_id
        `)
        .eq('class_id', classId)
        .eq('school_id', profile.school_id);

      if (realisasiError) throw realisasiError;

      // Group targets by subject_id and take the one with highest hours_per_year (most recent/relevant)
      const targetsBySubject = targets?.reduce((acc, target) => {
        const existing = acc[target.subject_id];
        if (!existing || target.hours_per_year > existing.hours_per_year) {
          acc[target.subject_id] = target;
        }
        return acc;
      }, {} as Record<string, any>) || {};

      // Calculate progress for each subject
      const progressData: JPProgress[] = Object.values(targetsBySubject).map(target => {
        // Sum up all realisasi for this subject across all academic years
        const totalRealisasi = realisasi
          ?.filter(r => r.subject_id === target.subject_id)
          ?.reduce((sum, r) => sum + (parseFloat(r.jp_realisasi) || 0), 0) || 0;

        const targetJP = target.hours_per_year || 0;
        const progressPercentage = targetJP > 0 ? (totalRealisasi / targetJP) * 100 : 0;

        return {
          subject_id: target.subject_id,
          subject_name: target.schedule_subjects?.name || 'Unknown',
          subject_code: target.schedule_subjects?.code,
          subject_color: target.schedule_subjects?.color,
          class_id: classId,
          class_name: target.classes?.name || 'Unknown',
          target_jp: targetJP,
          realisasi_jp: Math.round(totalRealisasi * 100) / 100, // Round to 2 decimal places
          progress_percentage: Math.min(Math.round(progressPercentage * 100) / 100, 100), // Cap at 100%
          category: target.schedule_subjects?.session_categories?.name || 'Unknown'
        };
      });

      console.log('📊 JP Progress calculated:', {
        classId,
        totalSubjects: progressData.length,
        progressData: progressData.map(p => ({
          subject: p.subject_name,
          target: p.target_jp,
          realisasi: p.realisasi_jp,
          percentage: p.progress_percentage
        }))
      });

      console.log('📊 Raw targets data:', targets);
      console.log('📊 Raw realisasi data:', realisasi);

      return progressData;
    },
    enabled: !!classId,
    staleTime: 5000, // ✅ FIXED: Reduced from 30s to 5s for more responsive updates
    gcTime: 300000, // Keep in cache for 5 minutes
  });
};

// Hook untuk mendapatkan summary JP progress per kategori
export const useJPProgressByCategory = (classId?: string) => {
  const { data: jpProgress = [] } = useJPProgress(classId);

  const progressByCategory = jpProgress.reduce((acc, progress) => {
    const category = progress.category;
    if (!acc[category]) {
      acc[category] = {
        category,
        total_target: 0,
        total_realisasi: 0,
        subjects_count: 0,
        average_progress: 0
      };
    }

    acc[category].total_target += progress.target_jp;
    acc[category].total_realisasi += progress.realisasi_jp;
    acc[category].subjects_count += 1;

    return acc;
  }, {} as Record<string, {
    category: string;
    total_target: number;
    total_realisasi: number;
    subjects_count: number;
    average_progress: number;
  }>);

  // Calculate average progress for each category
  Object.values(progressByCategory).forEach(cat => {
    cat.average_progress = cat.total_target > 0 
      ? (cat.total_realisasi / cat.total_target) * 100 
      : 0;
  });

  return {
    data: progressByCategory,
    categories: Object.keys(progressByCategory),
    totalSubjects: jpProgress.length
  };
};
