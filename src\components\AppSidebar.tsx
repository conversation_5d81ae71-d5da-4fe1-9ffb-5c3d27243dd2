
import React, { useState, useEffect } from 'react';
import { Home, Database, GraduationCap, Users, BookOpen, Clock, Calendar, CalendarDays, Settings, School, User, ChevronDown, BarChart3, Link2, <PERSON>er, <PERSON><PERSON>he<PERSON>, Share2 } from 'lucide-react';
import { Sidebar, SidebarContent, SidebarFooter, SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useAuth } from '@/contexts/AuthContext';

interface AppSidebarProps {
  activeItem: string;
  onItemChange: (item: string) => void;
}

const AppSidebar: React.FC<AppSidebarProps> = ({
  activeItem,
  onItemChange
}) => {
  const { profile } = useAuth();

  // Auto-expand schedule menu if any schedule item is active
  const isScheduleActive = activeItem.startsWith('schedules');
  const [isScheduleExpanded, setIsScheduleExpanded] = useState(isScheduleActive);

  // Auto-expand event menu if any event item is active
  const isEventActive = activeItem.startsWith('events');
  const [isEventExpanded, setIsEventExpanded] = useState(isEventActive);

  // Update expanded state when active item changes
  useEffect(() => {
    console.log('🔍 AppSidebar useEffect - activeItem:', activeItem, 'isScheduleActive:', isScheduleActive);
    if (isScheduleActive) {
      setIsScheduleExpanded(true);
    }
    if (isEventActive) {
      setIsEventExpanded(true);
    }
  }, [isScheduleActive, isEventActive, activeItem]);

  const hasPermission = (requiredRoles: string[]) => {
    // Superadmin always has access to everything
    if (profile?.role === 'superadmin') {
      return true;
    }
    return profile?.role && requiredRoles.includes(profile.role);
  };

  // Dashboard is always accessible to all authenticated users
  const menuItems = [{
    id: 'dashboard',
    title: 'Dashboard',
    icon: Home,
    onClick: () => onItemChange('dashboard')
  }];

  const masterDataItems = [{
    id: 'classes',
    title: 'Kelas',
    icon: GraduationCap,
    onClick: () => onItemChange('classes'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }, {
    id: 'teachers',
    title: 'Guru',
    icon: Users,
    onClick: () => onItemChange('teachers'),
    permissions: ['superadmin', 'kepsek']
  }, {
    id: 'subjects',
    title: 'Mata Pelajaran',
    icon: BookOpen,
    onClick: () => onItemChange('subjects'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }, {
    id: 'time-sessions',
    title: 'Sesi dan Waktu',
    icon: Clock,
    onClick: () => onItemChange('time-sessions'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }];

  const scheduleSubItems = [{
    id: 'schedules',
    title: 'Jadwal',
    icon: Calendar,
    onClick: () => onItemChange('schedules'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }, {
    id: 'schedules-overview',
    title: 'Overview',
    icon: BarChart3,
    onClick: () => onItemChange('schedules-overview'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }, {
    id: 'schedules-sync',
    title: 'Sinkronisasi Kalender',
    icon: Link2,
    onClick: () => onItemChange('schedules-sync'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }, {
    id: 'schedules-print',
    title: 'Print',
    icon: Printer,
    onClick: () => onItemChange('schedules-print'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }];

  const eventSubItems = [{
    id: 'events',
    title: 'Kalender Event',
    icon: CalendarDays,
    onClick: () => onItemChange('events'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }, {
    id: 'events-print',
    title: 'Print Kaldik',
    icon: Printer,
    onClick: () => onItemChange('events-print'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }, {
    id: 'events-share',
    title: 'Share Event',
    icon: Share2,
    onClick: () => onItemChange('events-share'),
    permissions: ['superadmin', 'kepsek', 'kesiswaan']
  }];

  const settingsItems = [{
    id: 'general-settings',
    title: 'Pengaturan Umum',
    icon: Settings,
    onClick: () => onItemChange('general-settings'),
    permissions: ['superadmin', 'kepsek']
  }, {
    id: 'school-info',
    title: 'Info Sekolah',
    icon: School,
    onClick: () => onItemChange('school-info'),
    permissions: ['superadmin', 'kepsek']
  }];

  const filteredMasterData = masterDataItems.filter(item => hasPermission(item.permissions));
  const filteredScheduleSubItems = scheduleSubItems.filter(item => hasPermission(item.permissions));
  const filteredEventSubItems = eventSubItems.filter(item => hasPermission(item.permissions));
  const filteredSettingsData = settingsItems.filter(item => hasPermission(item.permissions));

  // Check if user has permission to see schedule menu
  const canViewSchedules = filteredScheduleSubItems.length > 0 || filteredEventSubItems.length > 0;

  return (
    <Sidebar className="bg-sidebar border-sidebar-border">
      <SidebarHeader className="p-6 bg-sidebar-accent">
        <div className="text-2xl font-bold text-sidebar-foreground tracking-tight">
          Indo<span className="text-lime-400">Jadwal</span>
        </div>
        <div className="text-sm text-sidebar-accent-foreground mt-2">
          {profile?.full_name} • <span className="text-lime-400 font-semibold">{profile?.role?.toUpperCase()}</span>
        </div>
        {profile?.school_id && (
          <div className="text-xs text-lime-400 mt-1">
            Sekolah Demo IndoJadwal
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="bg-sidebar-accent">
        {/* Main Navigation - Always visible */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map(item => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton 
                    onClick={item.onClick} 
                    isActive={activeItem === item.id} 
                    className="text-sidebar-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent data-[active=true]:bg-lime-400 data-[active=true]:text-gray-900"
                  >
                    <item.icon className="h-5 w-5" />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Master Data Section - Only show if user has permissions */}
        {filteredMasterData.length > 0 && (
          <SidebarGroup>
            <SidebarGroupLabel className="text-sidebar-accent-foreground text-xs uppercase tracking-wider">
              Master Data
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {filteredMasterData.map(item => (
                  <SidebarMenuItem key={item.id}>
                    <SidebarMenuButton 
                      onClick={item.onClick} 
                      isActive={activeItem === item.id} 
                      className="text-sidebar-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent data-[active=true]:bg-lime-400 data-[active=true]:text-gray-900"
                    >
                      <item.icon className="h-5 w-5" />
                      <span>{item.title}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Schedule Management Section - Only show if user has permissions */}
        {canViewSchedules && (
          <SidebarGroup>
            <SidebarGroupLabel className="text-sidebar-accent-foreground text-xs uppercase tracking-wider">
              Manajemen Jadwal
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {/* Expandable Jadwal Menu */}
                <Collapsible open={isScheduleExpanded || isScheduleActive} onOpenChange={setIsScheduleExpanded}>
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton
                        isActive={activeItem.startsWith('schedules')}
                        className="text-sidebar-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent data-[active=true]:bg-lime-400 data-[active=true]:text-gray-900"
                      >
                        <Calendar className="h-5 w-5" />
                        <span>Jadwal</span>
                        <ChevronDown className={`h-4 w-4 ml-auto transition-transform duration-200 ${isScheduleExpanded ? 'rotate-180' : ''}`} />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <div className="ml-4 space-y-1">
                        {filteredScheduleSubItems.map(item => (
                          <div
                            key={item.id}
                            onClick={() => {
                              console.log('🎯 Schedule submenu clicked:', {
                                itemId: item.id,
                                itemTitle: item.title,
                                currentActiveItem: activeItem,
                                currentPath: window.location.pathname,
                                isScheduleExpanded
                              });
                              onItemChange(item.id);
                            }}
                            className={`flex h-7 items-center gap-2 rounded-md px-2 cursor-pointer transition-all duration-200 ${
                              activeItem === item.id
                                ? 'bg-lime-400 text-gray-900'
                                : 'text-sidebar-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent'
                            }`}
                          >
                            <item.icon className={`h-4 w-4 ${
                              activeItem === item.id
                                ? 'text-gray-900'
                                : 'text-sidebar-foreground'
                            }`} />
                            <span>{item.title}</span>
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>

                {/* Event Menu - Collapsible */}
                <Collapsible open={isEventExpanded} onOpenChange={setIsEventExpanded}>
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton
                        isActive={isEventActive}
                        className="w-full text-sidebar-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-foreground"
                      >
                        <div className="flex items-center gap-3">
                          <CalendarDays className="h-5 w-5" />
                          <span>Event</span>
                        </div>
                        <ChevronDown className={`h-4 w-4 transition-transform ${isEventExpanded ? 'rotate-180' : ''}`} />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <div className="ml-4 space-y-1">
                        {filteredEventSubItems.map(item => (
                          <div
                            key={item.id}
                            onClick={() => onItemChange(item.id)}
                            className={`flex h-7 items-center gap-2 rounded-md px-2 cursor-pointer transition-all duration-200 ${activeItem === item.id
                                ? 'bg-lime-400 text-gray-900'
                                : 'text-sidebar-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent'
                              }`}
                          >
                            <item.icon className={`h-4 w-4 ${activeItem === item.id
                                ? 'text-gray-900'
                                : 'text-sidebar-foreground'
                              }`} />
                            <span>{item.title}</span>
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Settings Section - Only show if user has permissions */}
        {filteredSettingsData.length > 0 && (
          <SidebarGroup>
            <SidebarGroupLabel className="text-sidebar-accent-foreground text-xs uppercase tracking-wider">
              Pengaturan
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {filteredSettingsData.map(item => (
                  <SidebarMenuItem key={item.id}>
                    <SidebarMenuButton 
                      onClick={item.onClick} 
                      isActive={activeItem === item.id} 
                      className="text-sidebar-foreground hover:text-sidebar-foreground hover:bg-sidebar-accent data-[active=true]:bg-lime-400 data-[active=true]:text-gray-900"
                    >
                      <item.icon className="h-5 w-5" />
                      <span>{item.title}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>

      <SidebarFooter className="p-4 bg-sidebar-accent">
        <div className="flex items-center space-x-3 p-3 bg-sidebar/50 rounded-lg">
          <div className="w-8 h-8 bg-lime-400 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-gray-900" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-sidebar-foreground truncate">
              {profile?.full_name}
            </div>
            <div className="text-xs text-lime-400 capitalize font-semibold">
              {profile?.role}
            </div>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};

export default AppSidebar;
