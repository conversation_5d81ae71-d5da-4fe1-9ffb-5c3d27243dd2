
import { useTimeSessions } from '@/hooks/useTimeSessions';

export const useThirtyMinuteSlots = () => {
  const { data: timeSessions } = useTimeSessions();

  const generateThirtyMinuteSlots = () => {
    let startHour = 3; // Default fallback start
    let endHour = 22; // Default fallback end

    if (timeSessions?.length) {
      // Get earliest start time and latest end time from sessions
      const startTimes = timeSessions.map(session => {
        const time = session.start_time?.slice(0, 5) || '03:00';
        return parseInt(time.split(':')[0]);
      });
      
      const endTimes = timeSessions.map(session => {
        const time = session.end_time?.slice(0, 5) || '22:00';
        return parseInt(time.split(':')[0]);
      });

      startHour = Math.min(...startTimes);
      endHour = Math.max(...endTimes);
    }

    // Generate 30-minute slots from start to end + 1 hour
    const slots = [];
    for (let hour = startHour; hour <= endHour + 1; hour++) {
      // Add :00 slot
      const timeString00 = `${hour.toString().padStart(2, '0')}:00`;
      slots.push({
        id: `slot-${hour}-00`,
        time: timeString00,
        displayTime: timeString00,
        hour: hour,
        minute: 0
      });

      // Add :30 slot (except for the last hour to avoid going too far)
      if (hour <= endHour) {
        const timeString30 = `${hour.toString().padStart(2, '0')}:30`;
        slots.push({
          id: `slot-${hour}-30`,
          time: timeString30,
          displayTime: timeString30,
          hour: hour,
          minute: 30
        });
      }
    }

    return slots;
  };

  return generateThirtyMinuteSlots();
};
