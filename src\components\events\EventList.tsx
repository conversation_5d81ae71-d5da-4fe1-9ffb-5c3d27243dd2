
import React, { useState, useMemo } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Edit2, Trash2, ChevronUp, ChevronDown, Filter } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { id } from 'date-fns/locale';
import { Holiday } from '@/types/event';
import { useClasses } from '@/hooks/useClasses';
import { useEventCategories } from '@/hooks/useEventCategories';

interface EventListProps {
  holidays: Holiday[];
  onEdit: (holiday: Holiday) => void;
  onDelete: (id: string) => void;
  selectedClassId?: string;
  onClassFilterChange: (classId: string) => void;
}

type SortField = 'name' | 'start_date' | 'end_date' | 'category';
type SortDirection = 'asc' | 'desc';

export const EventList: React.FC<EventListProps> = ({
  holidays,
  onEdit,
  onDelete,
  selectedClassId = 'all',
  onClassFilterChange,
}) => {
  const [sortField, setSortField] = useState<SortField>('start_date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  
  const { data: classes = [] } = useClasses();
  const { categories } = useEventCategories();

  const filteredAndSortedHolidays = useMemo(() => {
    // Filter by class
    let filtered = holidays.filter(holiday => {
      if (selectedClassId === 'all') return true;
      if (!holiday.class_ids || holiday.class_ids.length === 0) return true; // All classes
      return holiday.class_ids.includes(selectedClassId);
    });

    // Sort
    filtered.sort((a, b) => {
      let aValue: string | Date;
      let bValue: string | Date;

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'start_date':
          aValue = new Date(a.start_date);
          bValue = new Date(b.start_date);
          break;
        case 'end_date':
          aValue = new Date(a.end_date);
          bValue = new Date(b.end_date);
          break;
        case 'category':
          const aCat = categories.find(cat => cat.id === a.category_id);
          const bCat = categories.find(cat => cat.id === b.category_id);
          aValue = aCat?.name.toLowerCase() || '';
          bValue = bCat?.name.toLowerCase() || '';
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [holidays, selectedClassId, sortField, sortDirection, categories]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? 
      <ChevronUp className="h-4 w-4" /> : 
      <ChevronDown className="h-4 w-4" />;
  };

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name || 'Unknown';
  };

  const getClassNames = (classIds?: string[]) => {
    if (!classIds || classIds.length === 0) return 'Semua Kelas';
    
    const classNames = classIds
      .map(id => {
        const cls = classes.find(c => c.id === id);
        return cls?.name || '';
      })
      .filter(Boolean);
    
    return classNames.length > 0 ? classNames.join(', ') : 'Semua Kelas';
  };

  return (
    <div className="space-y-4">
      {/* Filter Controls */}
      <div className="flex items-center gap-4 p-4 bg-card rounded-lg border">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-primary" />
          <span className="text-foreground">Filter Kelas:</span>
        </div>
        <Select value={selectedClassId} onValueChange={onClassFilterChange}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua Kelas</SelectItem>
            {classes.map(cls => (
              <SelectItem key={cls.id} value={cls.id}>
                {cls.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <span className="text-muted-foreground text-sm">
          Menampilkan {filteredAndSortedHolidays.length} dari {holidays.length} event
        </span>
      </div>

      {/* Event Table */}
      <div className="overflow-x-auto rounded-lg border bg-card">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center gap-1">
                  Nama Event
                  <SortIcon field="name" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('category')}
              >
                <div className="flex items-center gap-1">
                  Kategori
                  <SortIcon field="category" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('start_date')}
              >
                <div className="flex items-center gap-1">
                  Tanggal Mulai
                  <SortIcon field="start_date" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('end_date')}
              >
                <div className="flex items-center gap-1">
                  Tanggal Selesai
                  <SortIcon field="end_date" />
                </div>
              </TableHead>
              <TableHead>Kelas</TableHead>
              <TableHead>Keterangan</TableHead>
              <TableHead className="text-center">Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedHolidays.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center text-muted-foreground py-8">
                  Tidak ada event yang ditemukan
                </TableCell>
              </TableRow>
            ) : (
              filteredAndSortedHolidays.map(holiday => (
                <TableRow key={holiday.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium text-foreground">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: holiday.color }}
                      />
                      {holiday.name}
                      {holiday.is_national_holiday && (
                        <Badge variant="secondary" className="text-xs">
                          Nasional
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {getCategoryName(holiday.category_id)}
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {format(parseISO(holiday.start_date), 'dd MMM yyyy', { locale: id })}
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {format(parseISO(holiday.end_date), 'dd MMM yyyy', { locale: id })}
                  </TableCell>
                  <TableCell className="text-muted-foreground text-sm">
                    {getClassNames(holiday.class_ids)}
                  </TableCell>
                  <TableCell className="text-muted-foreground text-sm max-w-xs truncate">
                    {holiday.description || '-'}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-center gap-1">
                      <Button
                        size="icon"
                        variant="outline"
                        onClick={() => onEdit(holiday)}
                        className="h-8 w-8"
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="destructive"
                        onClick={() => onDelete(holiday.id)}
                        className="h-8 w-8"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
