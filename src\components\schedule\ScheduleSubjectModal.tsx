// =====================================================
// SCHEDULE SUBJECT MODAL
// =====================================================
// Modal untuk menambah mata pelajaran manual ke schedule_subjects

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { X, Plus } from 'lucide-react';
import { useCreateScheduleSubject } from '@/hooks/useScheduleSubjects';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useClasses } from '@/hooks/useClasses';
import { toast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { SCHEDULE_CATEGORY_COLORS } from '@/types/scheduleCategory';

interface ScheduleSubjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCategoryId?: string;
  selectedClassId?: string;
}

interface ScheduleSubjectFormData {
  name: string;
  code: string;
  color: string;
  total_hours_per_year: number;
  standard_duration: number;
  schedule_category_id: string; // This will map to session_category_id in backend
  selected_classes: string[];
}

export const ScheduleSubjectModal: React.FC<ScheduleSubjectModalProps> = ({
  isOpen,
  onClose,
  selectedCategoryId,
  selectedClassId
}) => {
  const queryClient = useQueryClient();
  
  // Hooks
  const { data: classes = [] } = useClasses();
  const { data: sessionCategories = [] } = useSessionCategories();
  const createSubjectMutation = useCreateScheduleSubject(false); // Disable auto toast

  // Get default category ID
  const getDefaultCategoryId = () => {
    if (selectedCategoryId && sessionCategories.length > 0) {
      return selectedCategoryId;
    }
    return sessionCategories[0]?.id || '';
  };

  // Get default color
  const getDefaultColor = () => {
    return SCHEDULE_CATEGORY_COLORS[0]; // Selalu gunakan warna pertama sebagai default
  };

  // Form state
  const [formData, setFormData] = useState<ScheduleSubjectFormData>({
    name: '',
    code: '',
    color: getDefaultColor(),
    total_hours_per_year: 0,
    standard_duration: 45,
    schedule_category_id: getDefaultCategoryId(),
    selected_classes: selectedClassId ? [selectedClassId] : []
  });

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        name: '',
        code: '',
        color: getDefaultColor(),
        total_hours_per_year: 0,
        standard_duration: 45,
        schedule_category_id: getDefaultCategoryId(),
        selected_classes: selectedClassId ? [selectedClassId] : []
      });
    }
  }, [isOpen, selectedCategoryId, selectedClassId, sessionCategories]);

  // Handle form submission
  const handleSubmit = async () => {
    console.log('🚀 === SCHEDULE SUBJECT MODAL SUBMIT ===');
    console.log('📝 Form data:', formData);

    // Validasi form
    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Nama mata pelajaran harus diisi",
        variant: "destructive"
      });
      return;
    }

    if (formData.name.trim().length < 2) {
      toast({
        title: "Error",
        description: "Nama mata pelajaran minimal 2 karakter",
        variant: "destructive"
      });
      return;
    }

    if (!formData.schedule_category_id) {
      toast({
        title: "Error",
        description: "Kategori harus dipilih",
        variant: "destructive"
      });
      return;
    }

    if (formData.selected_classes.length === 0) {
      toast({
        title: "Error",
        description: "Minimal pilih satu kelas",
        variant: "destructive"
      });
      return;
    }

    try {
      // Siapkan data untuk disimpan
      const subjectDataForSupabase = {
        name: formData.name.trim(),
        code: formData.code?.trim() || undefined,
        color: formData.color,
        total_hours_per_year: formData.total_hours_per_year !== undefined ? formData.total_hours_per_year : 0,
        standard_duration: formData.standard_duration || 45,
        schedule_category_id: formData.schedule_category_id,
        selected_classes: formData.selected_classes
      };

      console.log('📊 Subject data to be sent:', subjectDataForSupabase);
      console.log('🎨 Color being saved:', formData.color);

      // Simpan ke database
      const newSubject = await createSubjectMutation.mutateAsync(subjectDataForSupabase);
      console.log('✅ Schedule subject created successfully:', newSubject);

      // Refresh queries
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });

      // Notifikasi sukses
      toast({
        title: "Berhasil",
        description: `Mata pelajaran "${subjectDataForSupabase.name}" berhasil ditambahkan ke ${formData.selected_classes.length} kelas`
      });

      console.log('🎉 === SCHEDULE SUBJECT MODAL COMPLETED ===');
      onClose();

    } catch (error) {
      console.error('💥 === ERROR IN SCHEDULE SUBJECT MODAL ===');
      console.error('💥 Error details:', error);
      
      toast({
        title: "Gagal",
        description: `Gagal menambahkan mata pelajaran: ${error?.message || 'Terjadi kesalahan tidak dikenal'}`,
        variant: "destructive"
      });
    }
  };

  // Handle class selection
  const handleClassSelection = (classId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selected_classes: checked
        ? [...prev.selected_classes, classId]
        : prev.selected_classes.filter(id => id !== classId)
    }));
  };

  // Handle select all classes
  const handleSelectAllClasses = () => {
    const allClassIds = classes.map(cls => cls.id);
    setFormData(prev => ({
      ...prev,
      selected_classes: allClassIds
    }));
  };

  // Handle deselect all classes
  const handleDeselectAllClasses = () => {
    setFormData(prev => ({
      ...prev,
      selected_classes: []
    }));
  };

  // Check if all classes are selected
  const isAllClassesSelected = classes.length > 0 && formData.selected_classes.length === classes.length;

  // ✅ FIXED: Gunakan session_categories langsung
  const availableCategories = sessionCategories;

  // Debug logging
  console.log('🔍 ScheduleSubjectModal - Debug data:', {
    isOpen,
    sessionCategories: sessionCategories?.length || 0,
    availableCategories: availableCategories?.length || 0,
    categories: sessionCategories?.map(cat => ({
      id: cat.id,
      name: cat.name
    }))
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white flex items-center justify-between">
            <span>Tambah Mata Pelajaran Manual</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Nama Mata Pelajaran */}
          <div>
            <Label className="text-white">Nama Mata Pelajaran *</Label>
            <Input
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Masukkan nama mata pelajaran"
              className="bg-gray-800 border-gray-600 text-white"
            />
          </div>

          {/* Kode Mata Pelajaran */}
          <div>
            <Label className="text-white">Kode Mata Pelajaran</Label>
            <Input
              value={formData.code}
              onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
              placeholder="Masukkan kode mata pelajaran (opsional)"
              className="bg-gray-800 border-gray-600 text-white"
            />
          </div>

          {/* Kategori */}
          <div>
            <Label className="text-white">Kategori *</Label>
            <Select
              value={formData.schedule_category_id}
              onValueChange={(value: string) => setFormData(prev => ({ ...prev, schedule_category_id: value }))}
            >
              <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                <SelectValue placeholder="Pilih kategori" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {availableCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id} className="text-white hover:bg-gray-700">
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-sm" 
                        style={{ backgroundColor: category.color }}
                      />
                      <span>{category.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {availableCategories.length === 0 && (
              <p className="text-sm text-yellow-400 mt-1">
                Tidak ada kategori tersedia. Buat kategori baru terlebih dahulu di menu Sesi dan Waktu.
              </p>
            )}
          </div>

          {/* Warna */}
          <div>
            <Label className="text-white">Warna</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {SCHEDULE_CATEGORY_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, color }))}
                  className={`w-8 h-8 rounded-full border-2 ${
                    formData.color === color ? 'border-white' : 'border-gray-600'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* JP/Tahun */}
          <div>
            <Label className="text-white">JP/Tahun</Label>
            <Input
              type="number"
              value={formData.total_hours_per_year}
              onChange={(e) => setFormData(prev => ({ ...prev, total_hours_per_year: parseInt(e.target.value) || 0 }))}
              placeholder="0"
              className="bg-gray-800 border-gray-600 text-white"
            />
          </div>

          {/* Durasi Standar */}
          <div>
            <Label className="text-white">Durasi Standar (menit)</Label>
            <Input
              type="number"
              value={formData.standard_duration}
              onChange={(e) => setFormData(prev => ({ ...prev, standard_duration: parseInt(e.target.value) || 45 }))}
              placeholder="45"
              className="bg-gray-800 border-gray-600 text-white"
            />
          </div>

          {/* Pilih Kelas */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label className="text-white">Pilih Kelas *</Label>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={isAllClassesSelected ? handleDeselectAllClasses : handleSelectAllClasses}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white text-xs"
                >
                  {isAllClassesSelected ? (
                    <>
                      <X className="h-3 w-3 mr-1" />
                      Batal Semua
                    </>
                  ) : (
                    <>
                      <Plus className="h-3 w-3 mr-1" />
                      Pilih Semua
                    </>
                  )}
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border border-gray-600 rounded-md p-2 bg-gray-800">
              {classes.map((cls) => (
                <div key={cls.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={cls.id}
                    checked={formData.selected_classes.includes(cls.id)}
                    onCheckedChange={(checked) => handleClassSelection(cls.id, checked as boolean)}
                    className="border-gray-600 data-[state=checked]:bg-blue-600"
                  />
                  <Label htmlFor={cls.id} className="text-white text-sm cursor-pointer">
                    {cls.name}
                  </Label>
                </div>
              ))}
            </div>
            <div className="text-xs text-gray-400 mt-1">
              {formData.selected_classes.length} dari {classes.length} kelas dipilih
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              Batal
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={createSubjectMutation.isPending}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {createSubjectMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Simpan
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
