import { useMemo } from 'react';
import { useSchedulesComplete } from './useSchedulesPaginated';
import { useSubjects } from './useSubjects';
import { useSessionCategories } from './useSessionCategories';
import { useJPProgress } from './useJPProgress';
import { useJPProgressSimple } from './useJPProgressSimple';

export interface JPData {
  id: string;
  name: string;
  targetJP: number;        // Target dari database
  realizationJP: number;   // Realisasi dari schedule
  percentage: number;      // Progress percentage
  color: string;          // Warna kategori
  category: 'KBM' | 'Ekstrakurikuler' | 'Lainnya';
  type: 'subject' | 'category';
}

interface UseJPCalculationProps {
  selectedClassId?: string;
  selectedWeek?: number;
}

export const useJPCalculation = ({ selectedClassId, selectedWeek }: UseJPCalculationProps = {}) => {
  // 🚀 FIXED: Use paginated hook to get ALL data including weeks 14-24
  const { data: schedules = [], isLoading: schedulesLoading, error: schedulesError } = useSchedulesComplete();
  const { data: subjects = [] } = useSubjects();
  const { data: sessionCategories = [] } = useSessionCategories();
  const { data: jpProgress = [] } = useJPProgress(selectedClassId);
  const { data: jpProgressSimple = [] } = useJPProgressSimple(selectedClassId);

  const jpData = useMemo(() => {
    if (!selectedClassId) return [];

    console.log('🔄 useJPCalculation (PAGINATED) - Processing JP Progress:', {
      jpProgress: jpProgress.length,
      jpProgressSimple: jpProgressSimple.length,
      schedulesCount: schedules.length,
      selectedWeek,
      isLoading: schedulesLoading,
      error: schedulesError
    });

    // ✅ NEW: Use JP Progress Simple data (more reliable for testing)
    const jpResults: JPData[] = jpProgressSimple.map(progress => ({
      id: progress.subject_id,
      name: progress.subject_name,
      targetJP: progress.target_jp,
      realizationJP: progress.realisasi_jp,
      percentage: Math.round(progress.progress_percentage),
      color: progress.subject_color || '#8B5CF6',
      category: progress.category as any,
      type: 'subject' as const
    }));

    console.log('✅ useJPCalculation - Final JP Results:', jpResults);

    return jpResults;
  }, [jpProgressSimple, selectedClassId]);

  // Hitung total JP
  const totalJP = useMemo(() => {
    const totalTarget = jpData.reduce((sum, item) => sum + item.targetJP, 0);
    const totalRealization = jpData.reduce((sum, item) => sum + item.realizationJP, 0);
    const totalPercentage = totalTarget > 0 ? Math.round((totalRealization / totalTarget) * 100) : 0;

    return {
      targetJP: totalTarget,
      realizationJP: Math.round(totalRealization * 10) / 10,
      percentage: totalPercentage
    };
  }, [jpData]);

  return {
    jpData,
    totalJP,
    isLoading: false // Bisa ditambahkan loading state jika diperlukan
  };
};
