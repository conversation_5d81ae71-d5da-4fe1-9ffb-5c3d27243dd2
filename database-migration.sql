-- 🗄️ DATABASE MIGRATION SCRIPT UNTUK PRODUCTION
-- Jalankan script ini di Supabase production yang disediakan hosting

-- ============================================================================
-- 1. ENABLE EXTENSIONS
-- ============================================================================
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- 2. CREATE TABLES (sesuai urutan dependency)
-- ============================================================================

-- Academic Years
CREATE TABLE IF NOT EXISTS academic_years (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Academic Weeks
CREATE TABLE IF NOT EXISTS academic_weeks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    week_number INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Schools
CREATE TABLE IF NOT EXISTS schools (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Session Categories
CREATE TABLE IF NOT EXISTS session_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    color VARCHAR(7) DEFAULT '#6B7280',
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Classes
CREATE TABLE IF NOT EXISTS classes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    level VARCHAR(50),
    grade INTEGER,
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teachers
CREATE TABLE IF NOT EXISTS teachers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    full_name VARCHAR(255) NOT NULL,
    nip VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subjects
CREATE TABLE IF NOT EXISTS subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20),
    color VARCHAR(7) DEFAULT '#6B7280',
    curriculum_categories_id UUID REFERENCES session_categories(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Schedule Subjects (Mata Pelajaran per Kelas)
CREATE TABLE IF NOT EXISTS schedule_subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20),
    color VARCHAR(7) DEFAULT '#6B7280',
    class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
    subject_id UUID REFERENCES subjects(id),
    session_category_id UUID REFERENCES session_categories(id),
    jp_per_year INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Schedules (Jadwal Utama) - Optimized for 500k+ rows
CREATE TABLE IF NOT EXISTS schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    academic_year_id UUID REFERENCES academic_years(id) ON DELETE CASCADE,
    academic_week INTEGER NOT NULL,
    class_id UUID REFERENCES classes(id) ON DELETE CASCADE,
    subject_id UUID REFERENCES schedule_subjects(id) ON DELETE CASCADE,
    teacher_id UUID REFERENCES teachers(id),
    day_of_week INTEGER CHECK (day_of_week >= 1 AND day_of_week <= 7),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    room VARCHAR(100),
    notes TEXT,
    learning_objectives TEXT,
    learning_materials TEXT,
    teacher_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 3. CREATE INDEXES untuk Performance (Optimized for 500k+ rows)
-- ============================================================================
CREATE INDEX IF NOT EXISTS idx_schedules_class_week ON schedules(class_id, academic_week);
CREATE INDEX IF NOT EXISTS idx_schedules_day_time ON schedules(day_of_week, start_time);
CREATE INDEX IF NOT EXISTS idx_schedules_academic_year ON schedules(academic_year_id);
CREATE INDEX IF NOT EXISTS idx_schedules_subject_id ON schedules(subject_id);
CREATE INDEX IF NOT EXISTS idx_schedules_teacher_id ON schedules(teacher_id);
CREATE INDEX IF NOT EXISTS idx_academic_weeks_year ON academic_weeks(academic_year_id, week_number);

-- Additional performance indexes for large datasets
CREATE INDEX IF NOT EXISTS idx_schedules_composite ON schedules(class_id, academic_week, day_of_week, start_time);
CREATE INDEX IF NOT EXISTS idx_schedules_time_range ON schedules(start_time, end_time);

-- ============================================================================
-- 4. CREATE VIEW untuk Calendar
-- ============================================================================
CREATE OR REPLACE VIEW schedules_view AS
SELECT 
    cs.id,
    cs.academic_week,
    cs.academic_year_id,
    cs.class_id,
    cs.day_of_week,
    cs.start_time,
    cs.end_time,
    cs.room,
    cs.notes,
    cs.learning_objectives,
    cs.learning_materials,
    cs.teacher_notes,
    cs.subject_id,
    ss.name as subject_name,
    ss.code as subject_code,
    ss.color as subject_color,
    c.name as class_name,
    t.full_name as teacher_name,
    t.id as teacher_id,
    sc.name as session_category_name,
    sc.color as session_category_color,
    sc.id as session_category_id,
    cs.created_at
FROM schedules cs
LEFT JOIN schedule_subjects ss ON cs.subject_id = ss.id
LEFT JOIN classes c ON cs.class_id = c.id
LEFT JOIN teachers t ON cs.teacher_id = t.id
LEFT JOIN session_categories sc ON ss.session_category_id = sc.id;

-- ============================================================================
-- 5. ENABLE ROW LEVEL SECURITY (RLS)
-- ============================================================================
ALTER TABLE academic_years ENABLE ROW LEVEL SECURITY;
ALTER TABLE academic_weeks ENABLE ROW LEVEL SECURITY;
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE session_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedules ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 6. CREATE RLS POLICIES (Allow all for now, customize later)
-- ============================================================================
CREATE POLICY "Allow all operations" ON academic_years FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON academic_weeks FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON schools FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON session_categories FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON classes FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON teachers FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON subjects FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON schedule_subjects FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON schedules FOR ALL USING (true);

-- ============================================================================
-- 7. ENABLE REALTIME
-- ============================================================================
-- Jalankan di Supabase Dashboard:
-- Database > Replication > Enable untuk tabel: schedules

-- ============================================================================
-- SELESAI! 
-- Sekarang import data dari development database Anda
-- ============================================================================
