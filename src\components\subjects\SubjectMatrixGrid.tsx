
import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, BookOpen, Edit3, Trash2 } from 'lucide-react';

interface SubjectMatrixGridProps {
  subjects: any[];
  extracurriculars: any[];
  sortedClasses: any[];
  getSubjectColor: (index: number) => string;
  getClassSubject: (subjectId: string, classId: string) => any;
  handleAddSubjectToClass: (subject: any, classItem: any) => void;
  handleEditAssignment: (assignment: any, subjectName: string, className: string) => void;
  handleDeleteAssignment: (assignment: any) => void;
  createClassSubject: any;
  onAddSubject: () => void;
  onAddExtracurricular: () => void;
  onEditSubject: (subject: any) => void;
  onDeleteSubject: (subject: any) => void;
}

const SubjectMatrixGrid: React.FC<SubjectMatrixGridProps> = ({
  subjects,
  sortedClasses,
  getSubjectColor,
  getClassSubject,
  handleAddSubjectToClass,
  handleEditAssignment,
  handleDeleteAssignment,
  createClassSubject,
  onAddSubject,
  onEditSubject,
  onDeleteSubject
}) => {
  if (!subjects || subjects.length === 0) {
    return (
      <div className="p-8 text-center">
        <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-foreground mb-2">Belum ada mata pelajaran</h3>
        <p className="text-muted-foreground mb-4">Mulai dengan menambahkan mata pelajaran pertama</p>
        <Button onClick={onAddSubject} className="bg-lime-500 hover:bg-lime-600 text-gray-900 font-semibold">
          <Plus className="mr-2 h-4 w-4" />
          Tambah Mata Pelajaran
        </Button>
      </div>
    );
  }

  return (
    // Background mata pelajaran
    <div className="overflow-x-auto">
      <div className="min-w-max">
        {/* Header with class names */}
        <div className="grid grid-cols-[250px_repeat(auto-fit,minmax(120px,1fr))] gap-2 p-4">
          <div className="flex items-center justify-center bg-muted/20 border border-border rounded-lg p-3 shadow-sm">
            <div className="text-center">
              <BookOpen className="h-5 w-5 text-lime-500 mx-auto mb-1" />
              <div className="text-foreground font-medium text-sm">MATA PELAJARAN</div>
            </div>
          </div>
          {sortedClasses.map(classItem => (
            <div key={classItem.id} className="text-center bg-muted/10 border border-border rounded-lg p-3 py-[12px] px-0 shadow-sm">
              <div className="text-foreground font-medium text-sm">Kelas {classItem.level}</div>
              <div className="text-muted-foreground text-xs">{classItem.name}</div>
            </div>
          ))}
        </div>

        {/* Subject rows */}
        <div className="space-y-2 p-4">
          {subjects.map((subject) => {
            return (
              <div key={subject.id} className="grid grid-cols-[250px_repeat(auto-fit,minmax(120px,1fr))] gap-2 items-center">
                {/* Subject name with color-coded design and action buttons */}
                <div
                  className="group relative rounded-xl p-3 h-16 flex flex-col justify-center border-2"
                  style={{
                    backgroundColor: `${subject.color}20`,
                    borderColor: `${subject.color}40`
                  }}
                >
                  <div className="text-foreground text-sm font-medium mb-1">
                    {subject.name}
                  </div>
                  <div className="text-muted-foreground text-xs opacity-70">
                    {subject.code}
                  </div>

                  {/* Action buttons - appear on hover, contained within box */}
                  <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditSubject(subject);
                      }}
                      className="h-5 w-5 p-0 hover:bg-accent/20 text-muted-foreground hover:text-foreground rounded-sm"
                    >
                      <Edit3 className="h-2.5 w-2.5" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteSubject(subject);
                      }}
                      className="h-5 w-5 p-0 hover:bg-red-500/20 text-muted-foreground hover:text-red-400 rounded-sm"
                    >
                      <Trash2 className="h-2.5 w-2.5" />
                    </Button>
                  </div>
                </div>

                {/* Class assignments */}
                {sortedClasses.map(classItem => {
                  const assignment = getClassSubject(subject.id, classItem.id);
                  return (
                    <div key={classItem.id} className="flex justify-center">
                      {assignment ? (
                        <div className="group relative h-16 w-full">
                          <div
                            className="p-3 h-16 w-full flex flex-col justify-center items-center cursor-pointer hover:opacity-80 transition-opacity rounded-lg border-2"
                            style={{
                              backgroundColor: `${subject.color}20`,
                              borderColor: `${subject.color}60`
                            }}
                          >
                            <div className="text-foreground text-sm font-medium">
                              {subject.name}
                            </div>
                            <div className="text-muted-foreground opacity-70 text-xs mt-1">
                              {assignment.hours_per_year || assignment.hours_per_week * 36} JP/Tahun
                            </div>
                          </div>
                          <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1">
                            <Button
                              size="sm"
                              onClick={() => handleEditAssignment(assignment, subject.name, `${classItem.level} ${classItem.name}`)}
                              className="h-5 w-5 p-0 bg-blue-600/80 hover:bg-blue-700 rounded-sm"
                            >
                              <Edit3 className="w-2.5 h-2.5" />
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleDeleteAssignment(assignment)}
                              className="h-5 w-5 p-0 bg-red-600/80 hover:bg-red-700 rounded-sm"
                            >
                              <Trash2 className="w-2.5 h-2.5" />
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <Button
                          variant="ghost"
                          onClick={() => handleAddSubjectToClass(subject, classItem)}
                          disabled={createClassSubject.isPending}
                          className="h-16 w-full text-primary hover:bg-primary/10 border border-dashed border-primary/30 flex flex-col items-center justify-center rounded-lg my-0 bg-muted/5"
                        >
                          <Plus className="h-4 w-4 mb-1" />
                          <span className="text-xs">Tambah</span>
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default SubjectMatrixGrid;
