
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useAcademicYears } from '@/hooks/useAcademicYears';

export const useSessionCategories = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useQuery({
    queryKey: ['session_categories', profile?.school_id, activeYear?.id],
    queryFn: async () => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      console.log('Fetching session categories for:', {
        school_id: profile.school_id,
        academic_year_id: activeYear.id
      });

      // Get session categories for active academic year
      const { data, error } = await supabase
        .from('session_categories')
        .select('*')
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeYear.id)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching session categories:', error);
        throw error;
      }

      console.log('Session Categories fetched:', {
        academicYear: activeYear.id,
        totalCategories: data?.length || 0,
        categories: data?.map(cat => ({ name: cat.name, color: cat.color }))
      });

      return data || [];
    },
    enabled: !!profile?.school_id && !!activeYear?.id,
    staleTime: 0,
    gcTime: 0,
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

export const useCreateSessionCategory = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useMutation({
    mutationFn: async (categoryData: any) => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      const { data, error } = await supabase
        .from('session_categories')
        .insert({
          name: categoryData.name,
          description: categoryData.description,
          color: categoryData.color || '#6B7280',
          school_id: profile.school_id,
          academic_year_id: activeYear.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['session_categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori sesi berhasil ditambahkan",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menambahkan kategori sesi: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateSessionCategory = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...categoryData }: any) => {
      const { data, error } = await supabase
        .from('session_categories')
        .update(categoryData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['session_categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori sesi berhasil diperbarui",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal memperbarui kategori sesi: " + error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteSessionCategory = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('session_categories')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['session_categories'] });
      toast({
        title: "Berhasil",
        description: "Kategori sesi berhasil dihapus",
      });
    },
    onError: (error) => {
      toast({
        title: "Gagal",
        description: "Gagal menghapus kategori sesi: " + error.message,
        variant: "destructive",
      });
    },
  });
};
