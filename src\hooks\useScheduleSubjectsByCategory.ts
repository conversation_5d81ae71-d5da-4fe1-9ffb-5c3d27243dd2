import { useQuery } from '@tanstack/react-query';
import { useSessionCategories } from './useSessionCategories';
import { useScheduleSubjects, useScheduleClassSubjects } from './useScheduleSubjects';
import { useSubjects } from './useSubjects';
import { useExtracurriculars } from './useExtracurriculars';
import { useClassSubjects } from './useClassSubjects';
import { useExtracurricularClasses } from './useExtracurricularClasses';
import { useJPProgressSimple } from './useJPProgressSimple';
import { calculateDurationMinutes, calculateJP } from '@/utils/timeCalculations';

export interface ScheduleSubjectItem {
  id: string;
  name: string;
  code?: string;
  color: string;
  total_hours_per_year: number;
  category: string;
  type: 'mata_pelajaran' | 'ekstrakurikuler' | 'schedule_subject';
  source: string;
  session_category_id?: string; // ✅ ADDED: Property untuk session category ID
  // ✅ NEW: JP Progress data yang akurat
  target_jp?: number;
  realisasi_jp?: number;
  progress_percentage?: number;
}

export interface CategoryWithSubjects {
  id: string;
  name: string;
  color: string;
  description?: string;
  is_system_category: boolean;
  subjects: ScheduleSubjectItem[];
}

// Hook untuk mendapatkan subjects berdasarkan kategori dari session_categories
export const useScheduleSubjectsByCategory = (selectedClassId?: string | null) => {
  // ✅ DEBUG: Log input parameters
  console.log('🚀 useScheduleSubjectsByCategory called with:', {
    selectedClassId,
    type: typeof selectedClassId,
    isNull: selectedClassId === null,
    isUndefined: selectedClassId === undefined,
    isEmpty: selectedClassId === ''
  });

  const { data: sessionCategories = [] } = useSessionCategories();
  const { data: scheduleSubjects = [] } = useScheduleSubjects();
  const { data: scheduleClassSubjects = [] } = useScheduleClassSubjects();
  const { data: mataPelajaran = [] } = useSubjects();
  const { data: ekstrakurikuler = [] } = useExtracurriculars();
  const { data: classSubjects = [] } = useClassSubjects();
  const { data: ekstrakurikulerClasses = [] } = useExtracurricularClasses();

  // ✅ NEW: Gunakan data JP yang akurat dari hook yang sama dengan tombol "JP dalam periode"
  // Convert null to undefined for useJPProgressSimple
  const classIdForJP = selectedClassId || undefined;
  const { data: jpProgressData = [], isLoading: jpLoading, error: jpError } = useJPProgressSimple(classIdForJP);

  // ✅ DEBUG: Log JP Progress data
  console.log('🔍 useScheduleSubjectsByCategory - JP Progress Data:', {
    selectedClassId,
    classIdForJP,
    jpProgressData: jpProgressData.length,
    jpLoading,
    jpError,
    jpData: jpProgressData
  });

  return useQuery({
    queryKey: [
      'schedule-subjects-by-category',
      selectedClassId,
      sessionCategories,
      scheduleSubjects,
      scheduleClassSubjects,
      mataPelajaran,
      ekstrakurikuler,
      classSubjects,
      ekstrakurikulerClasses,
      jpProgressData, // ✅ NEW: Include JP data in cache key
    ],
    queryFn: (): CategoryWithSubjects[] => {
      console.log('🔄 Processing schedule subjects by category with JP data...');
      console.log('📊 Data summary:', {
        sessionCategories: sessionCategories.length,
        scheduleSubjects: scheduleSubjects.length,
        mataPelajaran: mataPelajaran.length,
        ekstrakurikuler: ekstrakurikuler.length,
        jpProgressData: jpProgressData.length,
        selectedClassId
      });

      // ✅ NEW: Helper function untuk mendapatkan JP data yang akurat
      const getJPDataForSubject = (subjectId: string) => {
        const jpData = jpProgressData.find(jp => jp.subject_id === subjectId);
        const result = {
          target_jp: jpData?.target_jp || 0,
          realisasi_jp: jpData?.realisasi_jp || 0,
          progress_percentage: jpData?.progress_percentage || 0
        };

        // ✅ DEBUG: Log JP data lookup
        console.log(`🔍 JP Data lookup for subject ${subjectId}:`, {
          found: !!jpData,
          jpData,
          result
        });

        return result;
      };

      const categoriesWithSubjects: CategoryWithSubjects[] = [];

      // ✅ PERBAIKAN: Gunakan session_categories sebagai struktur sidebar
      sessionCategories.forEach(sessionCategory => {
        console.log(`🔍 Processing session category: ${sessionCategory.name} (${sessionCategory.id})`);
        const categorySubjects: ScheduleSubjectItem[] = [];

        // ✅ PERBAIKAN: Ambil dari schedule_subjects table yang memiliki session_category_id yang sesuai
        const scheduleSubjectsForCategory = scheduleSubjects.filter(scheduleSubject =>
          scheduleSubject.session_category_id === sessionCategory.id
        );

        // ✅ TAMBAHAN: Juga ambil dari subjects table untuk backward compatibility
        const subjectsForCategory = mataPelajaran.filter(subject =>
          subject.session_categories_id === sessionCategory.id
        );

        console.log(`📊 Data for category ${sessionCategory.name}:`, {
          scheduleSubjectsCount: scheduleSubjectsForCategory.length,
          subjectsCount: subjectsForCategory.length,
          scheduleSubjects: scheduleSubjectsForCategory.map(s => s.name),
          subjects: subjectsForCategory.map(s => s.name)
        });

        // ✅ PRIORITAS 1: Proses schedule_subjects terlebih dahulu (dari modal +Mapel)
        scheduleSubjectsForCategory.forEach(scheduleSubject => {
          if (selectedClassId) {
            // Filter berdasarkan kelas yang dipilih untuk schedule_subjects
            const isAssignedToClass = scheduleClassSubjects.some(scs =>
              scs.schedule_subject_id === scheduleSubject.id && scs.class_id === selectedClassId
            );

            if (isAssignedToClass) {
              // ✅ NEW: Dapatkan JP data yang akurat
              const jpData = getJPDataForSubject(scheduleSubject.id);

              const subjectWithJP = {
                id: scheduleSubject.id,
                name: scheduleSubject.name,
                code: scheduleSubject.code,
                color: scheduleSubject.color,
                total_hours_per_year: scheduleSubject.total_hours_per_year || 0,
                category: sessionCategory.name,
                type: 'schedule_subject',
                source: 'schedule_subjects',
                session_category_id: scheduleSubject.session_category_id,
                // ✅ NEW: JP data yang akurat dari perhitungan yang sama dengan tombol JP
                target_jp: jpData.target_jp,
                realisasi_jp: jpData.realisasi_jp,
                progress_percentage: jpData.progress_percentage
              };

              // ✅ DEBUG: Log subject dengan JP data
              console.log(`✅ Adding subject "${scheduleSubject.name}" with JP data:`, subjectWithJP);

              categorySubjects.push(subjectWithJP);
            }
          } else {
            // Jika tidak ada kelas dipilih, tampilkan semua schedule_subjects
            const jpData = getJPDataForSubject(scheduleSubject.id);

            categorySubjects.push({
              id: scheduleSubject.id,
              name: scheduleSubject.name,
              code: scheduleSubject.code,
              color: scheduleSubject.color,
              total_hours_per_year: scheduleSubject.total_hours_per_year || 0,
              category: sessionCategory.name,
              type: 'schedule_subject',
              source: 'schedule_subjects',
              session_category_id: scheduleSubject.session_category_id,
              // ✅ NEW: JP data yang akurat
              target_jp: jpData.target_jp,
              realisasi_jp: jpData.realisasi_jp,
              progress_percentage: jpData.progress_percentage
            });
          }
        });

        // ✅ PRIORITAS 2: Proses subjects table untuk backward compatibility
        subjectsForCategory.forEach(subject => {
          if (selectedClassId) {
            // Filter berdasarkan kelas yang dipilih
            const isAssignedToClass = classSubjects.some(cs =>
              cs.subject_id === subject.id && cs.class_id === selectedClassId
            );

            if (isAssignedToClass) {
              // ✅ NEW: Dapatkan JP data yang akurat
              const jpData = getJPDataForSubject(subject.id);

              categorySubjects.push({
                id: subject.id,
                name: subject.name,
                code: subject.code,
                color: subject.color,
                total_hours_per_year: subject.total_hours_per_year || 0,
                category: sessionCategory.name,
                type: 'mata_pelajaran',
                source: 'mata_pelajaran',
                session_category_id: subject.session_categories_id,
                // ✅ NEW: JP data yang akurat
                target_jp: jpData.target_jp,
                realisasi_jp: jpData.realisasi_jp,
                progress_percentage: jpData.progress_percentage
              });
            }
          } else {
            // Jika tidak ada kelas dipilih, tampilkan semua
            const jpData = getJPDataForSubject(subject.id);

            categorySubjects.push({
              id: subject.id,
              name: subject.name,
              code: subject.code,
              color: subject.color,
              total_hours_per_year: subject.total_hours_per_year || 0,
              category: sessionCategory.name,
              type: 'mata_pelajaran',
              source: 'mata_pelajaran',
              session_category_id: subject.session_categories_id,
              // ✅ NEW: JP data yang akurat
              target_jp: jpData.target_jp,
              realisasi_jp: jpData.realisasi_jp,
              progress_percentage: jpData.progress_percentage
            });
          }
        });

        // ✅ KHUSUS EKSKUL: Juga ambil dari ekstrakurikuler table untuk backward compatibility
        if (sessionCategory.name === 'Ekskul') {
          console.log('🎭 Processing Ekskul from ekstrakurikuler table');

          ekstrakurikuler.forEach(activity => {
            if (selectedClassId) {
              // Filter berdasarkan kelas yang dipilih
              const isAssignedToClass = ekstrakurikulerClasses.some(ec =>
                ec.extracurricular_id === activity.id && ec.class_id === selectedClassId
              );

              if (isAssignedToClass) {
                // ✅ NEW: Dapatkan JP data yang akurat untuk ekstrakurikuler
                const jpData = getJPDataForSubject(activity.id);

                categorySubjects.push({
                  id: activity.id,
                  name: activity.name,
                  color: activity.color,
                  total_hours_per_year: 0, // Ekstrakurikuler menggunakan JP system
                  category: sessionCategory.name,
                  type: 'ekstrakurikuler',
                  source: 'ekstrakurikuler',
                  session_category_id: sessionCategory.id,
                  // ✅ NEW: JP data yang akurat untuk ekstrakurikuler
                  target_jp: jpData.target_jp,
                  realisasi_jp: jpData.realisasi_jp,
                  progress_percentage: jpData.progress_percentage
                });
              }
            } else {
              // Jika tidak ada kelas dipilih, tampilkan semua
              const jpData = getJPDataForSubject(activity.id);

              categorySubjects.push({
                id: activity.id,
                name: activity.name,
                color: activity.color,
                total_hours_per_year: 0, // Ekstrakurikuler menggunakan JP system
                category: sessionCategory.name,
                type: 'ekstrakurikuler',
                source: 'ekstrakurikuler',
                session_category_id: sessionCategory.id,
                // ✅ NEW: JP data yang akurat untuk ekstrakurikuler
                target_jp: jpData.target_jp,
                realisasi_jp: jpData.realisasi_jp,
                progress_percentage: jpData.progress_percentage
              });
            }
          });
        }

        console.log(`✅ Session Category ${sessionCategory.name} processed with ${categorySubjects.length} subjects`);

        // ✅ Tambahkan kategori ke hasil (bahkan jika kosong untuk memungkinkan penambahan manual)
        categoriesWithSubjects.push({
          id: sessionCategory.id,
          name: sessionCategory.name,
          color: sessionCategory.color,
          description: sessionCategory.description,
          is_system_category: false, // session_categories tidak punya field ini
          subjects: categorySubjects
        });
      });

      console.log('🎉 Final categories with subjects:', {
        totalCategories: categoriesWithSubjects.length,
        categoriesWithData: categoriesWithSubjects.filter(cat => cat.subjects.length > 0).length,
        totalSubjects: categoriesWithSubjects.reduce((sum, cat) => sum + cat.subjects.length, 0),
        categories: categoriesWithSubjects.map(cat => ({
          name: cat.name,
          is_system: cat.is_system_category,
          subjectCount: cat.subjects.length,
          subjects: cat.subjects.map(s => ({ name: s.name, type: s.type, source: s.source }))
        }))
      });

      return categoriesWithSubjects;
    },
    enabled: !!sessionCategories &&
             !!scheduleSubjects &&
             !!scheduleClassSubjects &&
             !!mataPelajaran &&
             !!ekstrakurikuler &&
             !!classSubjects &&
             !!ekstrakurikulerClasses &&
             (!jpLoading), // ✅ FIXED: Wait for JP data to finish loading
    // ✅ ENHANCED: More aggressive real-time updates
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache data
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

// Hook untuk mendapatkan subjects yang bisa ditambahkan ke kelas tertentu
export const useAvailableSubjectsForClass = (classId: string) => {
  const { data: scheduleSubjects = [] } = useScheduleSubjects();
  const { data: scheduleClassSubjects = [] } = useScheduleClassSubjects();

  return useQuery({
    queryKey: ['available-subjects-for-class', classId, scheduleSubjects, scheduleClassSubjects],
    queryFn: () => {
      // Get subjects already assigned to this class
      const assignedSubjectIds = scheduleClassSubjects
        .filter(cs => cs.class_id === classId)
        .map(cs => cs.schedule_subject_id);

      // Return subjects not yet assigned to this class
      return scheduleSubjects.filter(subject =>
        !assignedSubjectIds.includes(subject.id)
      );
    },
    enabled: !!classId && scheduleSubjects.length > 0 && scheduleClassSubjects.length > 0,
  });
};

// Hook untuk mendapatkan subjects yang sudah ditambahkan ke kelas tertentu
export const useAssignedSubjectsForClass = (classId: string) => {
  const { data: scheduleClassSubjects = [] } = useScheduleClassSubjects();

  return useQuery({
    queryKey: ['assigned-subjects-for-class', classId, scheduleClassSubjects],
    queryFn: () => {
      return scheduleClassSubjects.filter(cs => cs.class_id === classId);
    },
    enabled: !!classId && scheduleClassSubjects.length > 0,
  });
};
