# 📋 Event Copy Feature Documentation

## 🎯 **Overview**
Fitur copy box pada event calendar yang memungkinkan pengguna untuk menyalin jadwal dengan cara drag horizontal (ke kanan atau ke kiri) pada event box.

## ✨ **Features**

### **Copy Mechanism:**
- **Drag ke Kanan**: Menyalin event ke hari berikutnya
- **Drag ke Kiri**: Menyalin event ke hari sebelumnya
- **Threshold**: 50 pixels horizontal movement untuk trigger copy
- **Smart Detection**: Membedakan antara resize (vertical) dan copy (horizontal)

### **Copy Behavior:**
- ✅ Mempertahankan waktu start dan end yang sama
- ✅ Mempertahankan semua data event (subject, teacher, room, notes)
- ✅ Otomatis adjust day_of_week (1=Monday, 7=Sunday)
- ✅ Handle week boundaries (Sunday → Monday, Monday → Sunday)
- ✅ Conflict detection sebelum copy
- ✅ Auto-save ke database

## 🔧 **Implementation Details**

### **1. State Variables**
```typescript
// Track copy state
let isActivelyCopying = false;
let hasCopied = false;
let startMouseX = 0;
let startMouseY_copy = 0;
let copyThreshold = 50; // pixels to trigger copy
```

### **2. Copy Detection**
```typescript
const startCopy = (e: MouseEvent) => {
  const resizeHandle = eventEl.querySelector('.fc-event-resizer');
  const rect = eventEl.getBoundingClientRect();
  const clickY = e.clientY;
  const bottomEdge = rect.bottom;

  // Check if click is NOT on resize handle AND NOT within 8px of bottom edge
  const isOnResizeHandle = resizeHandle && resizeHandle.contains(e.target as Node);
  const isOnBottomEdge = Math.abs(clickY - bottomEdge) <= 8;

  if (isOnResizeHandle || isOnBottomEdge) {
    return; // Let resize handle this
  }

  // Start copy detection
  isActivelyCopying = true;
  hasCopied = false;
  startMouseX = e.clientX;
  startMouseY_copy = e.clientY;
};
```

### **3. Movement Detection**
```typescript
const updateCopyMovement = (e: MouseEvent) => {
  if (!isActivelyCopying) return;

  const deltaX = Math.abs(e.clientX - startMouseX);
  const deltaY = Math.abs(e.clientY - startMouseY_copy);

  // Check if horizontal movement exceeds threshold and is more horizontal than vertical
  if (deltaX > copyThreshold && deltaX > deltaY) {
    hasCopied = true;
    isActivelyCopying = false;

    // Determine copy direction
    const direction = e.clientX > startMouseX ? 'right' : 'left';
    
    // Create copy of the event
    createEventCopy(info, direction);
  }
};
```

### **4. Copy Creation**
```typescript
const createEventCopy = async (eventInfo: any, direction: 'left' | 'right') => {
  const schedule = eventInfo.event.extendedProps.schedule;
  if (!schedule) return;

  try {
    // Calculate new day (left = previous day, right = next day)
    const currentDay = schedule.day_of_week;
    let newDay = direction === 'right' ? currentDay + 1 : currentDay - 1;
    
    // Handle week boundaries (1=Monday, 7=Sunday)
    if (newDay > 7) newDay = 1;
    if (newDay < 1) newDay = 7;

    // Create copy data
    const copyData = {
      subject_id: schedule.subject_id,
      class_id: schedule.class_id,
      day_of_week: newDay,
      start_time: schedule.start_time,
      end_time: schedule.end_time,
      academic_week: schedule.academic_week,
      schedule_date: schedule.schedule_date,
      teacher_id: schedule.teacher_id,
      room: schedule.room,
      notes: schedule.notes
    };

    // Check for conflicts
    if (hasTimeConflict(schedules || [], copyData)) {
      toast({
        title: "⚠️ Konflik Jadwal",
        description: `Tidak dapat menyalin ke ${direction === 'right' ? 'kanan' : 'kiri'} - jadwal bertabrakan`,
        variant: "destructive",
      });
      return;
    }

    await createScheduleMutation.mutateAsync(copyData);
    
    toast({
      title: "✅ Berhasil",
      description: `Jadwal berhasil disalin ke ${direction === 'right' ? 'kanan' : 'kiri'}`,
    });

  } catch (error) {
    console.error('❌ Error copying event:', error);
    toast({
      title: "❌ Gagal",
      description: "Gagal menyalin jadwal",
      variant: "destructive",
    });
  }
};
```

## 🎮 **User Experience**

### **How to Use:**
1. **Hover** pada event box di calendar
2. **Click dan drag** ke kanan atau ke kiri (bukan di area resize bottom)
3. **Drag minimal 50px** horizontal untuk trigger copy
4. **Release** mouse untuk execute copy
5. **Notification** akan muncul untuk konfirmasi hasil

### **Visual Feedback:**
- ✅ Console logging untuk debug
- ✅ Toast notifications untuk user feedback
- ✅ Error handling dengan pesan yang jelas
- ✅ Conflict detection dengan warning

### **Smart Behavior:**
- ✅ **Resize Priority**: Area bottom 8px tetap untuk resize
- ✅ **Copy Area**: Area tengah dan atas untuk copy
- ✅ **Direction Detection**: Otomatis detect arah drag
- ✅ **Threshold**: Mencegah accidental copy dengan movement kecil

## 🔄 **Day Mapping**

### **Day of Week Values:**
- 1 = Monday (Senin)
- 2 = Tuesday (Selasa)  
- 3 = Wednesday (Rabu)
- 4 = Thursday (Kamis)
- 5 = Friday (Jumat)
- 6 = Saturday (Sabtu)
- 7 = Sunday (Minggu)

### **Copy Direction Logic:**
```typescript
// Right = Next Day
if (direction === 'right') {
  newDay = currentDay + 1;
  if (newDay > 7) newDay = 1; // Sunday → Monday
}

// Left = Previous Day  
if (direction === 'left') {
  newDay = currentDay - 1;
  if (newDay < 1) newDay = 7; // Monday → Sunday
}
```

## 🛡️ **Error Handling**

### **Conflict Detection:**
- Menggunakan `hasTimeConflict()` function
- Check overlap dengan jadwal existing
- Prevent double booking pada waktu yang sama

### **Error Scenarios:**
1. **Time Conflict**: Jadwal bertabrakan dengan existing schedule
2. **Database Error**: Gagal save ke database
3. **Invalid Data**: Schedule data tidak valid
4. **Network Error**: Connection issues

### **User Feedback:**
- ✅ Success toast: "Jadwal berhasil disalin ke kanan/kiri"
- ⚠️ Warning toast: "Tidak dapat menyalin - jadwal bertabrakan"
- ❌ Error toast: "Gagal menyalin jadwal"

## 🧪 **Testing Checklist**

### **Functional Testing:**
- [ ] Copy ke kanan (next day) works
- [ ] Copy ke kiri (previous day) works  
- [ ] Week boundary handling (Sunday ↔ Monday)
- [ ] Conflict detection prevents invalid copies
- [ ] All schedule data preserved in copy
- [ ] Database save successful

### **UX Testing:**
- [ ] 50px threshold prevents accidental copy
- [ ] Resize area (bottom 8px) still works for resize
- [ ] Copy area (middle/top) triggers copy
- [ ] Visual feedback (toasts) appear correctly
- [ ] No interference with existing drag/drop

### **Edge Cases:**
- [ ] Copy from Sunday to Monday
- [ ] Copy from Monday to Sunday  
- [ ] Multiple rapid copy attempts
- [ ] Copy during network issues
- [ ] Copy with invalid schedule data

## 📝 **Files Modified**

### **src/components/schedule/ScheduleCalendar.tsx**
- ✅ Added copy state variables
- ✅ Added `startCopy()` function
- ✅ Added `updateCopyMovement()` function  
- ✅ Added `createEventCopy()` function
- ✅ Added `finishCopy()` function
- ✅ Updated event listeners for combined resize/copy
- ✅ Updated cleanup functions

## 🚀 **Future Enhancements**

### **Potential Improvements:**
1. **Visual Drag Preview**: Show ghost copy during drag
2. **Multi-day Copy**: Copy to multiple days at once
3. **Copy with Offset**: Copy with time adjustment
4. **Batch Copy**: Copy multiple events together
5. **Undo/Redo**: Ability to undo copy operations

### **Advanced Features:**
1. **Smart Scheduling**: Auto-find available slots
2. **Template Copy**: Save and reuse copy patterns
3. **Cross-week Copy**: Copy to different weeks
4. **Conditional Copy**: Copy with rules/constraints
