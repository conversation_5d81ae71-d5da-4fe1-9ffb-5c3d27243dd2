import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useClasses } from '@/hooks/useClasses';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useScheduleSubjects } from '@/hooks/useScheduleSubjects';
import { useExtracurriculars } from '@/hooks/useExtracurriculars';

interface OverviewFiltersProps {
  selectedClassId: string | null;
  onClassChange: (classId: string | null) => void;
  selectedCategoryId: string | null;
  onCategoryChange: (categoryId: string | null) => void;
  selectedSubjectId: string | null;
  onSubjectChange: (subjectId: string | null) => void;
  selectedSemester: string | null;
  onSemesterChange: (semester: string | null) => void;
}

export const OverviewFilters: React.FC<OverviewFiltersProps> = ({
  selectedClassId,
  onClassChange,
  selectedCategoryId,
  onCategoryChange,
  selectedSubjectId,
  onSubjectChange,
  selectedSemester,
  onSemesterChange
}) => {
  const { data: classes = [] } = useClasses();
  const { data: categories = [] } = useSessionCategories();
  const { data: scheduleSubjects = [], isLoading: scheduleSubjectsLoading } = useScheduleSubjects();
  const { data: extracurriculars = [], isLoading: extracurricularsLoading } = useExtracurriculars();
  // Combine schedule subjects and extracurriculars into unified list
  const allSubjects = React.useMemo(() => {
    const combined = [
      // Add schedule subjects (KBM subjects)
      ...scheduleSubjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        session_category_id: subject.session_category_id,
        color: subject.color || '#6B7280',
        type: 'subject' as const
      })),
      // Add extracurriculars
      ...extracurriculars.map(extracurricular => ({
        id: extracurricular.id,
        name: extracurricular.name,
        session_category_id: extracurricular.session_category_id,
        color: extracurricular.color || '#EF4444',
        type: 'extracurricular' as const
      }))
    ];

    console.log('🔍 OverviewFilters - Combined subjects:', {
      scheduleSubjectsLoading,
      extracurricularsLoading,
      scheduleSubjects: scheduleSubjects.length,
      extracurriculars: extracurriculars.length,
      combined: combined.length,
      scheduleSubjectsData: scheduleSubjects.map(s => ({ name: s.name, category_id: s.session_category_id })),
      extracurricularsData: extracurriculars.map(e => ({ name: e.name, category_id: e.session_category_id })),
      combinedData: combined.map(s => ({ name: s.name, type: s.type, category_id: s.session_category_id }))
    });

    return combined;
  }, [scheduleSubjects, extracurriculars]);





  // Sort classes
  const sortedClasses = [...classes].sort((a, b) => {
    if (a.level !== b.level) return a.level.localeCompare(b.level);
    if (a.grade !== b.grade) return a.grade - b.grade;
    return a.name.localeCompare(b.name);
  });

  // Filter subjects based on selected category
  const filteredSubjects = selectedCategoryId
    ? allSubjects.filter(subject => subject.session_category_id === selectedCategoryId)
    : allSubjects;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Class Filter */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">Filter Kelas:</label>
        <Select
          value={selectedClassId || 'all'}
          onValueChange={(value) => onClassChange(value === 'all' ? null : value)}
        >
          <SelectTrigger className="w-full bg-background border-border text-foreground">
            <SelectValue placeholder="Semua Kelas" />
          </SelectTrigger>
          <SelectContent className="bg-popover border-border">
            <SelectItem value="all" className="text-foreground hover:bg-accent">
              Semua Kelas
            </SelectItem>
            {sortedClasses.map(cls => (
              <SelectItem key={cls.id} value={cls.id} className="text-foreground hover:bg-accent">
                {cls.name} ({cls.level} {cls.grade})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Category Filter */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">Filter Kategori:</label>
        <Select
          value={selectedCategoryId || 'all'}
          onValueChange={(value) => {
            const newCategoryId = value === 'all' ? null : value;
            onCategoryChange(newCategoryId);
            // Reset subject filter when category changes
            if (newCategoryId !== selectedCategoryId) {
              onSubjectChange(null);
            }
          }}
        >
          <SelectTrigger className="w-full bg-background border-border text-foreground">
            <SelectValue placeholder="Semua Kategori" />
          </SelectTrigger>
          <SelectContent className="bg-popover border-border">
            <SelectItem value="all" className="text-foreground hover:bg-accent">
              Semua Kategori
            </SelectItem>
            {categories.map(category => (
              <SelectItem key={category.id} value={category.id} className="text-foreground hover:bg-accent">
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  {category.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Semester Filter */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">Filter Semester:</label>
        <Select
          value={selectedSemester || 'all'}
          onValueChange={(value) => onSemesterChange(value === 'all' ? null : value)}
        >
          <SelectTrigger className="w-full bg-background border-border text-foreground">
            <SelectValue placeholder="Semua Semester" />
          </SelectTrigger>
          <SelectContent className="bg-popover border-border">
            <SelectItem value="all" className="text-foreground hover:bg-accent">
              Semua Semester
            </SelectItem>
            <SelectItem value="1" className="text-foreground hover:bg-accent">
              Semester 1 (Juli - Desember)
            </SelectItem>
            <SelectItem value="2" className="text-foreground hover:bg-accent">
              Semester 2 (Januari - Juni)
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Subject Filter */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">Filter Pelajaran/Kegiatan:</label>
        <Select
          value={selectedSubjectId || 'all'}
          onValueChange={(value) => onSubjectChange(value === 'all' ? null : value)}
        >
          <SelectTrigger className="w-full bg-background border-border text-foreground">
            <SelectValue placeholder="Semua Pelajaran/Kegiatan" />
          </SelectTrigger>
          <SelectContent className="bg-popover border-border">
            <SelectItem value="all" className="text-foreground hover:bg-accent">
              Semua Pelajaran/Kegiatan
            </SelectItem>
            {filteredSubjects.map(subject => (
              <SelectItem key={subject.id} value={subject.id} className="text-foreground hover:bg-accent">
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: subject.color }}
                  />
                  {subject.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
