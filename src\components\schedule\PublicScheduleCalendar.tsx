import React, { useMemo, useRef, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import idLocale from '@fullcalendar/core/locales/id';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PublicScheduleCalendarProps {
  schedules: any[];
  academicWeeks: any[];
  timeSessions: any[];
  selectedWeek: number;
  onWeekChange: (week: number) => void;
  selectedClassId: string;
  className?: string;
}

const PublicScheduleCalendar: React.FC<PublicScheduleCalendarProps> = ({
  schedules,
  academicWeeks,
  timeSessions,
  selectedWeek,
  onWeekChange,
  selectedClassId,
  className
}) => {
  
  // ✅ FILTER SCHEDULES: EXACT SAME logic as main ScheduleCalendar
  const filteredSchedules = useMemo(() => {
    console.log('🔥 PUBLIC CALENDAR EVENTS PROCESSING (EXACT SAME AS MAIN):', {
      schedulesLength: schedules?.length || 0,
      selectedWeek,
      selectedClassId,
      rawSchedules: schedules
    });

    // 🚨 PERINGATAN: Jika belum pilih kelas, jangan tampilkan jadwal
    if (!selectedClassId) {
      console.log('⚠️ No class selected - showing empty calendar to prevent UI overlap');
      return [];
    }

    if (!schedules) {
      console.log('❌ No schedules data available');
      return [];
    }

    const filtered = schedules.filter((schedule: any) => {
      // ✅ KEMBALIKAN FILTER MINGGU - TAPI DENGAN DEBUGGING
      const scheduleWeek = parseInt(schedule.academic_week);
      const selectedWeekInt = parseInt(selectedWeek.toString());
      const weekMatch = scheduleWeek === selectedWeekInt;
      const classMatch = !selectedClassId || schedule.class_id === selectedClassId;

      // ✅ ENHANCED: More comprehensive field validation
      const hasRequiredFields = schedule.start_time &&
                               schedule.end_time &&
                               (schedule.subject_name || schedule.schedule_subjects?.name) &&
                               schedule.day_of_week;

      const isValidTimeFormat = /^\d{2}:\d{2}:\d{2}$/.test(schedule.start_time) &&
                               /^\d{2}:\d{2}:\d{2}$/.test(schedule.end_time);

      const result = weekMatch && classMatch && hasRequiredFields && isValidTimeFormat;

      // ✅ DEBUG: Log semua schedule untuk minggu yang dipilih
      if (scheduleWeek === selectedWeekInt && classMatch) {
        console.log('🔍 PUBLIC FILTER DEBUG:', {
          id: schedule.id,
          subject_name: schedule.subject_name || schedule.schedule_subjects?.name,
          academic_week: schedule.academic_week,
          scheduleWeek,
          selectedWeek,
          selectedWeekInt,
          weekMatch,
          day_of_week: schedule.day_of_week,
          start_time: schedule.start_time,
          end_time: schedule.end_time,
          class_id: schedule.class_id,
          hasRequiredFields,
          isValidTimeFormat,
          finalResult: result
        });
      }

      return result;
    });

    console.log('✅ Public Calendar - Filtered schedules (EXACT SAME LOGIC):', {
      originalCount: schedules.length,
      filteredCount: filtered.length,
      selectedWeek,
      selectedClassId,
      sampleData: filtered.slice(0, 3)
    });

    return filtered;
  }, [schedules, selectedWeek, selectedClassId]);

  // ✅ CONVERT TO CALENDAR EVENTS: EXACT SAME logic as main ScheduleCalendar
  const calendarEvents = useMemo(() => {
    console.log('🔥 PUBLIC CALENDAR EVENTS CONVERSION (EXACT SAME AS MAIN):', {
      filteredCount: filteredSchedules?.length || 0,
      selectedWeek,
      academicWeeksCount: academicWeeks?.length || 0
    });

    if (!filteredSchedules || filteredSchedules.length === 0) {
      console.log('❌ No filtered schedules to convert');
      return [];
    }

    // ✅ EXACT SAME: Get current week date calculation
    const getCurrentWeekDate = () => {
      const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);
      if (currentWeek) {
        return new Date(currentWeek.startDate);
      }

      // Fallback: calculate based on week number
      const baseDate = new Date(2025, 0, 6); // Jan 6, 2025 (Monday)
      const weekOffset = (selectedWeek - 1) * 7;
      const calculatedDate = new Date(baseDate);
      calculatedDate.setDate(baseDate.getDate() + weekOffset);
      return calculatedDate;
    };

    const weekStartDate = getCurrentWeekDate();
    console.log('📅 Week start date:', weekStartDate.toISOString());

    const events = filteredSchedules
      .map((schedule: any) => {
        try {
          // ✅ ENHANCED: Handle both schedules_view and manual join data structures
          let subject, category;

          if (schedule.subject_name) {
            // schedules_view format
            subject = {
              id: schedule.subject_id,
              name: schedule.subject_name,
              color: schedule.subject_color
            };
            category = {
              id: schedule.category_id,
              name: schedule.category_name,
              color: schedule.category_color
            };
          } else if (schedule.schedule_subjects) {
            // manual join format
            subject = schedule.schedule_subjects;
            category = schedule.schedule_subjects.session_categories;
          } else {
            console.warn('⚠️ Schedule missing subject data:', schedule);
            return null;
          }

          // ✅ FIXED: Date calculation logic with detailed debugging
          const dayOfWeek = schedule.day_of_week;
          const dayOffset = dayOfWeek - 1; // Convert to 0-based (Monday = 0)

          const scheduleDate = new Date(weekStartDate);
          scheduleDate.setDate(weekStartDate.getDate() + dayOffset);

          console.log(`📅 Date calculation for ${schedule.subject_name}:`, {
            dayOfWeek,
            dayOffset,
            weekStartDate: weekStartDate.toDateString(),
            scheduleDate: scheduleDate.toDateString(),
            startTime: schedule.start_time,
            endTime: schedule.end_time
          });

          // ✅ ENHANCED: Time parsing with error handling
          let startDateTime, endDateTime;

          try {
            const [startHour, startMinute, startSecond] = schedule.start_time.split(':').map(Number);
            const [endHour, endMinute, endSecond] = schedule.end_time.split(':').map(Number);

            startDateTime = new Date(scheduleDate);
            startDateTime.setHours(startHour, startMinute, startSecond || 0, 0);

            endDateTime = new Date(scheduleDate);
            endDateTime.setHours(endHour, endMinute, endSecond || 0, 0);

            console.log(`⏰ Time parsing for ${schedule.subject_name}:`, {
              startTime: schedule.start_time,
              endTime: schedule.end_time,
              startDateTime: startDateTime.toISOString(),
              endDateTime: endDateTime.toISOString()
            });
          } catch (timeError) {
            console.error('❌ Time parsing error:', timeError, schedule);
            return null;
          }

          // ✅ EXACT SAME: Color logic
          const colors = {
            background: subject?.color || category?.color || '#6b7280',
            border: '#ffffff',
            text: '#ffffff'
          };

          const calendarEvent = {
            id: schedule.id,
            title: subject?.name || 'Mata Pelajaran',
            start: startDateTime.toISOString(),
            end: endDateTime.toISOString(),
            backgroundColor: colors.background,
            borderColor: colors.border,
            textColor: colors.text,
            extendedProps: {
              schedule,
              teacher: { id: schedule.teacher_id, full_name: schedule.teacher_name },
              class: { id: schedule.class_id, name: schedule.class_name },
              subject: subject,
              category: category,
              originalColor: colors.background,
              isPublic: true // Mark as public view
            }
          };

          console.log('🔥 PUBLIC CREATED CALENDAR EVENT:', calendarEvent);
          return calendarEvent;
        } catch (error) {
          console.error('❌ Error converting schedule to event:', error, schedule);
          return null;
        }
      })
      .filter(Boolean);

    console.log('🔥 PUBLIC FINAL CALENDAR EVENTS:', {
      eventsCount: events.length,
      events: events,
      selectedWeek,
      selectedClassId,
      weekStartDate: weekStartDate.toISOString(),

      // Debug each event
      eventDetails: events.map(e => ({
        id: e.id,
        title: e.title,
        start: e.start,
        end: e.end,
        backgroundColor: e.backgroundColor
      }))
    });

    // ✅ CRITICAL: Return events even if empty for debugging
    if (events.length === 0) {
      console.log('❌ NO EVENTS CREATED - Check filtering and date calculation');
    }

    return events;
  }, [filteredSchedules, academicWeeks, selectedWeek]);

  // ✅ WEEK NAVIGATION
  const handlePrevWeek = () => {
    if (selectedWeek > 1) {
      onWeekChange(selectedWeek - 1);
    }
  };

  const handleNextWeek = () => {
    if (selectedWeek < academicWeeks.length) {
      onWeekChange(selectedWeek + 1);
    }
  };

  const goToCurrentWeek = () => {
    // Find current week based on today's date
    const today = new Date();
    const currentWeek = academicWeeks.find(week => {
      const startDate = new Date(week.startDate);
      const endDate = new Date(week.endDate);
      return today >= startDate && today <= endDate;
    });

    if (currentWeek) {
      onWeekChange(currentWeek.weekNumber);
    } else {
      // Default to week 1 if current week not found
      onWeekChange(1);
    }
  };

  const currentWeekData = academicWeeks.find(w => w.weekNumber === selectedWeek);

  console.log('📅 CURRENT WEEK DATA:', {
    selectedWeek,
    currentWeekData,
    academicWeeksCount: academicWeeks.length,
    calendarEventsCount: calendarEvents.length
  });

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const calendarRef = useRef<FullCalendar>(null);

  // Auto-scroll to selected week when it changes
  useEffect(() => {
    if (scrollAreaRef.current) {
      const selectedButton = scrollAreaRef.current.querySelector(`[data-week="${selectedWeek}"]`) as HTMLElement;
      if (selectedButton) {
        selectedButton.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  }, [selectedWeek]);

  // ✅ UPDATE CALENDAR DATE WHEN WEEK CHANGES
  useEffect(() => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi && currentWeekData) {
      console.log('🔄 PUBLIC: Updating calendar to week:', selectedWeek, 'date:', currentWeekData.startDate);
      calendarApi.gotoDate(currentWeekData.startDate);
    }
  }, [selectedWeek, currentWeekData]);

  return (
    <div className={cn("w-full", className)}>
      {/* ✅ WEEK NAVIGATION HEADER */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrevWeek}
            disabled={selectedWeek <= 1}
            className="bg-background border-border text-foreground hover:bg-primary hover:text-primary-foreground"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900">
              Minggu {selectedWeek}
            </h3>
            {currentWeekData && (
              <p className="text-sm text-gray-600">
                {currentWeekData.dateRange}
              </p>
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={handleNextWeek}
            disabled={selectedWeek >= academicWeeks.length}
            className="bg-background border-border text-foreground hover:bg-primary hover:text-primary-foreground"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Minggu Ini Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={goToCurrentWeek}
            className="w-10 h-10 rounded-full bg-background border border-blue-500 text-blue-500 hover:bg-blue-500/10 hover:border-blue-500/70 hover:text-blue-600 transition-all duration-300 transform hover:scale-105"
            title="Minggu Ini"
          >
            <Calendar className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-sm text-gray-500">
          {filteredSchedules.length} kegiatan
        </div>
      </div>

      {/* ✅ SCROLLABLE WEEK NUMBERS - SAMA SEPERTI MAIN CALENDAR */}
      <div className="relative z-15 p-3 py-[5px] my-0 mb-4">
        <div ref={scrollAreaRef} className="w-full overflow-x-auto pb-2 custom-scrollbar">
          <style>
            {`
              .custom-scrollbar::-webkit-scrollbar {
                height: 6px;
              }
              .custom-scrollbar::-webkit-scrollbar-track {
                background: hsl(var(--muted));
                border-radius: 3px;
              }
              .custom-scrollbar::-webkit-scrollbar-thumb {
                background: hsl(var(--muted-foreground) / 0.3);
                border-radius: 3px;
              }
              .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                background: hsl(var(--muted-foreground) / 0.5);
              }
            `}
          </style>
          <div className="flex gap-2" style={{
            width: `${academicWeeks.length * 60}px`
          }}>
            {academicWeeks.map(week => {
              const isSelected = selectedWeek === week.weekNumber;

              return (
                <Button
                  key={week.weekNumber}
                  data-week={week.weekNumber}
                  variant={isSelected ? "default" : "outline"}
                  size="sm"
                  onClick={() => onWeekChange(week.weekNumber)}
                  className={`h-6 flex items-center justify-center space-x-2 text-xs font-thin rounded-full px-3 transition-all duration-200 ${
                    isSelected
                      ? 'bg-primary border-primary text-primary-foreground'
                      : 'bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground hover:scale-105'
                  }`}
                >
                  <span className="font-medium">{week.weekNumber}</span>
                </Button>
              );
            })}
          </div>
        </div>
      </div>

      {/* ✅ FULLCALENDAR: Read-only version with FULL WIDTH */}
      <div className="bg-white rounded-lg border border-gray-200 w-full">
        <FullCalendar
          ref={calendarRef}
          plugins={[timeGridPlugin]}
          initialView="timeGridWeek"
          headerToolbar={false} // Hide default header
          height="auto"
          events={calendarEvents}
          locale={idLocale}

          // ✅ FORCE CALENDAR TO SHOW CORRECT WEEK
          initialDate={currentWeekData?.startDate || new Date(2025, 0, 6)} // Week 1 start date

          // ✅ DISABLE ALL INTERACTIONS for public view
          editable={false}
          selectable={false}
          selectMirror={false}
          dayMaxEvents={false}

          // ✅ TIME SETTINGS: Extended to match data range (03:00-23:00)
          slotMinTime="03:00:00"
          slotMaxTime="23:00:00"
          slotDuration="00:30:00"
          slotLabelInterval="01:00:00"
          
          // ✅ WEEK SETTINGS
          firstDay={1} // Monday first
          weekends={true} // Show Sunday
          allDaySlot={false}

          // ✅ STYLING untuk full width
          eventDisplay="block"
          eventMinHeight={15}
          eventShortHeight={30}

          // ✅ LOCALIZATION
          dayHeaderFormat={{
            weekday: 'short',
            month: 'numeric',
            day: 'numeric'
          }}
          slotLabelFormat={{
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          }}

          // ✅ BUSINESS HOURS untuk visual guide
          businessHours={{
            daysOfWeek: [1, 2, 3, 4, 5, 6, 0], // Include Sunday (0)
            startTime: "03:00",
            endTime: "23:00"
          }}
          
          // ✅ EVENT RENDERING: Display with time like main calendar
          eventContent={(eventInfo) => {
            const { event } = eventInfo;
            const teacher = event.extendedProps?.teacher?.full_name;
            const classroom = event.extendedProps?.classroom;
            
            // Format time display
            const startTime = new Date(event.start).toLocaleTimeString('id-ID', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            });
            const endTime = new Date(event.end).toLocaleTimeString('id-ID', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            });
            
            return (
              <div className="p-1 text-xs">
                {/* Time Display */}
                <div className="font-semibold text-white/90 text-[10px] mb-1">
                  {startTime} - {endTime}
                </div>
                {/* Subject Name */}
                <div className="font-medium truncate">
                  {event.title}
                </div>
                {/* Teacher Name */}
                {teacher && (
                  <div className="opacity-90 truncate">
                    {teacher}
                  </div>
                )}
                {/* Classroom */}
                {classroom && (
                  <div className="opacity-75 truncate">
                    📍 {classroom}
                  </div>
                )}
              </div>
            );
          }}
          
          // ✅ NO CLICK HANDLERS for public view
          eventClick={() => {}}
          select={() => {}}
          
          // ✅ CUSTOM CSS
          eventClassNames="cursor-default"
        />
      </div>

      {/* ✅ INFO MESSAGE */}
      <div className="mt-4 text-center text-sm text-gray-500">
        <p>📅 Jadwal ini adalah tampilan publik - hanya untuk melihat</p>
      </div>
    </div>
  );
};

export default PublicScheduleCalendar;
