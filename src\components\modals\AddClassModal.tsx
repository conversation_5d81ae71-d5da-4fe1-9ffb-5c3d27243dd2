
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCreateClass, useUpdateClass } from '@/hooks/useClasses';
import { useEducationLevels } from '@/hooks/useEducationLevels';

interface AddClassModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingClass?: any;
}

const AddClassModal: React.FC<AddClassModalProps> = ({ open, onOpenChange, editingClass }) => {
  const [formData, setFormData] = useState({
    name: '',
    level: '',
    capacity: ''
  });

  const createClass = useCreateClass();
  const updateClass = useUpdateClass();
  const { data: educationLevels } = useEducationLevels();

  // Generate available grade levels based on education levels data
  const getAvailableGradeLevels = () => {
    if (!educationLevels) return [];
    
    const gradeLevels = [];
    for (const eduLevel of educationLevels) {
      for (let grade = eduLevel.min_grade; grade <= eduLevel.max_grade; grade++) {
        const romanNumerals = ['', 'I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'];
        gradeLevels.push({
          value: romanNumerals[grade],
          label: `Kelas ${romanNumerals[grade]}`,
          grade: grade
        });
      }
    }
    
    // Remove duplicates and sort by grade number
    const uniqueGrades = gradeLevels.filter((grade, index, self) => 
      index === self.findIndex(g => g.value === grade.value)
    ).sort((a, b) => a.grade - b.grade);
    
    return uniqueGrades;
  };

  useEffect(() => {
    if (editingClass) {
      setFormData({
        name: editingClass.name || '',
        level: editingClass.level || '',
        capacity: editingClass.capacity?.toString() || '',
      });
    } else {
      setFormData({
        name: '',
        level: '',
        capacity: ''
      });
    }
  }, [editingClass, open]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const submitData = {
      ...formData,
      capacity: formData.capacity ? parseInt(formData.capacity) : null
    };
    
    if (editingClass) {
      updateClass.mutate(
        { id: editingClass.id, ...submitData },
        {
          onSuccess: () => {
            onOpenChange(false);
          }
        }
      );
    } else {
      createClass.mutate(submitData, {
        onSuccess: () => {
          onOpenChange(false);
        }
      });
    }
  };

  const isLoading = createClass.isPending || updateClass.isPending;
  const availableGrades = getAvailableGradeLevels();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {editingClass ? 'Edit Kelas' : 'Tambah Kelas Baru'}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nama Kelas</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Contoh: VII A, X IPA 1"
              
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="level">Tingkat</Label>
            <Select value={formData.level} onValueChange={(value) => setFormData(prev => ({ ...prev, level: value }))}>
              <SelectTrigger >
                <SelectValue placeholder="Pilih tingkat" />
              </SelectTrigger>
              <SelectContent>
                {availableGrades.length > 0 ? (
                  availableGrades.map((grade) => (
                    <SelectItem key={grade.value} value={grade.value}>
                      {grade.label}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="" disabled>
                    Belum ada jenjang pendidikan yang diatur
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="capacity">Kapasitas</Label>
            <Input
              id="capacity"
              type="number"
              value={formData.capacity}
              onChange={(e) => setFormData(prev => ({ ...prev, capacity: e.target.value }))}
              placeholder="Jumlah siswa maksimal"
              
            />
          </div>
          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)} 
              
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button 
              type="submit" 
              
              disabled={isLoading}
            >
              {isLoading ? 'Menyimpan...' : (editingClass ? 'Update' : 'Simpan')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddClassModal;
