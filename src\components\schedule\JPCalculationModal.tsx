import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Calculator, 
  ChevronDown, 
  ChevronRight, 
  Calendar,
  Clock,
  BarChart3,
  X
} from 'lucide-react';
import { useJPPeriodCalculation, PeriodType } from '@/hooks/useJPPeriodCalculation';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface JPCalculationModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedClassId?: string;
  selectedWeek?: number;
}

export const JPCalculationModal: React.FC<JPCalculationModalProps> = ({
  isOpen,
  onClose,
  selectedClassId,
  selectedWeek
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<PeriodType>('weekly');
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({
    kbm: true,
    ekstrakurikuler: true
  });

  const calculation = useJPPeriodCalculation({
    selectedClassId,
    selectedWeek,
    periodType: selectedPeriod
  });



  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const periodOptions = [
    { value: 'weekly' as PeriodType, label: 'Mingguan', icon: Calendar },
    { value: 'monthly' as PeriodType, label: 'Bulanan', icon: BarChart3 },
    { value: 'all' as PeriodType, label: 'Semua', icon: Clock }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg max-h-[85vh] bg-background border-border backdrop-blur-sm shadow-2xl animate-in fade-in-0 zoom-in-95 duration-300">
        <DialogHeader className="pb-3">
          <DialogTitle className="text-lg font-bold text-foreground flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-primary/20">
              <Calculator className="h-5 w-5 text-primary" />
            </div>
            JP dalam Periode
          </DialogTitle>

          {/* Period Filter Tabs */}
          <div className="flex gap-1.5 mt-3">
            {periodOptions.map((option) => {
              const Icon = option.icon;
              return (
                <Button
                  key={option.value}
                  variant={selectedPeriod === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedPeriod(option.value)}
                  className={`flex items-center gap-1.5 text-xs px-3 py-1.5 ${
                    selectedPeriod === option.value
                      ? 'bg-primary text-primary-foreground border-primary'
                      : 'bg-muted text-muted-foreground border-border hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <Icon className="h-3.5 w-3.5" />
                  {option.label}
                </Button>
              );
            })}
          </div>

          {/* Date Range */}
          {calculation.dateRange && (
            <div className="flex items-center gap-1.5 mt-2 p-2 bg-muted/50 border border-border rounded-md">
              <Calendar className="h-3.5 w-3.5 text-primary" />
              <span className="text-xs text-muted-foreground font-medium">{calculation.dateRange}</span>
            </div>
          )}
        </DialogHeader>

        <div className="space-y-3 overflow-y-auto max-h-[55vh] pr-2">
          {/* Categories */}
          {calculation.categories.map((category) => (
            <Card key={category.id} className="bg-card border-border">
              <Collapsible
                open={expandedCategories[category.id]}
                onOpenChange={() => toggleCategory(category.id)}
              >
                <CollapsibleTrigger asChild>
                  <div className="p-3 cursor-pointer hover:bg-accent/30 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2.5">
                        <div
                          className="w-3.5 h-3.5 rounded-sm"
                          style={{ backgroundColor: category.color }}
                        />
                        <span className="font-medium text-foreground text-sm">{category.name}</span>
                      </div>

                      <div className="flex items-center gap-2.5">
                        <Badge
                          variant="secondary"
                          className="bg-primary/20 text-primary border-primary/30 text-xs px-2 py-0.5"
                        >
                          {category.totalJP} JP
                        </Badge>

                        {expandedCategories[category.id] ? (
                          <ChevronDown className="h-3.5 w-3.5 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-3.5 w-3.5 text-muted-foreground" />
                        )}
                      </div>
                    </div>
                  </div>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <div className="px-3 pb-3 space-y-1.5">
                    {category.subjects.map((subject) => (
                      <div
                        key={subject.id}
                        className="flex items-center justify-between p-2.5 bg-muted/40 rounded-md border-l-3"
                        style={{ borderLeftColor: subject.color }}
                      >
                        <div className="flex-1">
                          <div className="font-medium text-foreground text-xs">
                            {subject.name}
                          </div>
                          <div className="text-xs text-muted-foreground mt-0.5">
                            {subject.hours} jam
                          </div>
                        </div>

                        <Badge
                          variant="outline"
                          className="bg-muted text-muted-foreground border-border text-xs px-1.5 py-0.5"
                        >
                          {subject.jp} JP
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}

          {/* Empty State */}
          {calculation.categories.length === 0 && (
            <div className="text-center py-8">
              <Calculator className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
              <h3 className="text-base font-medium text-foreground mb-1">
                Belum Ada Data JP
              </h3>
              <p className="text-muted-foreground text-sm">
                Belum ada jadwal untuk periode yang dipilih
              </p>
            </div>
          )}
        </div>

        {/* Grand Total */}
        {calculation.categories.length > 0 && (
          <div className="border-t border-border pt-2 mt-3">
            <Card className="bg-primary/10 border-primary/30">
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-bold text-foreground text-base">Total JP</h3>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">
                      {calculation.grandTotal.totalJP}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
