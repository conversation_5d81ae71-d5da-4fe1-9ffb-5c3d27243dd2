
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const useSubjects = () => {
  return useQuery({
    queryKey: ['subjects'],
    queryFn: async () => {
      console.log('Fetching subjects...');

      // Get current user's school_id and active academic year
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
        throw new Error('Failed to get user profile');
      }

      const { data: activeYear, error: yearError } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile.school_id)
        .eq('is_active', true)
        .single();

      if (yearError) {
        console.error('Error fetching active year:', yearError);
        throw new Error('Failed to get active academic year');
      }

      const { data, error } = await supabase
        .from('subjects')
        .select(`
          *,
          session_categories (
            id,
            name,
            color
          )
        `)
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeYear.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching subjects:', error);
        throw error;
      }
      console.log('Subjects fetched:', data);
      return data;
    },
    staleTime: 0, // Force fresh data
    gcTime: 0, // Don't cache
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

export const useCreateSubject = (showToast: boolean = true) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (subjectData: any) => {
      console.log('Creating subject with data:', subjectData);
      
      try {
        // Get current user's school_id and active academic year
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('school_id')
          .eq('id', user.id)
          .single();

        if (profileError) {
          console.error('Error fetching profile:', profileError);
          throw new Error('Failed to get user profile');
        }

        const { data: activeYear, error: yearError } = await supabase
          .from('academic_years')
          .select('id')
          .eq('school_id', profile.school_id)
          .eq('is_active', true)
          .single();

        if (yearError) {
          console.error('Error fetching active year:', yearError);
          throw new Error('Failed to get active academic year');
        }

        // Get KBM category for this school and academic year
        const { data: kbmCategory } = await supabase
          .from('session_categories')
          .select('id')
          .eq('name', 'KBM')
          .eq('school_id', profile.school_id)
          .eq('academic_year_id', activeYear.id)
          .single();

        // Clean the subject data
        const cleanSubjectData = {
          name: subjectData.name,
          code: subjectData.code,
          color: subjectData.color || '#6B7280', // ✅ ADDED: Color field with default
          total_hours_per_year: subjectData.total_hours_per_year || 0, // ✅ ADDED: JP/Tahun field
          session_categories_id: subjectData.session_categories_id || kbmCategory?.id || null, // ✅ Use new field
          curriculum_categories_id: subjectData.curriculum_categories_id || null, // ✅ New field for curriculum
          school_id: profile.school_id,
          academic_year_id: activeYear.id,
        };

        console.log('Clean subject data:', cleanSubjectData);

        const { data, error } = await supabase
          .from('subjects')
          .insert(cleanSubjectData)
          .select()
          .single();

        if (error) {
          console.error('Error creating subject:', error);
          console.error('Error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });

          // Handle duplicate key error specifically
          if (error.code === '23505' && error.message.includes('subjects_school_id_academic_year_id_code_key')) {
            throw new Error('Kode mata pelajaran sudah digunakan. Silakan gunakan kode yang berbeda.');
          }

          throw error;
        }

        console.log('Subject created:', data);
        return data;
      } catch (error) {
        console.error('Full error in createSubject:', error);
        throw error;
      }
    },
    onSuccess: async () => {
      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      await queryClient.invalidateQueries({ queryKey: ['subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });
      await queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] });

      // ✅ ENHANCED: Force immediate refetch for instant UI updates
      queryClient.refetchQueries({ queryKey: ['subjects'] });
      queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });

      if (showToast) {
        toast({
          title: "Berhasil",
          description: "Data mata pelajaran berhasil ditambahkan",
        });
      }
    },
    onError: (error: any) => {
      console.error('Error in onError:', error);
      if (showToast) {
        toast({
          title: "Gagal",
          description: "Gagal menambahkan data mata pelajaran: " + (error.message || 'Unknown error'),
          variant: "destructive",
        });
      }
    },
  });
};

export const useUpdateSubject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...subjectData }: any) => {
      console.log('Updating subject:', id, subjectData);

      const cleanSubjectData = {
        name: subjectData.name,
        code: subjectData.code,
        color: subjectData.color || '#6B7280', // ✅ ADDED: Color field for update
        total_hours_per_year: subjectData.total_hours_per_year || 0, // ✅ ADDED: JP/Tahun field for update
        session_categories_id: subjectData.session_categories_id || null, // ✅ Use new field
        curriculum_categories_id: subjectData.curriculum_categories_id || null, // ✅ New field for curriculum
      };

      const { data, error } = await supabase
        .from('subjects')
        .update(cleanSubjectData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating subject:', error);
        throw error;
      }

      console.log('Subject updated:', data);
      return data;
    },
    onSuccess: async () => {
      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      await queryClient.invalidateQueries({ queryKey: ['subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });
      await queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] });

      // ✅ ENHANCED: Force immediate refetch for instant UI updates
      queryClient.refetchQueries({ queryKey: ['subjects'] });
      queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });

      toast({
        title: "Berhasil",
        description: "Data mata pelajaran berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      console.error('Error updating subject:', error);
      toast({
        title: "Gagal",
        description: "Gagal memperbarui data mata pelajaran: " + (error.message || 'Unknown error'),
        variant: "destructive",
      });
    },
  });
};

export const useDeleteSubject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      console.log('Deleting subject:', id);
      
      const { error } = await supabase
        .from('subjects')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting subject:', error);
        throw error;
      }
    },
    onSuccess: async () => {
      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      await queryClient.invalidateQueries({ queryKey: ['subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });
      await queryClient.invalidateQueries({ queryKey: ['class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] });

      // ✅ ENHANCED: Force immediate refetch for instant UI updates
      queryClient.refetchQueries({ queryKey: ['subjects'] });
      queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });

      toast({
        title: "Berhasil",
        description: "Data mata pelajaran berhasil dihapus",
      });
    },
    onError: (error: any) => {
      console.error('Error deleting subject:', error);
      toast({
        title: "Gagal",
        description: "Gagal menghapus data mata pelajaran: " + (error.message || 'Unknown error'),
        variant: "destructive",
      });
    },
  });
};
