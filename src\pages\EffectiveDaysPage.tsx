import React, { useMemo } from 'react';
import { useHolidays } from '@/hooks/useHolidays';
import { useAcademicYears } from '@/hooks/useAcademicYears';
import { eachDayOfInterval, isWithinInterval, startOfDay, getDay } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { CalendarDays, CheckCircle } from 'lucide-react';
import EffectiveDaysCalendar from '@/components/events/EffectiveDaysCalendar';

const EffectiveDaysPage: React.FC = () => {
  const { holidays, isLoading: isLoadingHolidays } = useHolidays();
  const { data: academicYears, isLoading: isLoadingYears } = useAcademicYears();

  const { effectiveDaysCount, totalDays, year, nonEffectiveDaysSet, academicStartDate, academicEndDate } = useMemo(() => {
    const activeAcademicYear = academicYears?.find(year => year.is_active);
    if (!activeAcademicYear || !holidays) {
      return { effectiveDaysCount: 0, totalDays: 0, year: new Date().getFullYear(), nonEffectiveDaysSet: new Set<string>(), academicStartDate: null, academicEndDate: null };
    }

    const yearStartDate = new Date(activeAcademicYear.start_date);
    const yearEndDate = new Date(activeAcademicYear.end_date);
    const year = yearStartDate.getFullYear();

    const allDaysInYear = eachDayOfInterval({ start: yearStartDate, end: yearEndDate });
    const nonEffectiveDays = new Set<string>();

    // Mark all Sundays
    allDaysInYear.forEach(day => {
      if (getDay(day) === 0) { // 0 is Sunday
        nonEffectiveDays.add(day.toISOString().split('T')[0]);
      }
    });

    // Mark all holidays
    holidays.forEach(holiday => {
      const holidayStart = startOfDay(new Date(holiday.start_date));
      const holidayEnd = startOfDay(new Date(holiday.end_date));
      const daysInHoliday = eachDayOfInterval({ start: holidayStart, end: holidayEnd });
      daysInHoliday.forEach(day => {
        nonEffectiveDays.add(day.toISOString().split('T')[0]);
      });
    });

    const totalDaysInYear = allDaysInYear.length;
    const effectiveDays = totalDaysInYear - nonEffectiveDays.size;

    return {
      effectiveDaysCount: effectiveDays,
      totalDays: totalDaysInYear,
      year,
      nonEffectiveDaysSet: nonEffectiveDays,
      academicStartDate: yearStartDate,
      academicEndDate: yearEndDate
    };
  }, [academicYears, holidays]);

  const isLoading = isLoadingHolidays || isLoadingYears;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <CalendarDays className="h-12 w-12 animate-pulse text-lime-400 mx-auto mb-4" />
          <p className="text-muted-foreground">Menghitung hari efektif...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Card className="bg-card border-border rounded-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-lime-400" />
            <span>Total Hari Efektif Belajar ({year}/{year + 1})</span>
          </CardTitle>
          <CardDescription>
            Jumlah hari di luar hari Minggu dan hari libur/event yang telah ditetapkan.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-5xl font-bold text-foreground">{effectiveDaysCount}</div>
          <p className="text-muted-foreground mt-2">dari total {totalDays} hari dalam setahun.</p>
        </CardContent>
      </Card>

      <Card className="bg-card border-border rounded-2xl">
        <CardHeader>
          <CardTitle>Detail Kalender Tahunan</CardTitle>
          <CardDescription>Tampilan visual hari efektif dan non-efektif dalam satu tahun akademik.</CardDescription>
        </CardHeader>
        <CardContent>
          {academicStartDate && academicEndDate ? (
            <EffectiveDaysCalendar 
              year={year} 
              nonEffectiveDaysSet={nonEffectiveDaysSet}
              academicStartDate={academicStartDate}
              academicEndDate={academicEndDate}
            />
          ) : (
            <p className="text-muted-foreground">Data tahun akademik aktif tidak ditemukan untuk menampilkan kalender.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EffectiveDaysPage;

