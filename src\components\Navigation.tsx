
import React from 'react';
import { Home, Calendar, User, Phone, LayoutDashboard, LogOut } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SimpleThemeToggle } from '@/components/ui/theme-toggle';

interface NavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isLoggedIn: boolean;
  onLoginClick: () => void;
  onLogoutClick: () => void;
}

const Navigation = ({ activeTab, onTabChange, isLoggedIn, onLoginClick, onLogoutClick }: NavigationProps) => {
  const navItems = isLoggedIn ? [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'logout', label: 'Logout', icon: LogOut, action: onLogoutClick },
  ] : [
    { id: 'beranda', label: 'Beranda', icon: Home },
    { id: 'fitur', label: 'Fitur Utama', icon: Calendar },
    { id: 'login', label: 'Login/Daftar', icon: User, action: onLoginClick },
    { id: 'kontak', label: '<PERSON><PERSON>ng<PERSON> Ka<PERSON>', icon: Phone },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border/50">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold text-foreground tracking-tight">
            Indo<span className="text-lime-400">Jadwal</span>
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 bg-muted/60 rounded-full p-2">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => item.action ? item.action() : onTabChange(item.id)}
                  className={cn(
                    "flex items-center space-x-2 px-4 py-2 rounded-full transition-all duration-300",
                    activeTab === item.id || (item.id === 'beranda' && !isLoggedIn && activeTab === 'beranda')
                      ? "bg-lime-400 text-gray-900 shadow-lg shadow-lime-400/20"
                      : "text-muted-foreground hover:text-foreground hover:bg-accent/50"
                  )}
                >
                  <item.icon size={18} />
                  <span className="text-sm font-medium tracking-wide">{item.label}</span>
                </button>
              ))}
            </div>
            <SimpleThemeToggle />
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
