import React from 'react';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Card, CardContent } from '@/components/ui/card';

const PublicExternalViewTest: React.FC = () => {
  // ✅ HARDCODED TEST EVENTS - Based on actual database data
  const testEvents = [
    // Week 1 - Tuesday (day 2)
    {
      id: 'test-1',
      title: 'MPLS',
      start: '2025-01-07T03:00:00', // Tuesday, Week 1
      end: '2025-01-07T22:30:00',
      backgroundColor: '#ff6b6b',
      borderColor: '#ffffff',
      textColor: '#ffffff'
    },
    // Week 1 - Wednesday (day 3)
    {
      id: 'test-2',
      title: 'MPLS',
      start: '2025-01-08T03:00:00', // Wednesday, Week 1
      end: '2025-01-08T22:30:00',
      backgroundColor: '#4ecdc4',
      borderColor: '#ffffff',
      textColor: '#ffffff'
    },
    // Week 1 - Thursday (day 4)
    {
      id: 'test-3',
      title: 'MPLS',
      start: '2025-01-09T03:00:00', // Thursday, Week 1
      end: '2025-01-09T22:30:00',
      backgroundColor: '#45b7d1',
      borderColor: '#ffffff',
      textColor: '#ffffff'
    },
    // Week 1 - Friday (day 5)
    {
      id: 'test-4',
      title: 'MPLS',
      start: '2025-01-10T03:00:00', // Friday, Week 1
      end: '2025-01-10T22:30:00',
      backgroundColor: '#f9ca24',
      borderColor: '#ffffff',
      textColor: '#ffffff'
    },
    // Week 1 - Saturday (day 6)
    {
      id: 'test-5',
      title: 'Libur Semester',
      start: '2025-01-11T03:00:00', // Saturday, Week 1
      end: '2025-01-11T22:30:00',
      backgroundColor: '#6c5ce7',
      borderColor: '#ffffff',
      textColor: '#ffffff'
    },
    // Week 1 - Sunday (day 7)
    {
      id: 'test-6',
      title: 'Libur Semester',
      start: '2025-01-12T03:00:00', // Sunday, Week 1
      end: '2025-01-12T22:30:00',
      backgroundColor: '#a29bfe',
      borderColor: '#ffffff',
      textColor: '#ffffff'
    },
    
    // Week 2 - Monday (day 1)
    {
      id: 'test-7',
      title: 'MPLS',
      start: '2025-01-13T03:00:00', // Monday, Week 2
      end: '2025-01-13T22:30:00',
      backgroundColor: '#fd79a8',
      borderColor: '#ffffff',
      textColor: '#ffffff'
    },
    // Week 2 - Tuesday (day 2)
    {
      id: 'test-8',
      title: 'MPLS',
      start: '2025-01-14T03:00:00', // Tuesday, Week 2
      end: '2025-01-14T22:30:00',
      backgroundColor: '#fdcb6e',
      borderColor: '#ffffff',
      textColor: '#ffffff'
    }
  ];

  console.log('🧪 TEST EVENTS:', testEvents);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <h1 className="text-2xl font-bold text-gray-900">
            TEST - Public External View
          </h1>
          <p className="text-gray-600">
            Testing calendar with hardcoded events based on database data
          </p>
        </div>
      </div>

      {/* Calendar */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <Card>
          <CardContent className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold">Test Events Count: {testEvents.length}</h3>
              <p className="text-sm text-gray-600">
                Events span from Jan 7-14, 2025 (Week 1-2)
              </p>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200">
              <FullCalendar
                plugins={[timeGridPlugin]}
                initialView="timeGridWeek"
                headerToolbar={{
                  left: 'prev,next today',
                  center: 'title',
                  right: 'timeGridWeek,timeGridDay'
                }}
                height="auto"
                events={testEvents}
                
                // Time settings to match database data
                slotMinTime="03:00:00"
                slotMaxTime="23:00:00"
                slotDuration="00:30:00"
                slotLabelInterval="01:00:00"
                
                // Week settings
                firstDay={1} // Monday first
                weekends={true} // Show Sunday
                
                // Start at Week 1
                initialDate="2025-01-06" // Monday of Week 1
                
                // Styling
                eventDisplay="block"
                
                // Localization
                locale="id"
                dayHeaderFormat={{ weekday: 'long', day: 'numeric', month: 'short' }}
                slotLabelFormat={{
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false
                }}
                
                // Event rendering
                eventContent={(eventInfo) => {
                  const { event } = eventInfo;
                  
                  return (
                    <div className="p-1 text-xs">
                      <div className="font-medium truncate">
                        {event.title}
                      </div>
                      <div className="opacity-75 truncate">
                        {event.start?.toLocaleTimeString('id-ID', { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })} - {event.end?.toLocaleTimeString('id-ID', { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </div>
                    </div>
                  );
                }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Debug Info */}
      <div className="bg-gray-100 p-4 text-xs text-gray-600">
        <h4 className="font-bold mb-2">Debug Information:</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p><strong>Total Events:</strong> {testEvents.length}</p>
            <p><strong>Date Range:</strong> Jan 7-14, 2025</p>
            <p><strong>Time Range:</strong> 03:00-22:30</p>
          </div>
          <div>
            <p><strong>Week 1 Events:</strong> {testEvents.filter(e => e.start.includes('2025-01-0')).length}</p>
            <p><strong>Week 2 Events:</strong> {testEvents.filter(e => e.start.includes('2025-01-1')).length}</p>
            <p><strong>Calendar View:</strong> timeGridWeek</p>
          </div>
        </div>
        
        <div className="mt-4">
          <p><strong>Event Details:</strong></p>
          <ul className="list-disc list-inside">
            {testEvents.map(event => (
              <li key={event.id}>
                {event.title} - {event.start} to {event.end}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PublicExternalViewTest;
