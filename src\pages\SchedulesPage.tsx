
import React from 'react';
import { Routes, Route } from 'react-router-dom';

import { ScheduleCalendar } from '@/components/schedule/ScheduleCalendar';
import ScheduleOverview from '@/components/schedule/ScheduleOverview';
import ExternalView from '@/components/schedule/ExternalView';
import CalendarSync from '@/components/schedule/CalendarSync';
import PrintSchedule from '@/components/schedule/PrintSchedule';

const SchedulesPage = () => {
  return (
    <Routes>
      <Route index element={<ScheduleCalendar />} />
      <Route path="overview" element={<ScheduleOverview />} />
      <Route path="external" element={<ExternalView />} />
      <Route path="sync" element={<CalendarSync />} />
      <Route path="print" element={<PrintSchedule />} />
    </Routes>
  );
};

export default SchedulesPage;
