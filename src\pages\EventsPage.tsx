import React, { useState, useEffect, useMemo } from 'react';
import { eachDayOfInterval, getDay, startOfDay } from 'date-fns';
import { CalendarDays, Plus, Calendar, List, Flag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { AnnualCalendar } from '@/components/events/AnnualCalendar';
import { EventDialog } from '@/components/events/EventDialog';
import { NationalHolidaysDialog } from '@/components/events/NationalHolidaysDialog';
import { EventList } from '@/components/events/EventList';
import { useHolidays } from '@/hooks/useHolidays';
import { useClasses } from '@/hooks/useClasses';
import { useAcademicYears } from '@/hooks/useAcademicYears';
import { Holiday } from '@/types/event';
import { CheckCircle } from 'lucide-react';
const EventsPage: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [selectedEvent, setSelectedEvent] = useState<Holiday | null>(null);
  const [showEventDialog, setShowEventDialog] = useState(false);
  const [showNationalHolidaysDialog, setShowNationalHolidaysDialog] = useState(false);
  const [selectedClassId, setSelectedClassId] = useState<string>('all');
  const [showEventList, setShowEventList] = useState(() => {
    return localStorage.getItem('events-show-list') === 'true';
  });
  const {
    data: academicYears
  } = useAcademicYears();
  const {
    data: classes = []
  } = useClasses();
  const {
    holidays,
    isLoading,
    createHoliday,
    updateHoliday,
    deleteHoliday,
    createMultipleHolidays
  } = useHolidays();
  const activeAcademicYear = academicYears?.find(year => year.is_active);
  const currentYear = activeAcademicYear ? new Date(activeAcademicYear.start_date).getFullYear() : new Date().getFullYear();

  const { effectiveDaysCount, totalDays } = useMemo(() => {
    const activeAcademicYear = academicYears?.find(year => year.is_active);
    if (!activeAcademicYear || !holidays) {
      return { effectiveDaysCount: 0, totalDays: 0 };
    }

    const yearStartDate = new Date(activeAcademicYear.start_date);
    const yearEndDate = new Date(activeAcademicYear.end_date);

    const allDaysInYear = eachDayOfInterval({ start: yearStartDate, end: yearEndDate });
    const nonEffectiveDays = new Set<string>();

    // Mark all Sundays
    allDaysInYear.forEach(day => {
      if (getDay(day) === 0) { // 0 is Sunday
        nonEffectiveDays.add(day.toISOString().split('T')[0]);
      }
    });

    // 🎯 FILTER HOLIDAYS BASED ON SELECTED CLASS - SAME LOGIC AS AnnualCalendar
    const filteredHolidays = holidays.filter(holiday => {
      if (!selectedClassId || selectedClassId === 'all') return true;
      if (!holiday.class_ids || holiday.class_ids.length === 0) return true; // All classes
      return holiday.class_ids.includes(selectedClassId);
    });

    // Mark filtered holidays only
    filteredHolidays.forEach(holiday => {
      const holidayStart = startOfDay(new Date(holiday.start_date));
      const holidayEnd = startOfDay(new Date(holiday.end_date));
      const daysInHoliday = eachDayOfInterval({ start: holidayStart, end: holidayEnd });
      daysInHoliday.forEach(day => {
        nonEffectiveDays.add(day.toISOString().split('T')[0]);
      });
    });

    const totalDaysInYear = allDaysInYear.length;
    const effectiveDays = totalDaysInYear - nonEffectiveDays.size;

    console.log('🎯 Effective Days Calculation:', {
      selectedClassId,
      totalHolidays: holidays.length,
      filteredHolidays: filteredHolidays.length,
      totalDays: totalDaysInYear,
      nonEffectiveDays: nonEffectiveDays.size,
      effectiveDays
    });

    return {
      effectiveDaysCount: effectiveDays,
      totalDays: totalDaysInYear,
    };
  }, [academicYears, holidays, selectedClassId]); // 🎯 ADD selectedClassId dependency

  // Save toggle state to localStorage
  useEffect(() => {
    localStorage.setItem('events-show-list', showEventList.toString());
  }, [showEventList]);
  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    setSelectedEvent(null);
    setShowEventDialog(true);
  };
  const handleEventClick = (holiday: Holiday) => {
    setSelectedEvent(holiday);
    setSelectedDate(undefined);
    setShowEventDialog(true);
  };
  const handleEventSave = (eventData: Omit<Holiday, 'id' | 'school_id' | 'academic_year_id' | 'created_at' | 'updated_at'>) => {
    createHoliday(eventData);
  };
  const handleEventUpdate = (eventData: Partial<Holiday> & {
    id: string;
  }) => {
    updateHoliday(eventData);
  };
  const handleEventDelete = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus event ini?')) {
      deleteHoliday(id);
    }
  };
  const handleNationalHolidaysSave = (holidaysData: any[]) => {
    createMultipleHolidays(holidaysData);
  };
  const existingNationalHolidayIds = holidays.filter(holiday => holiday.is_national_holiday).map(holiday => {
    // Extract potential national holiday ID from name or create a simple mapping
    const name = holiday.name.toLowerCase();
    if (name.includes('tahun baru') && name.includes('2024')) return 'tahun-baru-2024';
    if (name.includes('tahun baru') && name.includes('2025')) return 'tahun-baru-2025';
    if (name.includes('imlek') && name.includes('2024')) return 'imlek-2024';
    if (name.includes('imlek') && name.includes('2025')) return 'imlek-2025';
    // Add more mappings as needed
    return '';
  }).filter(Boolean);
  return <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center space-x-2">
                <CalendarDays className="h-8 w-8 text-lime-400" />
                <h1 className="text-3xl font-bold text-foreground">Event</h1>
              </div>
            </div>
            <p className="text-muted-foreground">
              Kelola event dan kegiatan tahun akademik {currentYear}/{String(currentYear + 1).slice(-2)}
            </p>
          </div>
          <div className="flex gap-3">
            <Button onClick={() => setShowNationalHolidaysDialog(true)} className="bg-red-400/10 border border-red-400/20 hover:bg-ref-400/20 flex items-center justify-center space-x-2 text-red-400">
              <Flag className="h-4 w-4 mr-2" />
              Libur Nasional
            </Button>
            <Button onClick={() => {
            setSelectedDate(new Date());
            setSelectedEvent(null);
            setShowEventDialog(true);
          }} className="bg-lime-400/10 border border-lime-400/20 text-lime-400 hover:bg-lime-400/20 flex items-center justify-center space-x-2">
              <Plus className="h-5 w-5 mr-2" />
              Tambah Event
            </Button>
          </div>
        </div>

        {/* Filter Section */}
        <Card className="bg-card border-border rounded-2xl p-6">
          <CardContent className="p-0">
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-lime-400" />
                  <span className="text-foreground">Filter Kelas:</span>
                </div>
                <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                  <SelectTrigger className="w-48 bg-background border-lime-400/30 text-foreground">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Kelas</SelectItem>
                    {classes.map(cls => <SelectItem key={cls.id} value={cls.id}>
                        {cls.name}
                      </SelectItem>)}
                  </SelectContent>
                </Select>
              </div>
              
              <Button variant="outline" onClick={() => setShowEventList(!showEventList)} className="bg-lime-400/10 border border-lime-400/20 text-lime-400 hover:bg-lime-400/20 flex items-center justify-center space-x-2">
                <List className="h-4 w-4 mr-2" />
                {showEventList ? 'Sembunyikan' : 'Tampilkan'} Daftar Event
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Area */}
        <Card className="bg-card border-border rounded-2xl p-2">
          <CardHeader className="p-6 flex flex-row items-start justify-between">
            <div>
              <CardTitle className="text-card-foreground text-xl">
                Kalender Event Tahunan
              </CardTitle>
              <CardDescription className="text-muted-foreground mt-1">
                Klik tanggal kosong untuk menambah event, klik event untuk mengedit
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 text-sm font-medium text-lime-500">
                 <CheckCircle className="h-5 w-5" />
                 <span>Hari Efektif</span>
              </div>
              <div className="text-2xl font-bold text-foreground mt-1">{effectiveDaysCount} <span className="text-base font-normal text-muted-foreground">/ {totalDays} hari</span></div>
            </div>
          </CardHeader>
          <CardContent className="p-6 bg-transparent">
            {isLoading ? <div className="flex items-center justify-center py-16">
                <div className="text-center">
                  <CalendarDays className="h-12 w-12 animate-pulse text-lime-400 mx-auto mb-4" />
                  <p className="text-gray-400">Memuat kalender event...</p>
                </div>
              </div> : <AnnualCalendar year={currentYear} holidays={holidays} selectedClassId={selectedClassId} onDateClick={handleDateClick} onEventClick={handleEventClick} />}
          </CardContent>
        </Card>

        {/* Event List */}
        <Collapsible open={showEventList} onOpenChange={setShowEventList}>
          <CollapsibleContent>
            <Card className="bg-card border-border">
              <CardHeader className="pb-4">
                <CardTitle className="text-card-foreground text-xl">
                  Daftar Event
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Kelola semua event dengan mudah melalui tabel di bawah ini
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <EventList holidays={holidays} onEdit={handleEventClick} onDelete={handleEventDelete} selectedClassId={selectedClassId} onClassFilterChange={setSelectedClassId} />
              </CardContent>
            </Card>
          </CollapsibleContent>
        </Collapsible>
      </div>

      {/* Dialogs */}
      <EventDialog open={showEventDialog} onOpenChange={setShowEventDialog} event={selectedEvent} initialDate={selectedDate} onSave={handleEventSave} onUpdate={handleEventUpdate} onDelete={handleEventDelete} />

      <NationalHolidaysDialog open={showNationalHolidaysDialog} onOpenChange={setShowNationalHolidaysDialog} currentYear={currentYear} onSave={handleNationalHolidaysSave} existingHolidays={existingNationalHolidayIds} />
    </div>;
};
export default EventsPage;