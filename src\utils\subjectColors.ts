
// Generate consistent color for each subject based on subject name/id
export const getSubjectColor = (subject: any, categoryColor?: string, theme?: string) => {
  // Determine text color based on theme and background brightness
  const getTextColor = (bgColor: string) => {
    // For light backgrounds in light mode, use dark text
    // For dark backgrounds or dark mode, use white text
    const brightness = getBrightness(bgColor);
    if (theme === 'light' && brightness > 128) {
      return '#1F2937'; // Dark text for light backgrounds in light mode
    }
    return '#FFFFFF'; // White text for dark backgrounds or dark mode
  };

  // Priority 1: If subject has a custom color, use it
  if (subject?.color && subject.color !== '#6B7280') {
    return {
      background: subject.color,
      text: getTextColor(subject.color),
      border: adjustBrightness(subject.color, -20)
    };
  }

  // Priority 2: If category color is provided, use it
  if (categoryColor && categoryColor !== '#6B7280') {
    return {
      background: categoryColor,
      text: getTextColor(categoryColor),
      border: adjustBrightness(categoryColor, -20)
    };
  }

  // Priority 3: Generate color based on subject name for consistency
  const subjectName = subject?.name || subject?.id || 'default';
  const hash = hashString(subjectName);

  // Predefined color palette for better visual variety
  const colorPalette = [
    { bg: '#10B981', border: '#059669' }, // Emerald
    { bg: '#3B82F6', border: '#2563EB' }, // Blue
    { bg: '#F59E0B', border: '#D97706' }, // Amber
    { bg: '#8B5CF6', border: '#7C3AED' }, // Purple
    { bg: '#EF4444', border: '#DC2626' }, // Red
    { bg: '#06B6D4', border: '#0891B2' }, // Cyan
    { bg: '#84CC16', border: '#65A30D' }, // Lime
    { bg: '#F97316', border: '#EA580C' }, // Orange
    { bg: '#EC4899', border: '#DB2777' }, // Pink
    { bg: '#6366F1', border: '#4F46E5' }, // Indigo
    { bg: '#14B8A6', border: '#0F766E' }, // Teal
    { bg: '#A855F7', border: '#9333EA' }, // Violet
  ];

  const colorIndex = hash % colorPalette.length;
  const selectedColor = colorPalette[colorIndex];

  return {
    background: selectedColor.bg,
    text: getTextColor(selectedColor.bg),
    border: selectedColor.border
  };
};

// Simple hash function for consistent color mapping
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

// Helper function to adjust brightness
function adjustBrightness(hex: string, percent: number): string {
  const num = parseInt(hex.replace("#", ""), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

// Helper function to calculate brightness of a color
function getBrightness(hex: string): number {
  const num = parseInt(hex.replace("#", ""), 16);
  const R = (num >> 16) & 255;
  const G = (num >> 8) & 255;
  const B = num & 255;
  // Calculate perceived brightness using the luminance formula
  return (R * 299 + G * 587 + B * 114) / 1000;
}

// Fallback function for category-based colors (for backward compatibility)
export const getSubjectColorByCategory = (category: string, theme?: string) => {
  const getTextColor = (bgColor: string) => {
    const brightness = getBrightness(bgColor);
    if (theme === 'light' && brightness > 128) {
      return '#1F2937';
    }
    return '#FFFFFF';
  };

  switch (category?.toLowerCase()) {
    case 'kbm':
    case 'akademik':
    case 'wajib':
      return {
        background: '#10B981',
        text: getTextColor('#10B981'),
        border: '#059669'
      };
    case 'ekskul':
    case 'ekstrakurikuler':
    case 'pilihan':
      return {
        background: '#3B82F6',
        text: getTextColor('#3B82F6'),
        border: '#2563EB'
      };
    case 'muatan lokal':
    case 'mulok':
      return {
        background: '#F59E0B',
        text: getTextColor('#F59E0B'),
        border: '#D97706'
      };
    default:
      return {
        background: '#8B5CF6',
        text: getTextColor('#8B5CF6'),
        border: '#7C3AED'
      };
  }
};

export const getSubjectColorHex = (subject: any): string => {
  return getSubjectColor(subject).background;
};

// ✅ NEW: Get consistent color for subject with category fallback
export const getSubjectColorWithCategory = (subject: any, category?: any) => {
  // If subject has custom color, use it
  if (subject?.color && subject.color !== '#6B7280') {
    return getSubjectColor(subject);
  }

  // If category has color, use category color for consistency
  if (category?.color && category.color !== '#6B7280') {
    return getSubjectColor(subject, category.color);
  }

  // Fallback to subject-based color generation
  return getSubjectColor(subject);
};

// ✅ NEW: Get category color with fallback
export const getCategoryColor = (category: any) => {
  if (category?.color && category.color !== '#6B7280') {
    return {
      background: category.color,
      text: '#FFFFFF',
      border: adjustBrightness(category.color, -20)
    };
  }

  // Generate color based on category name
  const categoryName = category?.name || 'default';
  const hash = hashString(categoryName);

  const colorPalette = [
    { bg: '#10B981', border: '#059669' }, // Emerald
    { bg: '#3B82F6', border: '#2563EB' }, // Blue
    { bg: '#F59E0B', border: '#D97706' }, // Amber
    { bg: '#8B5CF6', border: '#7C3AED' }, // Purple
    { bg: '#EF4444', border: '#DC2626' }, // Red
    { bg: '#06B6D4', border: '#0891B2' }, // Cyan
    { bg: '#84CC16', border: '#65A30D' }, // Lime
    { bg: '#F97316', border: '#EA580C' }, // Orange
  ];

  const colorIndex = hash % colorPalette.length;
  const selectedColor = colorPalette[colorIndex];

  return {
    background: selectedColor.bg,
    text: '#FFFFFF',
    border: selectedColor.border
  };
};
