import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock } from 'lucide-react';
import { useActiveSemester } from '@/hooks/useSemesters';
import { getSemesterDescription } from '@/types/semester';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

interface SemesterBadgeProps {
  className?: string;
  showDescription?: boolean;
  showDateRange?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const SemesterBadge: React.FC<SemesterBadgeProps> = ({
  className = '',
  showDescription = true,
  showDateRange = false,
  size = 'md'
}) => {
  const { data: activeSemester, isLoading } = useActiveSemester();

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-xs px-2 py-1';
      case 'lg':
        return 'text-base px-4 py-2';
      default:
        return 'text-sm px-3 py-1.5';
    }
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse bg-gray-700/50 rounded-full h-6 w-24 ${className}`}></div>
    );
  }

  if (!activeSemester) {
    return (
      <Badge 
        variant="secondary" 
        className={`bg-gray-700/50 text-gray-400 border-gray-600/30 ${getSizeClasses()} ${className}`}
      >
        <Calendar className="h-3 w-3 mr-1" />
        Tidak ada semester aktif
      </Badge>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge 
        className={`bg-gradient-to-r from-purple-500/20 to-cyan-500/20 text-white border-purple-400/30 ${getSizeClasses()}`}
      >
        <Calendar className="h-3 w-3 mr-1" />
        {activeSemester.name}
      </Badge>
      
      {showDescription && (
        <Badge 
          variant="outline" 
          className={`bg-gray-800/50 text-gray-300 border-gray-600/30 ${getSizeClasses()}`}
        >
          <Clock className="h-3 w-3 mr-1" />
          {getSemesterDescription(activeSemester.semester_number)}
        </Badge>
      )}

      {showDateRange && (
        <Badge 
          variant="outline" 
          className={`bg-gray-800/50 text-gray-300 border-gray-600/30 ${getSizeClasses()}`}
        >
          {format(new Date(activeSemester.start_date), 'MMM yyyy', { locale: id })} - {format(new Date(activeSemester.end_date), 'MMM yyyy', { locale: id })}
        </Badge>
      )}

      {activeSemester.academic_year && (
        <Badge 
          variant="outline" 
          className={`bg-gray-800/50 text-gray-300 border-gray-600/30 ${getSizeClasses()}`}
        >
          {activeSemester.academic_year.year_name}
        </Badge>
      )}
    </div>
  );
};

export default SemesterBadge;
