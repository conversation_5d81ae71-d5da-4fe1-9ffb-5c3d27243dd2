import React, { useState, useRef, useMemo, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import dayGridPlugin from '@fullcalendar/daygrid';
import idLocale from '@fullcalendar/core/locales/id';
import { Card, CardContent } from '@/components/ui/card';
import { useClasses } from '@/hooks/useClasses';
import { useSchedulesComplete } from '@/hooks/useSchedulesPaginated';
import { useSchedulesSimple } from '@/hooks/useSchedulesSimple';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { useSchools } from '@/hooks/useSchools';
import { useTimeSessions } from '@/hooks/useTimeSessions';
import { useAcademicContext } from '@/hooks/useAcademicContext';
import { useToast } from '@/hooks/use-toast';
import { ExternalScheduleHeaderControls } from './ExternalScheduleHeaderControls';
import { WeeklyScrollableNavigation } from './WeeklyScrollableNavigation';
import { useTimeSlots } from '@/hooks/useTimeSlots';

const ExternalView = () => {
  const [selectedClassId, setSelectedClassId] = useState<string>('');
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [shareableLink, setShareableLink] = useState<string>('');

  // ✅ Parse URL parameters for shareable links
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const classParam = urlParams.get('class');
    const weekParam = urlParams.get('week');

    if (classParam) {
      setSelectedClassId(classParam);
    }
    if (weekParam) {
      const weekNumber = parseInt(weekParam, 10);
      if (weekNumber >= 1 && weekNumber <= 24) {
        setSelectedWeek(weekNumber);
      }
    }
  }, []);
  
  const calendarRef = useRef<FullCalendar>(null);
  
  const { data: classes = [] } = useClasses();
  const { academicWeeks } = useAcademicWeeks();
  const { data: currentSchool } = useSchools();
  const { data: timeSessions = [] } = useTimeSessions();
  const { academicPeriodDisplay } = useAcademicContext();
  const { timeSlots } = useTimeSlots();

  // ✅ CRITICAL DEBUG: Check academic weeks data
  console.log('📅 CRITICAL Academic Weeks Debug:', {
    academicWeeksCount: academicWeeks?.length || 0,
    academicWeeksSample: academicWeeks?.slice(0, 5),
    selectedWeek,
    currentWeekData: academicWeeks?.find(w => w.weekNumber === selectedWeek),
    allWeekNumbers: academicWeeks?.map(w => w.weekNumber)
  });

  // ✅ SMART FALLBACK: Create academic weeks that work with current data
  const fallbackAcademicWeeks = useMemo(() => {
    if (academicWeeks && academicWeeks.length > 0) {
      console.log('✅ Using real academic weeks:', academicWeeks.length);
      return academicWeeks;
    }

    console.log('⚠️ No academic weeks found, creating smart fallback weeks');
    // Create fallback weeks starting from current academic year or Jan 2025
    const baseDate = new Date(2025, 0, 6); // Jan 6, 2025 (Monday)

    return Array.from({ length: 52 }, (_, i) => {
      const weekNumber = i + 1;
      const startDate = new Date(baseDate);
      startDate.setDate(baseDate.getDate() + (i * 7));

      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);

      return {
        weekNumber,
        startDate,
        endDate,
        label: `Minggu ${weekNumber}`,
        dateRange: `${startDate.getDate()}/${startDate.getMonth() + 1} - ${endDate.getDate()}/${endDate.getMonth() + 1}`,
        isCurrentWeek: false
      };
    });
  }, [academicWeeks]);

  // ✅ TEST: Try simple hook first for debugging
  const { data: schedules = [], isLoading, error } = useSchedulesSimple();
  const { toast } = useToast();

  // ✅ CRITICAL DEBUG: Check exact data for week 2 and selected class
  console.log('🚨 CRITICAL DEBUG - External View Data Analysis:');
  console.log('📊 Basic Info:', {
    selectedWeek,
    selectedClassId,
    schedulesCount: schedules?.length || 0,
    isLoading,
    error: error?.message
  });

  // Check if we have data for week 2 specifically
  const week2Data = schedules?.filter(s => s.academic_week === 2);
  console.log('📅 Week 2 Data:', {
    week2Count: week2Data?.length || 0,
    week2Sample: week2Data?.slice(0, 3)
  });

  // Check if we have data for selected class
  const classData = schedules?.filter(s => s.class_id === selectedClassId);
  console.log('🏫 Selected Class Data:', {
    classDataCount: classData?.length || 0,
    classDataSample: classData?.slice(0, 3)
  });

  // Check intersection of week 2 AND selected class
  const week2ClassData = schedules?.filter(s => s.academic_week === 2 && s.class_id === selectedClassId);
  console.log('🎯 Week 2 + Selected Class Data:', {
    intersectionCount: week2ClassData?.length || 0,
    intersectionData: week2ClassData
  });

  // ✅ Generate shareable link for public access (NO LOGIN REQUIRED)
  useEffect(() => {
    const params = new URLSearchParams();
    if (selectedClassId) params.set('class', selectedClassId);
    params.set('week', selectedWeek.toString());

    const baseUrl = window.location.origin;
    // ✅ FIXED: Use public route that bypasses authentication
    setShareableLink(`${baseUrl}/public/schedule?${params.toString()}`);
  }, [selectedClassId, selectedWeek]);

  // ✅ STEP-BY-STEP FILTERING with detailed logging
  const filteredSchedules = useMemo(() => {
    console.log('🔥 STEP-BY-STEP FILTERING PROCESS:');
    console.log('Step 1 - Input data:', {
      schedulesLength: schedules?.length || 0,
      selectedWeek,
      selectedClassId,
      isLoading,
      error: error?.message
    });

    if (!schedules || schedules.length === 0) {
      console.log('❌ STEP 1 FAILED: No schedules data available');
      return [];
    }

    console.log('✅ STEP 1 PASSED: Schedules data available');

    // Step 2: Filter by week
    const weekFiltered = schedules.filter(s => s.academic_week === selectedWeek);
    console.log('Step 2 - Week filter:', {
      originalCount: schedules.length,
      weekFilteredCount: weekFiltered.length,
      selectedWeek,
      weekFilteredSample: weekFiltered.slice(0, 2)
    });

    if (weekFiltered.length === 0) {
      console.log('❌ STEP 2 FAILED: No data for selected week');
      return [];
    }

    console.log('✅ STEP 2 PASSED: Week data found');

    // Step 3: Filter by class (if selected)
    let classFiltered = weekFiltered;
    if (selectedClassId) {
      classFiltered = weekFiltered.filter(s => s.class_id === selectedClassId);
      console.log('Step 3 - Class filter:', {
        weekFilteredCount: weekFiltered.length,
        classFilteredCount: classFiltered.length,
        selectedClassId,
        classFilteredSample: classFiltered.slice(0, 2)
      });

      if (classFiltered.length === 0) {
        console.log('❌ STEP 3 FAILED: No data for selected class');
        return [];
      }

      console.log('✅ STEP 3 PASSED: Class data found');
    } else {
      console.log('⚠️ STEP 3 SKIPPED: No class selected');
    }

    // Step 4: Filter by required fields
    const finalFiltered = classFiltered.filter(s => s.start_time && s.end_time && s.subject_name);
    console.log('Step 4 - Required fields filter:', {
      classFilteredCount: classFiltered.length,
      finalFilteredCount: finalFiltered.length,
      finalFilteredSample: finalFiltered.slice(0, 2)
    });

    if (finalFiltered.length === 0) {
      console.log('❌ STEP 4 FAILED: No data with required fields');
      return [];
    }

    console.log('✅ STEP 4 PASSED: Final filtered data ready');
    console.log('🎯 FINAL RESULT:', {
      totalSteps: 4,
      finalCount: finalFiltered.length,
      finalData: finalFiltered
    });

    return finalFiltered;
  }, [schedules, selectedWeek, selectedClassId, isLoading, error]);

  // ✅ SIMPLIFIED: Calendar events conversion with better error handling
  const calendarEvents = useMemo(() => {
    console.log('🎯 Converting filtered schedules to calendar events:', {
      filteredCount: filteredSchedules?.length || 0,
      selectedWeek,
      academicWeeksCount: academicWeeks?.length || 0,
      fallbackWeeksCount: fallbackAcademicWeeks?.length || 0,
      usingFallback: !academicWeeks || academicWeeks.length === 0
    });

    if (!filteredSchedules || filteredSchedules.length === 0) {
      console.log('❌ No filtered schedules for calendar events');
      return [];
    }

    const currentWeek = fallbackAcademicWeeks.find(week => week.weekNumber === selectedWeek);
    if (!currentWeek) {
      console.log('❌ Current week not found:', selectedWeek, 'Available weeks:', fallbackAcademicWeeks.map(w => w.weekNumber));
      return [];
    }

    console.log('📅 Using week data:', {
      weekNumber: currentWeek.weekNumber,
      startDate: currentWeek.startDate,
      endDate: currentWeek.endDate
    });

    const events = filteredSchedules.map((schedule: any, index) => {
      try {
        const weekStartDate = new Date(currentWeek.startDate);
        const scheduleDate = new Date(weekStartDate);

        // Adjust for day of week (1=Monday, 7=Sunday)
        const dayOffset = schedule.day_of_week === 7 ? 0 : schedule.day_of_week;
        scheduleDate.setDate(weekStartDate.getDate() + dayOffset);

        const dateStr = scheduleDate.toISOString().split('T')[0];

        const event = {
          id: schedule.id,
          title: schedule.subject_name || 'Mata Pelajaran',
          start: `${dateStr}T${schedule.start_time}`,
          end: `${dateStr}T${schedule.end_time}`,
          backgroundColor: schedule.subject_color || '#6b7280',
          borderColor: '#ffffff',
          textColor: '#ffffff',
          extendedProps: {
            teacher: { full_name: schedule.teacher_name },
            class: { name: schedule.class_name },
            classroom: schedule.room
          }
        };

        console.log(`🎯 EVENT ${index + 1} CREATED:`, {
          id: event.id,
          title: event.title,
          start: event.start,
          end: event.end,
          backgroundColor: event.backgroundColor,
          originalSchedule: {
            subject_name: schedule.subject_name,
            day_of_week: schedule.day_of_week,
            start_time: schedule.start_time,
            end_time: schedule.end_time
          },
          calculatedDate: {
            weekStartDate: weekStartDate.toISOString(),
            scheduleDate: scheduleDate.toISOString(),
            dateStr,
            dayOffset
          }
        });

        return event;
      } catch (error) {
        console.error('❌ Error converting schedule to event:', error, schedule);
        return null;
      }
    }).filter(Boolean);

    console.log('✅ External Calendar events created:', {
      eventsCount: events.length,
      sampleEvents: events.slice(0, 2)
    });

    return events;
  }, [filteredSchedules, fallbackAcademicWeeks, selectedWeek]);

  // Calculate time range from time sessions
  const timeRange = useMemo(() => {
    if (!timeSessions || timeSessions.length === 0) {
      return { slotMinTime: '06:00:00', slotMaxTime: '18:00:00' };
    }

    const startTimes = timeSessions.map(session => session.start_time);
    const endTimes = timeSessions.map(session => session.end_time);

    const earliestStart = startTimes.sort()[0];
    const latestEnd = endTimes.sort().reverse()[0];

    return {
      slotMinTime: earliestStart || '06:00:00',
      slotMaxTime: latestEnd || '18:00:00'
    };
  }, [timeSessions]);

  const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);



  const copyShareableLink = async () => {
    try {
      await navigator.clipboard.writeText(shareableLink);
      toast({
        title: "✅ Link Berhasil Disalin",
        description: "Link jadwal telah disalin ke clipboard. Bagikan link ini untuk akses publik tanpa login.",
      });
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast({
        title: "❌ Gagal Menyalin Link",
        description: "Terjadi kesalahan saat menyalin link ke clipboard.",
        variant: "destructive",
      });
    }
  };

  const goToPreviousWeek = () => {
    if (selectedWeek > 1) {
      setSelectedWeek(selectedWeek - 1);
    }
  };

  const goToNextWeek = () => {
    if (selectedWeek < academicWeeks.length) {
      setSelectedWeek(selectedWeek + 1);
    }
  };

  const handleCurrentWeekClick = () => {
    const currentDate = new Date();
    const currentWeekData = academicWeeks.find(week => {
      const startDate = new Date(week.startDate);
      const endDate = new Date(week.endDate);
      return currentDate >= startDate && currentDate <= endDate;
    });

    if (currentWeekData) {
      setSelectedWeek(currentWeekData.weekNumber);
    } else {
      // Fallback to week 1 if current week not found
      setSelectedWeek(1);
    }
  };

  return (
    <div
      className="bg-gray-800/40 backdrop-blur-sm border border-gray-600/30 rounded-2xl overflow-hidden mx-auto"
      style={{
        maxWidth: '100%',
        transition: 'max-width 0.3s ease-in-out'
      }}
    >
      <div className="relative z-10 flex flex-col" style={{ minHeight: '800px' }}>
        <ExternalScheduleHeaderControls
          selectedClassId={selectedClassId}
          onClassChange={setSelectedClassId}
          selectedWeek={selectedWeek}
          onWeekSelect={setSelectedWeek}
          onCurrentWeekClick={handleCurrentWeekClick}
          onCopyLink={copyShareableLink}
        />

        <WeeklyScrollableNavigation
          selectedWeek={selectedWeek}
          onWeekSelect={setSelectedWeek}
        />

        {/* 🚀 LOADING STATE: Show loading indicator while fetching paginated data */}
        {isLoading && (
          <div className="flex items-center justify-center py-8 bg-gray-900/50 rounded-lg mx-4 mb-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span className="text-gray-300 text-sm">Memuat data jadwal dengan paginasi...</span>
            </div>
          </div>
        )}

        {/* 🚨 ERROR STATE: Show error if data fetching fails */}
        {error && (
          <div className="flex items-center justify-center py-8 bg-red-900/20 border border-red-500/30 rounded-lg mx-4 mb-4">
            <div className="text-center">
              <div className="text-red-400 text-sm mb-2">❌ Gagal memuat data jadwal</div>
              <div className="text-gray-400 text-xs">{error.message}</div>
            </div>
          </div>
        )}

        {/* Calendar Container */}
        <div className="flex-1 px-4 sm:px-6">
          <Card className="bg-card border-border shadow-sm">
          <CardContent className="p-6">
            <div style={{ minHeight: '600px' }}>
              <FullCalendar
                ref={calendarRef}
                plugins={[timeGridPlugin, dayGridPlugin]}
                initialView="timeGridWeek"
                headerToolbar={false}
                events={calendarEvents}
                editable={false}
                selectable={false}
                height="auto"
                locale={idLocale}
                slotMinTime={timeRange.slotMinTime}
                slotMaxTime={timeRange.slotMaxTime}
                slotDuration="00:30:00"
                slotLabelInterval="01:00:00"
                allDaySlot={false}
                weekends={true}
                dayHeaderFormat={{
                  weekday: 'short',
                  month: 'numeric',
                  day: 'numeric'
                }}
                eventContent={(eventInfo) => (
                  <div className="p-1 text-xs">
                    <div className="font-semibold truncate">{eventInfo.event.title}</div>
                    <div className="text-xs opacity-75 truncate">
                      {eventInfo.event.extendedProps.teacher?.full_name || 'Guru'}
                    </div>
                  </div>
                )}
              />
            </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ExternalView;
