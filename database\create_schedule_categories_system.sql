-- =====================================================
-- CREATE SCHEDULE CATEGORIES SYSTEM
-- =====================================================
-- Sistem kategori jadwal yang fleksibel dengan tabel database terpisah
-- Mengambil data KBM dan Ekskul dari menu MATA PELAJARAN
-- <PERSON><PERSON><PERSON> lain dapat ditambah secara manual

-- 1. Create table for schedule categories (different from subject categories)
CREATE TABLE IF NOT EXISTS public.schedule_categories (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT NOT NULL DEFAULT '#6B7280',
  description TEXT,
  is_system_category BOOLEAN NOT NULL DEFAULT false,
  school_id UUID NOT NULL,
  academic_year_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Constraints
  UNIQUE(name, school_id, academic_year_id)
);

-- 2. Create table for schedule subjects (mata pelajaran khusus jadwal)
CREATE TABLE IF NOT EXISTS public.schedule_subjects (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  code TEXT,
  color TEXT NOT NULL DEFAULT '#6B7280',
  total_hours_per_year INTEGER DEFAULT 0,
  standard_duration INTEGER DEFAULT 45,
  
  -- Relasi ke schedule_categories
  schedule_category_id UUID NOT NULL REFERENCES public.schedule_categories(id) ON DELETE CASCADE,
  
  -- Metadata sekolah dan tahun akademik
  school_id UUID NOT NULL,
  academic_year_id UUID NOT NULL,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Constraints
  UNIQUE(name, schedule_category_id, school_id, academic_year_id)
);

-- 3. Create table for schedule subject classes (relasi mata pelajaran jadwal dengan kelas)
CREATE TABLE IF NOT EXISTS public.schedule_subject_classes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  schedule_subject_id UUID NOT NULL REFERENCES public.schedule_subjects(id) ON DELETE CASCADE,
  class_id UUID NOT NULL REFERENCES public.classes(id) ON DELETE CASCADE,
  hours_per_week INTEGER DEFAULT 0,
  hours_per_year INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Constraints
  UNIQUE(schedule_subject_id, class_id)
);

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_schedule_categories_school_year ON public.schedule_categories(school_id, academic_year_id);
CREATE INDEX IF NOT EXISTS idx_schedule_categories_system ON public.schedule_categories(is_system_category);
CREATE INDEX IF NOT EXISTS idx_schedule_subjects_category ON public.schedule_subjects(schedule_category_id);
CREATE INDEX IF NOT EXISTS idx_schedule_subjects_school_year ON public.schedule_subjects(school_id, academic_year_id);
CREATE INDEX IF NOT EXISTS idx_schedule_subject_classes_subject ON public.schedule_subject_classes(schedule_subject_id);
CREATE INDEX IF NOT EXISTS idx_schedule_subject_classes_class ON public.schedule_subject_classes(class_id);

-- 5. Function to get user's school_id (if not exists)
CREATE OR REPLACE FUNCTION get_user_school_id(user_id UUID)
RETURNS UUID AS $$
DECLARE
  school_id UUID;
BEGIN
  SELECT profiles.school_id INTO school_id
  FROM profiles
  WHERE profiles.id = user_id;
  
  RETURN school_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Enable RLS
ALTER TABLE public.schedule_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.schedule_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.schedule_subject_classes ENABLE ROW LEVEL SECURITY;

-- 7. Create policies for schedule_categories
CREATE POLICY "Users can view schedule categories for their school" 
  ON public.schedule_categories 
  FOR SELECT 
  USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can create schedule categories for their school" 
  ON public.schedule_categories 
  FOR INSERT 
  WITH CHECK (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can update schedule categories for their school" 
  ON public.schedule_categories 
  FOR UPDATE 
  USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can delete custom schedule categories for their school" 
  ON public.schedule_categories 
  FOR DELETE 
  USING (school_id = get_user_school_id(auth.uid()) AND is_system_category = false);

-- 8. Create policies for schedule_subjects
CREATE POLICY "Users can view schedule subjects for their school" 
  ON public.schedule_subjects 
  FOR SELECT 
  USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can create schedule subjects for their school" 
  ON public.schedule_subjects 
  FOR INSERT 
  WITH CHECK (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can update schedule subjects for their school" 
  ON public.schedule_subjects 
  FOR UPDATE 
  USING (school_id = get_user_school_id(auth.uid()));

CREATE POLICY "Users can delete schedule subjects for their school" 
  ON public.schedule_subjects 
  FOR DELETE 
  USING (school_id = get_user_school_id(auth.uid()));

-- 9. Create policies for schedule_subject_classes
CREATE POLICY "Users can view schedule subject classes for their school" 
  ON public.schedule_subject_classes 
  FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.classes 
    WHERE classes.id = schedule_subject_classes.class_id 
    AND classes.school_id = get_user_school_id(auth.uid())
  ));

CREATE POLICY "Users can create schedule subject classes for their school" 
  ON public.schedule_subject_classes 
  FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.classes 
    WHERE classes.id = schedule_subject_classes.class_id 
    AND classes.school_id = get_user_school_id(auth.uid())
  ));

CREATE POLICY "Users can update schedule subject classes for their school" 
  ON public.schedule_subject_classes 
  FOR UPDATE 
  USING (EXISTS (
    SELECT 1 FROM public.classes 
    WHERE classes.id = schedule_subject_classes.class_id 
    AND classes.school_id = get_user_school_id(auth.uid())
  ));

CREATE POLICY "Users can delete schedule subject classes for their school" 
  ON public.schedule_subject_classes 
  FOR DELETE 
  USING (EXISTS (
    SELECT 1 FROM public.classes 
    WHERE classes.id = schedule_subject_classes.class_id 
    AND classes.school_id = get_user_school_id(auth.uid())
  ));

-- 10. Create function for updating updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 11. Add triggers for updated_at
CREATE TRIGGER update_schedule_categories_updated_at 
  BEFORE UPDATE ON public.schedule_categories 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedule_subjects_updated_at 
  BEFORE UPDATE ON public.schedule_subjects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedule_subject_classes_updated_at 
  BEFORE UPDATE ON public.schedule_subject_classes 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 12. Comments untuk dokumentasi
COMMENT ON TABLE public.schedule_categories IS 'Tabel kategori jadwal yang fleksibel, berbeda dari subject_categories';
COMMENT ON TABLE public.schedule_subjects IS 'Tabel mata pelajaran khusus untuk sistem jadwal';
COMMENT ON TABLE public.schedule_subject_classes IS 'Tabel relasi antara mata pelajaran jadwal dan kelas';
COMMENT ON COLUMN public.schedule_categories.is_system_category IS 'True untuk kategori sistem (KBM, Ekskul), false untuk kategori custom';
COMMENT ON COLUMN public.schedule_subjects.schedule_category_id IS 'Foreign key ke schedule_categories';

RAISE NOTICE 'Schedule categories system tables created successfully';
