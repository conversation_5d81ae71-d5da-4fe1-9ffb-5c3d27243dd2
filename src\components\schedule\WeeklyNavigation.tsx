import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
interface WeeklyNavigationProps {
  selectedWeek: number;
  onWeekSelect: (week: number) => void;
}
export const WeeklyNavigation: React.FC<WeeklyNavigationProps> = ({
  selectedWeek,
  onWeekSelect
}) => {
  const { academicWeeks } = useAcademicWeeks();
  const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);
  const goToPreviousWeek = () => {
    if (selectedWeek > 1) {
      onWeekSelect(selectedWeek - 1);
    }
  };
  const goToNextWeek = () => {
    if (selectedWeek < academicWeeks.length) {
      onWeekSelect(selectedWeek + 1);
    }
  };



  return <div className="flex items-center gap-3">
      {/* Week Navigation Controls */}
      <Button variant="outline" size="sm" onClick={goToPreviousWeek} disabled={selectedWeek <= 1} className="bg-background border-border text-foreground hover:bg-primary hover:text-primary-foreground flex items-center justify-center space-x-2">
        <ChevronLeft className="h-4 w-4" />
      </Button>

      <div className="text-center">
        <div className="text-bold text-foreground px-4 py-1">
          Minggu {selectedWeek}
        </div>
        {currentWeek && <div className="text-xs text-muted-foreground">
            {currentWeek.dateRange}
          </div>}
      </div>

      <Button variant="outline" size="sm" onClick={goToNextWeek} disabled={selectedWeek >= academicWeeks.length} className="bg-background border-border text-foreground hover:bg-primary hover:text-primary-foreground flex items-center justify-center space-x-2">
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>;
};