
import { useMemo } from 'react';
import { useAcademicYears } from './useAcademicYears';
import { addWeeks, format, startOfWeek, endOfWeek, isWithinInterval } from 'date-fns';
import { id } from 'date-fns/locale';

export const useAcademicWeeks = () => {
  const { data: academicYears } = useAcademicYears();
  
  const activeAcademicYear = useMemo(() => {
    return academicYears?.find(year => year.is_active);
  }, [academicYears]);

  const academicWeeks = useMemo(() => {
    if (!activeAcademicYear) return [];

    const startDate = new Date(activeAcademicYear.start_date);
    const endDate = new Date(activeAcademicYear.end_date);
    const weeks = [];
    
    let currentWeek = startOfWeek(startDate, { weekStartsOn: 1 }); // Start on Monday
    let weekNumber = 1;

    while (currentWeek <= endDate) {
      const weekEnd = endOfWeek(currentWeek, { weekStartsOn: 1 });
      
      weeks.push({
        weekNumber,
        startDate: currentWeek,
        endDate: weekEnd,
        label: `Minggu ${weekNumber}`,
        dateRange: `${format(currentWeek, 'dd MMM', { locale: id })} - ${format(weekEnd, 'dd MMM yyyy', { locale: id })}`,
        isCurrentWeek: isWithinInterval(new Date(), { start: currentWeek, end: weekEnd })
      });

      currentWeek = addWeeks(currentWeek, 1);
      weekNumber++;
    }

    return weeks;
  }, [activeAcademicYear]);

  return {
    academicWeeks,
    activeAcademicYear
  };
};
