// Supabase client configuration for production deployment
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Use environment variables for production deployment
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://tksnfarlivfrulbdkrid.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRrc25mYXJsaXZmcnVsYmRrcmlkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMTk1ODMsImV4cCI6MjA2Mzg5NTU4M30._LvaFvjYLVEk-omqPi0z3N2MogtFsvmerVxtFwRE6vE";

// Validate environment variables
if (!SUPABASE_URL || !SUPABASE_PUBLISHABLE_KEY) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUP<PERSON><PERSON>E_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});