import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { calculateDurationMinutes, calculateJP } from '@/utils/timeCalculations';

export interface JPProgressSimple {
  subject_id: string;
  subject_name: string;
  subject_code?: string;
  subject_color?: string;
  target_jp: number;
  realisasi_jp: number;
  progress_percentage: number;
  category: string;
}

export const useJPProgressSimple = (classId?: string) => {
  // ✅ DEBUG: Log hook call
  console.log('🚀 useJPProgressSimple hook called with:', {
    classId,
    type: typeof classId,
    isUndefined: classId === undefined,
    isEmpty: classId === ''
  });

  return useQuery({
    queryKey: ['jp-progress-simple', classId],
    queryFn: async (): Promise<JPProgressSimple[]> => {
      console.log('🔄 useJPProgressSimple - queryFn executing for classId:', classId);

      if (!classId) {
        console.log('❌ useJPProgressSimple - No classId provided, returning empty array');
        return [];
      }

      console.log('🔄 useJPProgressSimple - Starting query for classId:', classId);

      // Get current user's school_id and active academic year
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: profile } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('id', user.id)
        .single();

      if (!profile?.school_id) throw new Error('School not found');

      const { data: activeYear } = await supabase
        .from('academic_years')
        .select('id')
        .eq('school_id', profile.school_id)
        .eq('is_active', true)
        .single();

      if (!activeYear?.id) throw new Error('Active academic year not found');

      // ✅ FIXED: Get target JP from schedule_class_subjects (KBM subjects)
      const { data: targets, error: targetError } = await supabase
        .from('schedule_class_subjects')
        .select(`
          schedule_subject_id,
          hours_per_year,
          schedule_subjects (
            id,
            name,
            code,
            color,
            session_category_id,
            session_categories (
              name
            )
          )
        `)
        .eq('class_id', classId);

      if (targetError) {
        console.error('❌ Error fetching KBM targets:', targetError);
        throw targetError;
      }

      // ✅ NEW: Get target JP from extracurricular_classes (EKSKUL subjects)
      const { data: ekskulTargets, error: ekskulTargetError } = await supabase
        .from('extracurricular_classes')
        .select(`
          extracurricular_id,
          hours_per_year,
          extracurriculars (
            id,
            name,
            color,
            session_category_id,
            session_categories (
              name
            )
          )
        `)
        .eq('class_id', classId);

      if (ekskulTargetError) {
        console.error('❌ Error fetching EKSKUL targets:', ekskulTargetError);
        throw ekskulTargetError;
      }

      console.log('📊 KBM Targets fetched:', targets);
      console.log('🎨 EKSKUL Targets fetched:', ekskulTargets);

      // ✅ ENHANCED DEBUG: Log all subject IDs for comparison
      console.log('🔍 ALL SUBJECT IDs in targets:', {
        kbm_subject_ids: targets?.map(t => ({
          name: t.schedule_subjects?.name,
          id: t.schedule_subject_id
        })) || [],
        ekskul_subject_ids: ekskulTargets?.map(t => ({
          name: t.extracurriculars?.name,
          id: t.extracurricular_id
        })) || []
      });

      // ✅ FIXED: Get realisasi JP from class_schedules table (CORRECT TABLE)
      const { data: realisasi, error: realisasiError } = await supabase
        .from('class_schedules')
        .select(`
          id,
          subject_id,
          start_time,
          end_time,
          day_of_week,
          schedule_date
        `)
        .eq('class_id', classId)
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeYear.id)
        .not('day_of_week', 'is', null); // Only scheduled items (not targets)

      if (realisasiError) {
        console.error('❌ Error fetching realisasi:', realisasiError);
        throw realisasiError;
      }

      console.log('📊 Realisasi fetched:', realisasi);

      // ✅ ENHANCED DEBUG: Log all unique subject IDs in realisasi
      const uniqueSubjectIds = [...new Set(realisasi?.map(r => r.subject_id) || [])];
      console.log('🔍 UNIQUE SUBJECT IDs in realisasi:', uniqueSubjectIds);

      // ✅ ENHANCED DEBUG: Check for Aqidatuna and Nabiyuna specifically
      const aqidatunaRealisasi = realisasi?.filter(r => {
        // Check if subject_id matches any target
        const matchesKBM = targets?.some(t => t.schedule_subject_id === r.subject_id && t.schedule_subjects?.name === 'Aqidatuna');
        const matchesEKSKUL = ekskulTargets?.some(t => t.extracurricular_id === r.subject_id && t.extracurriculars?.name === 'Aqidatuna');
        return matchesKBM || matchesEKSKUL;
      }) || [];

      const nabiyunaRealisasi = realisasi?.filter(r => {
        // Check if subject_id matches any target
        const matchesKBM = targets?.some(t => t.schedule_subject_id === r.subject_id && t.schedule_subjects?.name === 'Nabiyuna');
        const matchesEKSKUL = ekskulTargets?.some(t => t.extracurricular_id === r.subject_id && t.extracurriculars?.name === 'Nabiyuna');
        return matchesKBM || matchesEKSKUL;
      }) || [];

      console.log('🔍 AQIDATUNA REALISASI FOUND:', aqidatunaRealisasi.length, aqidatunaRealisasi);
      console.log('🔍 NABIYUNA REALISASI FOUND:', nabiyunaRealisasi.length, nabiyunaRealisasi);

      // ✅ ENHANCED: Calculate progress for KBM subjects
      const kbmProgressData: JPProgressSimple[] = targets?.map(target => {
        // ✅ FIXED: Calculate actual JP based on duration, not session count
        const subjectSchedules = realisasi?.filter(r => r.subject_id === target.schedule_subject_id) || [];

        // ✅ ENHANCED DEBUG: Special logging for Aqidatuna and Nabiyuna
        if (target.schedule_subjects?.name === 'Aqidatuna' || target.schedule_subjects?.name === 'Nabiyuna') {
          console.log(`🔍 DETAILED DEBUG for ${target.schedule_subjects?.name}:`, {
            target_schedule_subject_id: target.schedule_subject_id,
            total_realisasi_records: realisasi?.length || 0,
            filtered_schedules: subjectSchedules.length,
            filtered_schedules_detail: subjectSchedules.map(s => ({
              id: s.id,
              subject_id: s.subject_id,
              start_time: s.start_time,
              end_time: s.end_time,
              schedule_date: s.schedule_date,
              day_of_week: s.day_of_week
            }))
          });
        }

        // Calculate total JP from all scheduled sessions for this subject
        const totalRealizationJP = subjectSchedules.reduce((total, schedule) => {
          if (!schedule.start_time || !schedule.end_time) return total;

          // Calculate duration in minutes using the utility function
          const durationMinutes = calculateDurationMinutes(schedule.start_time, schedule.end_time);

          // Convert to JP (default: 45 minutes = 1 JP)
          const jp = calculateJP(durationMinutes, 45);

          console.log(`📊 KBM JP calculation for ${target.schedule_subjects?.name}:`, {
            schedule_id: schedule.id || 'unknown',
            start_time: schedule.start_time,
            end_time: schedule.end_time,
            durationMinutes,
            jp,
            day_of_week: schedule.day_of_week,
            schedule_date: schedule.schedule_date
          });

          return total + jp;
        }, 0);

        const targetJP = target.hours_per_year || 0;
        const progressPercentage = targetJP > 0 ? (totalRealizationJP / targetJP) * 100 : 0;

        console.log(`✅ Final KBM JP calculation for ${target.schedule_subjects?.name}:`, {
          subject_id: target.schedule_subject_id,
          totalSchedules: subjectSchedules.length,
          totalRealizationJP: Math.round(totalRealizationJP * 100) / 100,
          targetJP,
          progressPercentage: Math.round(progressPercentage * 100) / 100
        });

        return {
          subject_id: target.schedule_subject_id,
          subject_name: target.schedule_subjects?.name || 'Unknown',
          subject_code: target.schedule_subjects?.code,
          subject_color: target.schedule_subjects?.color,
          target_jp: targetJP,
          realisasi_jp: Math.round(totalRealizationJP * 100) / 100, // Round to 2 decimal places
          progress_percentage: Math.min(Math.round(progressPercentage * 100) / 100, 100),
          category: target.schedule_subjects?.session_categories?.name || 'Unknown'
        };
      }) || [];

      // ✅ NEW: Calculate progress for EKSKUL subjects
      const ekskulProgressData: JPProgressSimple[] = ekskulTargets?.map(target => {
        // ✅ FIXED: Calculate actual JP based on duration for EKSKUL
        const subjectSchedules = realisasi?.filter(r => r.subject_id === target.extracurricular_id) || [];

        // ✅ ENHANCED DEBUG: Special logging for specific subjects
        if (target.extracurriculars?.name === 'Aqidatuna' || target.extracurriculars?.name === 'Nabiyuna') {
          console.log(`🔍 DETAILED DEBUG EKSKUL for ${target.extracurriculars?.name}:`, {
            target_extracurricular_id: target.extracurricular_id,
            total_realisasi_records: realisasi?.length || 0,
            filtered_schedules: subjectSchedules.length,
            filtered_schedules_detail: subjectSchedules.map(s => ({
              id: s.id,
              subject_id: s.subject_id,
              start_time: s.start_time,
              end_time: s.end_time,
              schedule_date: s.schedule_date,
              day_of_week: s.day_of_week
            }))
          });
        }

        // Calculate total JP from all scheduled sessions for this extracurricular
        const totalRealizationJP = subjectSchedules.reduce((total, schedule) => {
          if (!schedule.start_time || !schedule.end_time) return total;

          // Calculate duration in minutes using the utility function
          const durationMinutes = calculateDurationMinutes(schedule.start_time, schedule.end_time);

          // Convert to JP (default: 45 minutes = 1 JP)
          const jp = calculateJP(durationMinutes, 45);

          console.log(`🎨 EKSKUL JP calculation for ${target.extracurriculars?.name}:`, {
            schedule_id: schedule.id || 'unknown',
            start_time: schedule.start_time,
            end_time: schedule.end_time,
            durationMinutes,
            jp,
            day_of_week: schedule.day_of_week,
            schedule_date: schedule.schedule_date
          });

          return total + jp;
        }, 0);

        const targetJP = target.hours_per_year || 0;
        const progressPercentage = targetJP > 0 ? (totalRealizationJP / targetJP) * 100 : 0;

        console.log(`✅ Final EKSKUL JP calculation for ${target.extracurriculars?.name}:`, {
          subject_id: target.extracurricular_id,
          totalSchedules: subjectSchedules.length,
          totalRealizationJP: Math.round(totalRealizationJP * 100) / 100,
          targetJP,
          progressPercentage: Math.round(progressPercentage * 100) / 100
        });

        return {
          subject_id: target.extracurricular_id,
          subject_name: target.extracurriculars?.name || 'Unknown',
          subject_code: undefined, // EKSKUL doesn't have codes
          subject_color: target.extracurriculars?.color,
          target_jp: targetJP,
          realisasi_jp: Math.round(totalRealizationJP * 100) / 100, // Round to 2 decimal places
          progress_percentage: Math.min(Math.round(progressPercentage * 100) / 100, 100),
          category: target.extracurriculars?.session_categories?.name || 'EKSKUL'
        };
      }) || [];

      // ✅ COMBINE: Merge KBM and EKSKUL progress data
      const progressData = [...kbmProgressData, ...ekskulProgressData];

      console.log('✅ JP Progress calculated (simple) - COMBINED:', {
        total: progressData.length,
        kbm: kbmProgressData.length,
        ekskul: ekskulProgressData.length,
        data: progressData
      });

      return progressData;
    },
    enabled: !!classId,
    // ✅ DEBUG: Log enabled condition
    onSuccess: (data) => {
      console.log('✅ useJPProgressSimple - Query successful:', {
        classId,
        dataLength: data.length,
        data
      });
    },
    onError: (error) => {
      console.error('❌ useJPProgressSimple - Query error:', {
        classId,
        error
      });
    },
    staleTime: 5000, // ✅ FIXED: Reduced from 30s to 5s for more responsive updates
    gcTime: 300000,
  });
};
