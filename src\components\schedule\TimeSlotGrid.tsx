
import React from 'react';
import { startOfWeek, addDays } from 'date-fns';
import { useSchedules } from '@/hooks/useSchedules';
import { useTimeSlots } from '@/hooks/useTimeSlots';
import { useThirtyMinuteSlots } from '@/hooks/useThirtyMinuteSlots';
import { TimeColumn } from './TimeColumn';
import { DayColumn } from './DayColumn';

interface TimeSlotGridProps {
  selectedDate: Date | undefined;
  selectedWeek: number;
  selectedClassId: string | null;
  onTimeSlotClick: (timeSlot: any) => void;
  onScheduleEdit: (schedule: any) => void;
  onScheduleResize: (scheduleId: string, duration: number) => void;
}

export const TimeSlotGrid: React.FC<TimeSlotGridProps> = ({
  selectedDate,
  selectedWeek,
  selectedClassId,
  onTimeSlotClick,
  onScheduleEdit,
  onScheduleResize
}) => {
  const { data: schedules } = useSchedules();
  const { slots: timeSlots } = useTimeSlots(); // Destructure to get slots array
  const thirtyMinuteSlots = useThirtyMinuteSlots(); // 30-minute for day columns

  // Days including Sunday
  const days = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
  
  // Get week dates based on selectedWeek
  const getWeekDates = () => {
    if (!selectedDate) return [];
    const weekStart = startOfWeek(selectedDate, { weekStartsOn: 1 });
    return days.map((_, index) => addDays(weekStart, index));
  };

  const weekDates = getWeekDates();

  // Calculate total container height based on number of 30-minute time slots
  const containerHeight = 48 + (thirtyMinuteSlots.length * 30); // 48px for header + 24px per 30-min slot

  return (
    <div className="bg-gradient-to-br from-gray-900/95 via-gray-800/90 to-gray-900/95 backdrop-blur-xl border border-gray-600/30 rounded-lg shadow-2xl relative">
      <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-blue-500/5 to-purple-500/5"></div>
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400"></div>
      
      <div className="relative z-10">
        <div style={{ minHeight: `${containerHeight}px` }}>
          <div className="grid grid-cols-[60px_repeat(7,1fr)]" style={{ height: `${containerHeight}px` }}>
            {/* Time column - using hourly slots */}
            <TimeColumn timeSlots={timeSlots} />

            {/* Day columns - using 30-minute slots */}
            {days.map((day, dayIndex) => (
              <DayColumn
                key={day}
                day={day}
                dayIndex={dayIndex}
                timeSlots={thirtyMinuteSlots}
                weekDates={weekDates}
                selectedWeek={selectedWeek}
                selectedClassId={selectedClassId}
                schedules={schedules || []}
                onTimeSlotClick={onTimeSlotClick}
                onScheduleEdit={onScheduleEdit}
                onScheduleResize={onScheduleResize}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
