
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useCreateSessionCategory, useUpdateSessionCategory } from '@/hooks/useSessionCategories';

interface AddSessionCategoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingCategory?: any;
}

const colors = [
  '#6B7280', '#EF4444', '#F97316', '#EAB308', '#22C55E', 
  '#06B6D4', '#3B82F6', '#8B5CF6', '#EC4899', '#F59E0B'
];

const AddSessionCategoryModal: React.FC<AddSessionCategoryModalProps> = ({ 
  open, 
  onOpenChange, 
  editingCategory 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#6B7280'
  });

  const createCategory = useCreateSessionCategory();
  const updateCategory = useUpdateSessionCategory();

  useEffect(() => {
    if (editingCategory) {
      setFormData({
        name: editingCategory.name || '',
        description: editingCategory.description || '',
        color: editingCategory.color || '#6B7280',
      });
    } else {
      setFormData({
        name: '',
        description: '',
        color: '#6B7280'
      });
    }
  }, [editingCategory, open]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingCategory) {
      updateCategory.mutate(
        { id: editingCategory.id, ...formData },
        {
          onSuccess: () => {
            onOpenChange(false);
          }
        }
      );
    } else {
      createCategory.mutate(formData, {
        onSuccess: () => {
          onOpenChange(false);
        }
      });
    }
  };

  const isLoading = createCategory.isPending || updateCategory.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {editingCategory ? 'Edit Kategori Sesi' : 'Tambah Kategori Sesi Baru'}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name" >Nama Kategori</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              
              placeholder="Contoh: Pelajaran, Istirahat, Ekstrakulikuler"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description" >Deskripsi (Opsional)</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              
              placeholder="Deskripsi kategori sesi..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label >Warna</Label>
            <div className="grid grid-cols-5 gap-2">
              {colors.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-8 h-8 rounded-full transition-all ${ 
                    formData.color === color ? 'ring-2 ring-offset-2 ring-ring ring-offset-background' : 'hover:scale-110'
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setFormData(prev => ({ ...prev, color }))}
                />
              ))}
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)} 
              
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button 
              type="submit" 
              
              disabled={isLoading}
            >
              {isLoading ? 'Menyimpan...' : (editingCategory ? 'Update' : 'Simpan')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddSessionCategoryModal;
