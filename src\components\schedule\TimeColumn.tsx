import React from 'react';
interface TimeColumnProps {
  timeSlots: any[];
}
export const TimeColumn: React.FC<TimeColumnProps> = ({
  timeSlots
}) => {
  return <div className="bg-gray-800/60 backdrop-blur-sm border-r-2 border-gray-600/30 sticky left-0 z-20">
      <div className="h-12 border-b-2 border-gray-600/30 flex items-center justify-center bg-gray-800/80">
        <span className="font-semibold text-gray-300 text-sm">Waktu</span>
      </div>
      {timeSlots.map(timeSlot => <div key={timeSlot.id} className="h-12 border-b border-gray-600/30 flex items-center justify-center text-xs font-medium text-gray-300 bg-gray-800/40 hover:bg-gray-700/40 transition-colors">
          <div className="text-center font-semibold text-[10px]">
            {timeSlot.displayTime}
          </div>
        </div>)}
    </div>;
};