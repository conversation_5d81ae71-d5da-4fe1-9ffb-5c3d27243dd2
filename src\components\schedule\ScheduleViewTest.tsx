import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';

export const ScheduleViewTest: React.FC = () => {
  const [schedules, setSchedules] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testScheduleView = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔍 Testing schedule view for class 12.A...');

      // Test 1: Check if class_schedules query works (the problematic one)
      console.log('🔍 Testing class_schedules query...');
      const { data: testQuery, error: testError } = await supabase
        .from('class_schedules')
        .select(`
          *,
          classes(*),
          schedule_subjects(*)
        `)
        .is('day_of_week', null)
        .limit(1);

      if (testError) {
        console.error('❌ class_schedules query failed:', testError);
        // This is expected if the query is wrong
      } else {
        console.log('✅ class_schedules query works:', testQuery?.length);
      }

      // Test 2: Try the old problematic query to see if it still exists
      console.log('🔍 Testing old problematic query...');
      try {
        const { data: oldQuery, error: oldError } = await supabase
          .from('class_schedules')
          .select(`
            *,
            class:classes(*),
            subject:subjects(*)
          `)
          .is('day_of_week', null)
          .limit(1);

        if (oldError) {
          console.log('✅ Old query correctly fails:', oldError.message);
        } else {
          console.log('⚠️ Old query still works (this is the problem):', oldQuery?.length);
        }
      } catch (err) {
        console.log('✅ Old query correctly throws error:', err);
      }

      // Get class 12.A ID first
      const { data: classes, error: classError } = await supabase
        .from('classes')
        .select('id, name')
        .eq('name', '12.A')
        .single();

      if (classError) {
        throw new Error(`Class error: ${classError.message}`);
      }

      if (!classes) {
        throw new Error('Class 12.A not found');
      }

      console.log('✅ Found class 12.A:', classes);

      // Test schedules_view
      const { data: viewData, error: viewError } = await supabase
        .from('schedules_view')
        .select('*')
        .eq('class_id', classes.id)
        .not('day_of_week', 'is', null)
        .order('academic_week', { ascending: true })
        .order('day_of_week', { ascending: true })
        .order('start_time', { ascending: true })
        .limit(20);

      if (viewError) {
        console.error('❌ schedules_view error:', viewError);
        
        // Fallback to class_schedules
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('class_schedules')
          .select(`
            *,
            schedule_subjects (id, name, code, color, session_category_id),
            classes (id, name),
            teachers (id, full_name)
          `)
          .eq('class_id', classes.id)
          .not('day_of_week', 'is', null)
          .order('academic_week', { ascending: true })
          .order('day_of_week', { ascending: true })
          .order('start_time', { ascending: true })
          .limit(20);

        if (fallbackError) {
          throw new Error(`Fallback error: ${fallbackError.message}`);
        }

        setSchedules(fallbackData || []);
        console.log('✅ Fallback data loaded:', fallbackData?.length);
      } else {
        setSchedules(viewData || []);
        console.log('✅ View data loaded:', viewData?.length);
      }

    } catch (err: any) {
      console.error('❌ Test error:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const getDayName = (dayNumber: number) => {
    const days = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
    return days[dayNumber] || `Hari ${dayNumber}`;
  };

  return (
    <Card className="bg-gray-800/20 backdrop-blur-sm border-gray-700/30">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          📅 Schedule View Test - Class 12.A
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={testScheduleView}
          disabled={isLoading}
          className="bg-green-500 hover:bg-green-600"
        >
          {isLoading ? 'Testing...' : 'Test Schedule View'}
        </Button>

        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <p className="text-red-400 font-medium">Error:</p>
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        )}

        {schedules.length > 0 && (
          <div className="space-y-4">
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
              <p className="text-green-400 font-medium">
                ✅ Success! Found {schedules.length} schedules
              </p>
            </div>

            <div className="bg-gray-900/50 p-4 rounded-lg max-h-96 overflow-y-auto">
              <h3 className="text-white font-semibold mb-3">Schedule List:</h3>
              <div className="space-y-2">
                {schedules.map((schedule, index) => (
                  <div 
                    key={schedule.id || index}
                    className="bg-gray-800/50 p-3 rounded border border-gray-600/30"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">
                        {schedule.subject_name || schedule.schedule_subjects?.name || 'Unknown Subject'}
                      </span>
                      <span className="text-gray-400 text-sm">
                        Week {schedule.academic_week}
                      </span>
                    </div>
                    <div className="text-gray-300 text-sm">
                      {getDayName(schedule.day_of_week)} • {schedule.start_time} - {schedule.end_time}
                      {schedule.room && ` • ${schedule.room}`}
                    </div>
                    {schedule.session_category_name && (
                      <div className="text-gray-400 text-xs mt-1">
                        Category: {schedule.session_category_name}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {!isLoading && schedules.length === 0 && !error && (
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4">
            <p className="text-yellow-400">
              Click "Test Schedule View" to check if schedules are visible
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
