
import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCreateScheduleSubject, useUpdateScheduleSubject } from '@/hooks/useScheduleSubjects';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useSubjectCategories } from '@/hooks/useSubjectCategories';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';

interface AddSubjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingSubject?: any;
}

// Define color palette consistent with existing subject colors
const subjectColorPalette = [
  '#8B5CF6', // Purple
  '#F97316', // Orange
  '#3B82F6', // Blue
  '#22C55E', // Green
  '#06B6D4', // Cyan
  '#EC4899', // Pink
  '#EAB308', // Yellow
  '#EF4444', // Red
  '#84CC16', // Lime
  '#F59E0B', // Amber
  '#14B8A6', // Teal
  '#6366F1', // Indigo
];

const AddSubjectModal: React.FC<AddSubjectModalProps> = ({ open, onOpenChange, editingSubject }) => {
  const [selectedClasses, setSelectedClasses] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    schedule_category_id: '',
    color: subjectColorPalette[0],
    total_hours_per_year: '0',
    standard_duration: '45'
  });
  // Hooks
  const { data: sessionCategories = [] } = useSessionCategories();
  const createScheduleSubject = useCreateScheduleSubject();
  const updateScheduleSubject = useUpdateScheduleSubject();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (editingSubject) {
      setFormData({
        name: editingSubject.name || '',
        code: editingSubject.code || '',
        schedule_category_id: editingSubject.session_category_id || '',
        color: editingSubject.color || subjectColorPalette[0],
        total_hours_per_year: editingSubject.total_hours_per_year?.toString() || '0',
        standard_duration: editingSubject.standard_duration?.toString() || '45'
      });
    } else {
      setFormData({
        name: '',
        code: '',
        schedule_category_id: '',
        color: subjectColorPalette[0],
        total_hours_per_year: '0',
        standard_duration: '45'
      });
    }
  }, [editingSubject]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.code.trim()) {
      toast({
        title: "Error",
        description: "Nama dan kode mata pelajaran harus diisi",
        variant: "destructive",
      });
      return;
    }

    // Validate JP/Tahun
    const hoursPerYear = parseInt(formData.total_hours_per_year);
    if (isNaN(hoursPerYear) || hoursPerYear < 0) {
      toast({
        title: "Error",
        description: "JP/Tahun harus berupa angka yang valid (0 atau lebih)",
        variant: "destructive",
      });
      return;
    }

    console.log('Form data:', formData);

    try {
      const submitData = {
        name: formData.name.trim(),
        code: formData.code.trim(),
        color: formData.color,
        total_hours_per_year: hoursPerYear,
        standard_duration: parseInt(formData.standard_duration) || 45,
        schedule_category_id: formData.schedule_category_id,
        selected_classes: selectedClasses
      };

      if (editingSubject) {
        await updateScheduleSubject.mutateAsync({ id: editingSubject.id, ...submitData });
      } else {
        await createScheduleSubject.mutateAsync(submitData);
      }

      // ✅ ENHANCED: Force refresh all related queries after successful submission
      console.log('🔄 Modal: Starting aggressive cache refresh...');

      // Immediate invalidation
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] }),
        queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] }),
        queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['kbm-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['unified-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['subjects'] })
      ]);

      // Force immediate refetch
      setTimeout(async () => {
        console.log('🔄 Modal: Force refetching all queries...');
        await Promise.all([
          queryClient.refetchQueries({ queryKey: ['schedule-subjects'] }),
          queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] }),
          queryClient.refetchQueries({ queryKey: ['all-subjects-by-category'] }),
          queryClient.refetchQueries({ queryKey: ['matrix-subjects'] }),
          queryClient.refetchQueries({ queryKey: ['kbm-subjects'] }),
          queryClient.refetchQueries({ queryKey: ['unified-subjects'] })
        ]);
        console.log('✅ Modal: All queries refetched');
      }, 100);

      // Additional aggressive refresh
      setTimeout(() => {
        console.log('🔄 Modal: Additional refresh cycle...');
        queryClient.invalidateQueries();
        queryClient.refetchQueries({ queryKey: ['kbm-subjects'] });
        queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
      }, 500);

      onOpenChange(false);
    } catch (error) {
      console.error('Error in handleSubmit:', error);
    }
  };

  const isLoading = createScheduleSubject.isPending || updateScheduleSubject.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {editingSubject ? 'Edit Mata Pelajaran' : 'Tambah Mata Pelajaran Baru'}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Mata Pelajaran*</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Contoh: Matematika"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="code">Kode Mata Pelajaran*</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                placeholder="Contoh: MAT"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="schedule_category_id">Kategori Jadwal</Label>
              <Select value={formData.schedule_category_id} onValueChange={(value) => setFormData(prev => ({ ...prev, schedule_category_id: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih kategori sesi" />
                </SelectTrigger>
                <SelectContent>
                  {sessionCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        {category.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="standard_duration">Durasi Standar (menit)</Label>
              <Input
                id="standard_duration"
                type="number"
                value={formData.standard_duration}
                onChange={(e) => setFormData(prev => ({ ...prev, standard_duration: e.target.value }))}
                placeholder="45"
                min="1"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="total_hours_per_year">JP/Tahun*</Label>
            <Input
              id="total_hours_per_year"
              type="number"
              value={formData.total_hours_per_year}
              onChange={(e) => setFormData(prev => ({ ...prev, total_hours_per_year: e.target.value }))}
              placeholder="Contoh: 144 (atau 0 jika belum direncanakan)"
              min="0"
              required
            />
          </div>

          <p className="text-xs text-muted-foreground">
            Masukkan 0 jika belum ada perencanaan pasti. Nanti akan otomatis terhitung dari menu Jadwal.
          </p>

          <div className="space-y-2">
            <Label>Warna Mata Pelajaran</Label>
            <div className="grid grid-cols-8 gap-2">
              {subjectColorPalette.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-10 h-10 rounded-lg border-2 transition-all duration-200 hover:scale-105 ${
                    formData.color === color
                      ? 'border-foreground ring-2 ring-offset-2 ring-foreground'
                      : 'border-border hover:border-muted-foreground'
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setFormData(prev => ({ ...prev, color }))}
                  title={`Pilih warna ${color}`}
                />
              ))}
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <div
                className="w-4 h-4 rounded border"
                style={{ backgroundColor: formData.color }}
              />
              <span className="text-sm text-muted-foreground">Warna terpilih: {formData.color}</span>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)} 
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading}
            >
              {isLoading ? 'Menyimpan...' : (editingSubject ? 'Update' : 'Simpan')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddSubjectModal;
