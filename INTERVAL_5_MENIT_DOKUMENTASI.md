# ⏰ Dokumentasi Implementasi Interval 5 Menit

## 📋 **Ringkasan Implementasi**

Implementasi sistem interval waktu 5 menit untuk semua operasi drag and drop dan resize pada kalender jadwal, memastikan semua waktu snap ke kelipatan 5 menit terdekat.

## 🔧 **File yang Dibuat/Dimodifikasi**

### **1. File Baru: `src/utils/timeUtils.ts`**
Utility functions untuk menangani snap ke interval 5 menit:

#### **Core Functions:**
- `snapToFiveMinutes(time)` - Snap waktu ke interval 5 menit terdekat
- `snapDateToFiveMinutes(date)` - Snap Date object ke interval 5 menit
- `addDurationAndSnap(startTime, duration)` - Tambah durasi dan snap hasil
- `snapResizeToFiveMinutes()` - Handle resize dengan snap 5 menit
- `snapDragToFiveMinutes()` - <PERSON>le drag dengan snap 5 menit
- `hasTimeConflict()` - <PERSON><PERSON><PERSON> konflik jadwal setelah snap
- `logTimeOperation()` - Debug logging untuk operasi waktu

#### **Contoh Pengg<PERSON>an:**
```typescript
// Snap 08:07 menjadi 08:05
const snapped = snapToFiveMinutes("08:07"); // "08:05"

// Snap 08:08 menjadi 08:10  
const snapped2 = snapToFiveMinutes("08:08"); // "08:10"

// Tambah 45 menit dan snap
const endTime = addDurationAndSnap("08:05", 45); // "08:50"
```

### **2. Modified: `src/components/schedule/ScheduleCalendar.tsx`**

#### **Konfigurasi FullCalendar:**
```typescript
snapDuration="00:05:00"  // ✅ CHANGED: dari 00:01:00 ke 00:05:00
slotDuration="00:30:00"  // ✅ UNCHANGED: tetap 30 menit untuk grid
```

#### **Enhanced Handlers:**

##### **handleDrop (Drag dari Sidebar):**
```typescript
// ✅ NEW: Snap drop time ke interval 5 menit
const rawStartTime = dropTime.toTimeString().slice(0, 5);
const snappedStartTime = snapToFiveMinutes(rawStartTime);
const snappedEndTime = addDurationAndSnap(snappedStartTime, 45);

// ✅ NEW: Check konflik sebelum save
if (hasTimeConflict(schedules, scheduleData)) {
  toast({ title: "⚠️ Konflik Jadwal" });
  return;
}
```

##### **handleEventDrop (Move Event):**
```typescript
// ✅ NEW: Preserve durasi, snap start time
const originalDuration = calculateDurationMinutes(schedule.start_time, schedule.end_time);
const { startTime, endTime } = snapDragToFiveMinutes(originalDuration, rawNewStartTime);

// ✅ NEW: Check konflik dengan exclude current event
if (hasTimeConflict(schedules, conflictData, schedule.id)) {
  info.revert();
  return;
}
```

##### **handleEventResize (Resize Event):**
```typescript
// ✅ NEW: Keep start time, snap end time
const { startTime, endTime, duration } = snapResizeToFiveMinutes(
  schedule.start_time,
  schedule.end_time, 
  rawNewEndTime
);

// ✅ NEW: Minimum 5 menit duration
if (duration < 5) {
  endTime = addDurationAndSnap(startTime, 5);
}
```

##### **handleDateSelect (Calendar Selection):**
```typescript
// ✅ NEW: Snap selected time range
const snappedStartTime = snapToFiveMinutes(rawStartTime);
const snappedEndTime = snapToFiveMinutes(rawEndTime);
```

### **3. Modified: `src/hooks/useExternalDraggable.ts`**
```typescript
duration: '00:45:00' // ✅ CONSISTENT: 45 menit (9 x 5-menit intervals)
```

## 🎯 **Behavior yang Diimplementasikan**

### **1. Drag and Drop dari Sidebar ke Calendar:**
- **Input**: Drop pada 08:07
- **Process**: Snap ke 08:05 (terdekat)
- **Output**: start_time = 08:05, end_time = 08:50 (45 menit)

### **2. Event Resize pada Calendar:**
- **Input**: Resize dari 08:00-09:30 ke ~09:37
- **Process**: Snap end_time ke 09:35
- **Output**: start_time = 08:00, end_time = 09:35

### **3. Event Drag (Move) pada Calendar:**
- **Input**: Move jadwal 08:00-09:30 ke ~10:07
- **Process**: Snap start_time ke 10:05, preserve durasi 90 menit
- **Output**: start_time = 10:05, end_time = 11:35

### **4. Calendar Selection:**
- **Input**: Select time range 08:07-09:37
- **Process**: Snap both times
- **Output**: start_time = 08:05, end_time = 09:35

## 🔍 **Algoritma Snapping**

### **Snap Logic:**
```typescript
const remainder = minutes % 5;
if (remainder < 2.5) {
  // Round down: 08:07 → 08:05
  snappedMinutes = minutes - remainder;
} else {
  // Round up: 08:08 → 08:10
  snappedMinutes = minutes + (5 - remainder);
}
```

### **Examples:**
- 08:00 → 08:00 ✅ (sudah kelipatan 5)
- 08:01 → 08:00 ✅ (round down)
- 08:02 → 08:00 ✅ (round down)
- 08:03 → 08:05 ✅ (round up)
- 08:04 → 08:05 ✅ (round up)
- 08:05 → 08:05 ✅ (sudah kelipatan 5)

## ⚠️ **Validasi dan Error Handling**

### **1. Conflict Detection:**
```typescript
// Check overlap: (start1 < end2) && (start2 < end1)
if (hasTimeConflict(schedules, newSchedule, excludeId)) {
  toast({
    title: "⚠️ Konflik Jadwal",
    description: `Jadwal bertabrakan pada ${startTime}-${endTime}`
  });
  info.revert(); // Revert FullCalendar operation
  return;
}
```

### **2. Minimum Duration:**
```typescript
// Ensure minimum 5 minutes duration
if (duration < 5) {
  const adjustedEndTime = addDurationAndSnap(startTime, 5);
  return { startTime, endTime: adjustedEndTime, duration: 5 };
}
```

### **3. Database Validation:**
```typescript
// All times saved to database are in HH:MM format with 5-minute intervals
const isValid = isValidFiveMinuteInterval(time); // minutes % 5 === 0
```

## 🧪 **Testing Instructions**

### **Test 1: Drag and Drop dari Sidebar**
1. Drag mata pelajaran dari sidebar
2. Drop pada waktu non-kelipatan 5 (misal: 08:07)
3. **Expected**: Jadwal dibuat pada 08:05-08:50
4. **Verify**: Console log menunjukkan snapping process

### **Test 2: Event Resize**
1. Resize jadwal existing dengan drag bottom edge
2. Resize ke waktu non-kelipatan 5 (misal: 09:37)
3. **Expected**: End time snap ke 09:35
4. **Verify**: Start time tidak berubah

### **Test 3: Event Move (Drag)**
1. Drag jadwal existing ke slot waktu lain
2. Drop pada waktu non-kelipatan 5 (misal: 10:07)
3. **Expected**: Start time snap ke 10:05, durasi preserved
4. **Verify**: End time otomatis adjust

### **Test 4: Conflict Detection**
1. Buat jadwal pada 08:00-09:00
2. Coba drag jadwal lain ke 08:30-09:30
3. **Expected**: Error "Konflik Jadwal", operation di-revert
4. **Verify**: Toast notification muncul

### **Test 5: Calendar Selection**
1. Select time range dengan drag pada calendar
2. Select dari 08:07 ke 09:37
3. **Expected**: Modal terbuka dengan time 08:05-09:35
4. **Verify**: Time fields auto-populated dengan snapped values

## 🔧 **Debug Console Logs**

### **Drag & Drop Logging:**
```
⏰ Drag & Drop Time Snapping: {
  rawDropTime: "08:07",
  snappedStartTime: "08:05", 
  snappedEndTime: "08:50",
  subject: "Matematika",
  timestamp: "2024-01-15T10:30:00.000Z"
}
```

### **Event Drag Logging:**
```
⏰ Event Drag Time Snapping: {
  originalStartTime: "08:00",
  originalEndTime: "09:30",
  originalDuration: 90,
  rawNewStartTime: "10:07",
  snappedStartTime: "10:05",
  snappedEndTime: "11:35"
}
```

### **Event Resize Logging:**
```
⏰ Event Resize Time Snapping: {
  originalStartTime: "08:00",
  originalEndTime: "09:30", 
  rawNewEndTime: "09:37",
  snappedStartTime: "08:00",
  snappedEndTime: "09:35",
  newDuration: 95
}
```

## ✅ **Checklist Verifikasi**

### **Konfigurasi:**
- [ ] snapDuration = "00:05:00" ✅
- [ ] slotDuration = "00:30:00" (unchanged) ✅
- [ ] Default duration = 45 menit ✅

### **Operasi:**
- [ ] Drag from sidebar snaps to 5-min intervals ✅
- [ ] Event drag preserves duration, snaps start time ✅
- [ ] Event resize snaps end time, preserves start time ✅
- [ ] Calendar selection snaps both start and end times ✅

### **Validasi:**
- [ ] Conflict detection works correctly ✅
- [ ] Minimum 5-minute duration enforced ✅
- [ ] Database saves only 5-minute interval times ✅
- [ ] Error messages are user-friendly ✅

### **UX:**
- [ ] Operations feel smooth and predictable ✅
- [ ] Visual feedback is immediate ✅
- [ ] Revert works when conflicts occur ✅
- [ ] Toast notifications are informative ✅

## 🚀 **Benefits**

1. **⏰ Consistent Timing**: Semua waktu dalam kelipatan 5 menit
2. **🎯 Predictable Behavior**: User tahu waktu akan di-snap
3. **⚠️ Conflict Prevention**: Deteksi konflik otomatis
4. **📱 Better UX**: Operasi smooth dengan feedback jelas
5. **🔧 Maintainable**: Centralized time utilities
6. **📊 Data Integrity**: Database hanya berisi waktu valid

## 📝 **Future Enhancements**

1. **Custom Intervals**: Allow admin to configure snap interval
2. **Smart Snapping**: Different intervals for different time ranges
3. **Bulk Operations**: Snap multiple events at once
4. **Undo/Redo**: History of time operations
5. **Keyboard Shortcuts**: Quick time adjustments
6. **Visual Indicators**: Show snap targets on hover
