import React from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, getDay, addMonths, isSameMonth, isSameDay, startOfWeek, endOfWeek } from 'date-fns';
import { id } from 'date-fns/locale';

interface EffectiveDaysCalendarProps {
  year: number;
  nonEffectiveDaysSet: Set<string>;
  academicStartDate: Date;
  academicEndDate: Date;
}

const EffectiveDaysCalendar: React.FC<EffectiveDaysCalendarProps> = ({ year, nonEffectiveDaysSet, academicStartDate, academicEndDate }) => {
  const generateAcademicMonths = () => {
    const months = [];
    let currentDate = startOfMonth(academicStartDate);
    while (currentDate <= academicEndDate) {
      months.push(currentDate);
      currentDate = addMonths(currentDate, 1);
    }
    return months;
  };

  const months = generateAcademicMonths();

  const renderMonth = (month: Date) => {
    const monthStart = startOfMonth(month);
    const monthEnd = endOfMonth(month);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 1 }); // Start on Monday
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 1 });
    const days = eachDayOfInterval({ start: startDate, end: endDate });
    const weekDays = ['S', 'S', 'R', 'K', 'J', 'S', 'M'];

    return (
      <div key={month.toISOString()} className="bg-background p-4 rounded-lg border border-border">
        <h4 className="text-base font-semibold text-center mb-3 text-foreground">{format(month, 'MMMM yyyy', { locale: id })}</h4>
        <div className="grid grid-cols-7 gap-1 text-center text-xs text-muted-foreground font-mono">
          {['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'].map(day => <div key={day}>{day}</div>)}
        </div>
        <div className="grid grid-cols-7 gap-1 mt-2">
          {days.map(day => {
            const dateString = day.toISOString().split('T')[0];
            const isNonEffective = nonEffectiveDaysSet.has(dateString);
            const isCurrentMonth = isSameMonth(day, monthStart);

            return (
              <div
                key={day.toISOString()}
                className={`flex items-center justify-center h-8 w-8 rounded-lg text-xs transition-all duration-200 ${isCurrentMonth ? 'text-foreground' : 'text-muted-foreground/30'}
                  ${isNonEffective && isCurrentMonth ? 'bg-destructive/20 text-destructive-foreground' : ''}
                  ${!isNonEffective && isCurrentMonth ? '' : ''}`
                }
              >
                {format(day, 'd')}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {months.map(month => renderMonth(month))}
    </div>
  );
};

export default EffectiveDaysCalendar;
