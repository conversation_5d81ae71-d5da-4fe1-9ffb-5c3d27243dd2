// =====================================================
// UNIFIED SUBJECT SYSTEM HOOKS
// =====================================================
// Hooks for KBM (schedule_subjects) and EKSKUL (extracurriculars) management

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useAcademicYears } from '@/hooks/useAcademicYears';

export interface UnifiedSubject {
  id: string;
  name: string;
  code?: string;
  color: string;
  total_hours_per_year: number;
  session_category_id?: string;
  school_id: string;
  academic_year_id: string;
  created_at: string;
  updated_at: string;
  type: 'kbm' | 'ekskul';
  category_name?: string;
  category_color?: string;
  session_categories?: {
    id: string;
    name: string;
    color: string;
  };
}

export interface MatrixClassSubject {
  id: string;
  class_id: string;
  subject_id: string;
  hours_per_week: number;
  hours_per_year: number;
  created_at: string;
  updated_at: string;
}

// =====================================================
// FETCH KBM SUBJECTS (MATA PELAJARAN)
// =====================================================
export const useKBMSubjects = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useQuery({
    queryKey: ['kbm-subjects', profile?.school_id, activeYear?.id],
    queryFn: async (): Promise<UnifiedSubject[]> => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      console.log('🔍 Fetching KBM subjects for:', {
        school_id: profile.school_id,
        academic_year_id: activeYear.id,
        timestamp: new Date().toISOString()
      });

      const { data, error } = await supabase
        .from('schedule_subjects')
        .select(`
          id,
          name,
          code,
          color,
          total_hours_per_year,
          session_category_id,
          school_id,
          academic_year_id,
          created_at,
          updated_at,
          session_categories (
            id,
            name,
            color
          )
        `)
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeYear.id)
        .order('name', { ascending: true });

      if (error) {
        console.error('❌ Error fetching KBM subjects:', error);
        throw error;
      }

      console.log('📊 Raw schedule_subjects data:', {
        totalCount: data?.length || 0,
        data: data?.map(item => ({
          id: item.id,
          name: item.name,
          session_category_id: item.session_category_id,
          category_name: item.session_categories?.name
        }))
      });

      // Transform to unified format
      const transformedData = data?.map(item => ({
        ...item,
        type: 'kbm' as const,
        category_name: item.session_categories?.name,
        category_color: item.session_categories?.color
      })) || [];

      console.log('✅ KBM subjects transformed:', {
        totalCount: transformedData.length,
        subjects: transformedData.map(item => ({
          id: item.id,
          name: item.name,
          category_name: item.category_name,
          session_category_id: item.session_category_id
        }))
      });

      return transformedData;
    },
    enabled: !!profile?.school_id && !!activeYear?.id,
    staleTime: 0,
    gcTime: 0,
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

// =====================================================
// FETCH EKSKUL SUBJECTS (EKSTRAKURIKULER)
// =====================================================
export const useEkskulSubjects = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useQuery({
    queryKey: ['ekskul-subjects', profile?.school_id, activeYear?.id],
    queryFn: async (): Promise<UnifiedSubject[]> => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      console.log('Fetching EKSKUL subjects for:', {
        school_id: profile.school_id,
        academic_year_id: activeYear.id
      });

      const { data, error } = await supabase
        .from('extracurriculars')
        .select(`
          id,
          name,
          color,
          hours_per_year,
          session_category_id,
          school_id,
          academic_year_id,
          created_at,
          updated_at,
          session_categories (
            id,
            name,
            color
          )
        `)
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeYear.id)
        .order('name', { ascending: true });

      if (error) {
        console.error('Error fetching EKSKUL subjects:', error);
        throw error;
      }

      // Transform to unified format
      const transformedData = data?.map(item => ({
        id: item.id,
        name: item.name,
        code: undefined,
        color: item.color,
        total_hours_per_year: item.hours_per_year,
        session_category_id: item.session_category_id,
        school_id: item.school_id,
        academic_year_id: item.academic_year_id,
        created_at: item.created_at,
        updated_at: item.updated_at,
        type: 'ekskul' as const,
        category_name: item.session_categories?.name,
        category_color: item.session_categories?.color,
        session_categories: item.session_categories
      })) || [];

      console.log('EKSKUL subjects fetched:', transformedData);
      return transformedData;
    },
    enabled: !!profile?.school_id && !!activeYear?.id,
    staleTime: 0,
    gcTime: 0,
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

// =====================================================
// FETCH ALL SUBJECTS (KBM + EKSKUL) - UNIFIED VIEW
// =====================================================
export const useUnifiedSubjects = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useQuery({
    queryKey: ['unified-subjects', profile?.school_id, activeYear?.id],
    queryFn: async (): Promise<UnifiedSubject[]> => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      console.log('Fetching unified subjects for:', {
        school_id: profile.school_id,
        academic_year_id: activeYear.id
      });

      const { data, error } = await supabase
        .from('unified_subjects_view')
        .select('*')
        .eq('school_id', profile.school_id)
        .eq('academic_year_id', activeYear.id)
        .order('type', { ascending: true })
        .order('name', { ascending: true });

      if (error) {
        console.error('Error fetching unified subjects:', error);
        throw error;
      }

      console.log('Unified subjects fetched:', data);
      return data || [];
    },
    enabled: !!profile?.school_id && !!activeYear?.id,
    staleTime: 0,
    gcTime: 0,
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

// Alias for backward compatibility
export const useMatrixSubjects = useKBMSubjects;

// =====================================================
// FETCH CLASS-SUBJECT RELATIONSHIPS FOR MATRIX
// =====================================================
export const useMatrixClassSubjects = () => {
  const { profile } = useAuth();
  const { data: academicYears } = useAcademicYears();
  const activeYear = academicYears?.find(year => year.is_active);

  return useQuery({
    queryKey: ['matrix-class-subjects', profile?.school_id, activeYear?.id],
    queryFn: async (): Promise<MatrixClassSubject[]> => {
      if (!profile?.school_id || !activeYear?.id) {
        throw new Error('Missing required data');
      }

      console.log('Fetching matrix class subjects for:', {
        school_id: profile.school_id,
        academic_year_id: activeYear.id
      });

      try {
        // Simple query without complex relations to avoid schema cache issues
        const { data: classSubjects, error: classSubjectsError } = await supabase
          .from('schedule_class_subjects')
          .select('*');

        if (classSubjectsError) {
          console.error('Error fetching schedule_class_subjects:', classSubjectsError);
          // Return empty array instead of throwing to prevent app crash
          return [];
        }

        // Get all schedule_subjects for this school/year
        const { data: subjects, error: subjectsError } = await supabase
          .from('schedule_subjects')
          .select('id, school_id, academic_year_id')
          .eq('school_id', profile.school_id)
          .eq('academic_year_id', activeYear.id);

        if (subjectsError) {
          console.error('Error fetching schedule_subjects:', subjectsError);
          return [];
        }

        // Filter class subjects to only include those with subjects from this school/year
        const validSubjectIds = new Set(subjects?.map(s => s.id) || []);
        const filteredClassSubjects = classSubjects?.filter(cs =>
          validSubjectIds.has(cs.schedule_subject_id)
        ) || [];

        // Transform data to match expected interface
        const transformedData = filteredClassSubjects.map(item => ({
          id: item.id,
          class_id: item.class_id,
          subject_id: item.schedule_subject_id, // Map to subject_id for compatibility
          hours_per_week: item.hours_per_week || 0,
          hours_per_year: item.hours_per_year || 0,
          created_at: item.created_at,
          updated_at: item.updated_at
        }));

        console.log('Matrix class subjects fetched successfully:', {
          totalClassSubjects: classSubjects?.length || 0,
          validSubjects: subjects?.length || 0,
          filteredResults: transformedData.length
        });

        return transformedData;
      } catch (error) {
        console.error('Error in useMatrixClassSubjects:', error);
        // Return empty array to prevent app crash
        return [];
      }
    },
    enabled: !!profile?.school_id && !!activeYear?.id,
    staleTime: 0,
    gcTime: 0,
    retry: 1, // Reduce retry to prevent excessive error logs
    refetchOnWindowFocus: false, // Disable to reduce noise
    refetchOnMount: true,
  });
};

// =====================================================
// CREATE CLASS-SUBJECT ASSIGNMENT
// =====================================================
export const useCreateMatrixClassSubject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: { subject_id: string; class_id: string; hours_per_year: number }) => {
      console.log('Creating matrix class subject with data:', data);

      // Insert into schedule_class_subjects table
      const insertData = {
        schedule_subject_id: data.subject_id, // Map subject_id to schedule_subject_id
        class_id: data.class_id,
        hours_per_week: Math.round(data.hours_per_year / 36) || 0,
        hours_per_year: data.hours_per_year || 0
      };

      const { data: result, error } = await supabase
        .from('schedule_class_subjects')
        .insert(insertData)
        .select(`
          id,
          schedule_subject_id,
          class_id,
          hours_per_week,
          hours_per_year,
          created_at,
          updated_at
        `)
        .single();

      if (error) {
        console.error('Error creating matrix class subject:', error);
        throw error;
      }

      console.log('Matrix class subject created:', result);
      return result;
    },
    onSuccess: async () => {
      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      await queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['matrix-class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] });

      // ✅ ENHANCED: Force immediate refetch for instant UI updates
      queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
      queryClient.refetchQueries({ queryKey: ['matrix-class-subjects'] });
      queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });

      toast({
        title: "Berhasil",
        description: "Mata pelajaran berhasil ditambahkan ke kelas",
      });
    },
    onError: (error: any) => {
      console.error('Error in useCreateMatrixClassSubject:', error);
      toast({
        title: "Gagal",
        description: error?.message || 'Gagal menambahkan mata pelajaran ke kelas',
        variant: "destructive",
      });
    },
  });
};

// =====================================================
// UPDATE CLASS-SUBJECT ASSIGNMENT FOR MATRIX
// =====================================================
export const useUpdateMatrixClassSubject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, hours_per_year }: { id: string; hours_per_year: number }) => {
      console.log('🔄 Updating matrix class subject:', { id, hours_per_year });

      // ✅ FIXED: Update the correct table - schedule_class_subjects
      const { data: result, error } = await supabase
        .from('schedule_class_subjects')
        .update({
          hours_per_year,
          hours_per_week: Math.round(hours_per_year / 36) || 0
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating matrix class subject:', error);
        throw error;
      }

      console.log('✅ Matrix class subject updated successfully:', result);
      return result;
    },
    onSuccess: async () => {
      console.log('🔄 Matrix class subject update success - invalidating caches...');

      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['matrix-class-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] }),
        queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['subjects'] }),
        queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] })
      ]);

      // ✅ ENHANCED: Force immediate refetch for instant UI updates
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
        queryClient.refetchQueries({ queryKey: ['matrix-class-subjects'] });
        queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });
      }, 100);

      toast({
        title: "Berhasil",
        description: "JP berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      console.error('❌ Matrix class subject update failed:', error);
      toast({
        title: "Gagal",
        description: error?.message || 'Gagal memperbarui JP',
        variant: "destructive",
      });
    },
  });
};

// =====================================================
// DELETE CLASS-SUBJECT ASSIGNMENT
// =====================================================
export const useDeleteMatrixClassSubject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('schedule_class_subjects')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting matrix class subject:', error);
        throw error;
      }
    },
    onSuccess: async () => {
      // ✅ ENHANCED: Comprehensive cache invalidation for real-time updates
      await queryClient.invalidateQueries({ queryKey: ['matrix-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['matrix-class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['all-subjects-by-category'] });

      // ✅ ENHANCED: Force immediate refetch for instant UI updates
      queryClient.refetchQueries({ queryKey: ['matrix-subjects'] });
      queryClient.refetchQueries({ queryKey: ['matrix-class-subjects'] });
      queryClient.refetchQueries({ queryKey: ['schedule-subjects-by-category'] });

      toast({
        title: "Berhasil",
        description: "Assignment berhasil dihapus",
      });
    },
    onError: (error: any) => {
      console.error('Error in useDeleteMatrixClassSubject:', error);
      toast({
        title: "Gagal",
        description: error?.message || 'Gagal menghapus assignment',
        variant: "destructive",
      });
    },
  });
};
