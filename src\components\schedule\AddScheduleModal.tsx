
import React, { useState, useEffect, useMemo } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Clock, Check, ChevronsUpDown, Plus, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useScheduleSubjectsByCategory } from '@/hooks/useScheduleSubjectsByCategory';
import { useTeachers } from '@/hooks/useTeachers';
import { useClasses } from '@/hooks/useClasses';
import { useCreateSchedule } from '@/hooks/useSchedules';
import { useToast } from '@/hooks/use-toast';
import { NewAddSubjectModal } from './NewAddSubjectModal';

interface AddScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTimeSlot: any;
  selectedDate: Date | undefined;
  selectedClassId: string | null;
  onScheduleCreated?: () => void; // ✅ ADDED: Optional callback for schedule creation
}

export const AddScheduleModal: React.FC<AddScheduleModalProps> = ({
  isOpen,
  onClose,
  selectedTimeSlot,
  selectedDate,
  selectedClassId,
  onScheduleCreated // ✅ ADDED: Destructure the callback
}) => {
  const [formData, setFormData] = useState({
    subject_id: '',
    teacher_id: 'no-teacher',
    class_id: '',
    start_time: '',
    end_time: '',
    room: '',
    notes: '',
    tujuan_pembelajaran: '',
    materi_pembelajaran: ''
  });

  const [subjectSearchOpen, setSubjectSearchOpen] = useState(false);
  const [subjectSearchValue, setSubjectSearchValue] = useState('');
  const [showAddSubjectModal, setShowAddSubjectModal] = useState(false);

  const { data: scheduleSubjectsByCategory = [] } = useScheduleSubjectsByCategory(selectedClassId);
  const { data: teachers } = useTeachers();
  const { data: classes } = useClasses();

  // ✅ FIXED: Use only schedule_subjects (following "+ Mapel" modal pattern)
  const allSubjects = React.useMemo(() => {
    const subjectsFromCategories = scheduleSubjectsByCategory.flatMap(category =>
      category.subjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        code: subject.code,
        category: category.name
      }))
    );

    console.log('🔍 AddScheduleModal - Schedule subjects only:', {
      fromCategories: subjectsFromCategories.length,
      categories: scheduleSubjectsByCategory.length,
      subjects: subjectsFromCategories.map(s => ({ name: s.name, category: s.category }))
    });

    return subjectsFromCategories;
  }, [scheduleSubjectsByCategory]);
  const createSchedule = useCreateSchedule();
  const { toast } = useToast();

  useEffect(() => {
    // Auto-populate form when modal opens
    if (isOpen) {
      const newFormData: any = {
        subject_id: '',
        teacher_id: 'no-teacher',
        class_id: selectedClassId || '', // ✅ Auto-populate kelas dari filter header
        start_time: '',
        end_time: '',
        room: '',
        notes: '',
        tujuan_pembelajaran: '',
        materi_pembelajaran: ''
      };

      // ✅ Auto-populate subject jika dari drag & drop
      if (selectedTimeSlot?.subject) {
        newFormData.subject_id = selectedTimeSlot.subject.id;
      }

      // ✅ Auto-populate waktu dari time slot atau drag & drop
      if (selectedTimeSlot) {
        // Dari FullCalendar select/click
        if (selectedTimeSlot.start_time && selectedTimeSlot.end_time) {
          newFormData.start_time = selectedTimeSlot.start_time;
          newFormData.end_time = selectedTimeSlot.end_time;
        }
        // Dari time slot grid
        else if (selectedTimeSlot.timeSlot?.time) {
          const [startTime, endTime] = selectedTimeSlot.timeSlot.time.split(' - ');
          newFormData.start_time = startTime;
          newFormData.end_time = endTime;
        }
        // Dari time slot individual
        else if (selectedTimeSlot.time) {
          newFormData.start_time = selectedTimeSlot.time;
          // Default end time 1 hour later
          const startDate = new Date(`2000-01-01 ${selectedTimeSlot.time}`);
          const endDate = new Date(startDate.getTime() + 60 * 60 * 1000);
          newFormData.end_time = endDate.toTimeString().slice(0, 5);
        }
      }

      setFormData(newFormData);

      console.log('🎯 Auto-populated form data:', {
        selectedClassId,
        selectedTimeSlot,
        formData: newFormData
      });
    }
  }, [isOpen, selectedTimeSlot, selectedClassId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // ✅ FIXED: Remove teacher_id requirement - allow null teacher
    if (!selectedTimeSlot || !formData.subject_id || !formData.class_id) {
      return;
    }

    // Validate time if both are provided
    if (formData.start_time && formData.end_time) {
      if (formData.start_time >= formData.end_time) {
        toast({
          title: "Validasi Gagal",
          description: "Waktu mulai harus lebih awal dari waktu selesai",
          variant: "destructive",
        });
        return;
      }
    }

    try {
      const scheduleData = {
        ...formData,
        // ✅ FIXED: Convert empty string or "no-teacher" to null for database compatibility
        teacher_id: (formData.teacher_id === '' || formData.teacher_id === 'no-teacher') ? null : formData.teacher_id,
        time_session_id: selectedTimeSlot.timeSlot?.id || null,
        day_of_week: selectedTimeSlot.dayIndex,
        // Use custom time if provided, otherwise use time slot time
        start_time: formData.start_time || selectedTimeSlot.timeSlot?.time?.split(' - ')[0] || null,
        end_time: formData.end_time || selectedTimeSlot.timeSlot?.time?.split(' - ')[1] || null,
        academic_week: selectedTimeSlot.academicWeek,
        schedule_date: selectedTimeSlot.date ? selectedTimeSlot.date.toISOString().split('T')[0] : null,
      };

      console.log('🚀 Creating schedule with data:', scheduleData);

      await createSchedule.mutateAsync(scheduleData);

      console.log('✅ Schedule created successfully');

      toast({
        title: "Berhasil",
        description: "Jadwal berhasil ditambahkan",
      });

      // ✅ ADDED: Call the callback if provided
      if (onScheduleCreated) {
        onScheduleCreated();
      }

      onClose();
      setFormData({
        subject_id: '',
        teacher_id: 'no-teacher',
        class_id: '',
        start_time: '',
        end_time: '',
        room: '',
        notes: '',
        tujuan_pembelajaran: '',
        materi_pembelajaran: ''
      });
    } catch (error) {
      console.error('Error creating schedule:', error);
      toast({
        title: "Gagal",
        description: "Gagal menambahkan jadwal. Silakan coba lagi.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white">Tambah Jadwal Pelajaran</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {selectedTimeSlot && (
            <div className="p-3 bg-gray-700/50 rounded-lg border border-gray-500">
              <div className="flex items-center gap-2 text-sm text-gray-200">
                <Clock className="h-4 w-4 text-gray-400" />
                <span className="font-medium">{selectedTimeSlot.day}</span>
                {selectedTimeSlot.timeSlot && (
                  <>
                    <span className="text-gray-400">•</span>
                    <span>{selectedTimeSlot.timeSlot.time}</span>
                  </>
                )}
                <span className="text-gray-400">•</span>
                <span>Minggu {selectedTimeSlot.academicWeek}</span>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-6">
            {/* Kolom Kiri - Field Utama */}
            <div className="space-y-4">
              {/* Mata Pelajaran dengan Search */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-white">Mata Pelajaran</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAddSubjectModal(true)}
                    className="text-xs bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Buat Baru
                  </Button>
                </div>

                {allSubjects.length === 0 ? (
                  <div className="p-4 bg-gray-700/50 rounded-lg border border-gray-500 text-center">
                    <AlertCircle className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                    <p className="text-gray-300 text-sm mb-3">Belum ada mata pelajaran tersedia</p>
                    <Button
                      type="button"
                      onClick={() => setShowAddSubjectModal(true)}
                      className="bg-lime-500 hover:bg-lime-600 text-white"
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Buat Mata Pelajaran Pertama
                    </Button>
                  </div>
                ) : (
                  <Popover open={subjectSearchOpen} onOpenChange={setSubjectSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={subjectSearchOpen}
                        className="w-full justify-between bg-gray-800 border-gray-600 text-white hover:bg-gray-700"
                      >
                        {formData.subject_id
                          ? allSubjects.find((subject) => subject.id === formData.subject_id)?.name
                          : "Ketik untuk mencari mata pelajaran..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0 bg-gray-800 border-gray-700">
                      <Command className="bg-gray-800">
                        <CommandInput
                          placeholder="Ketik nama mata pelajaran..."
                          className="text-white"
                          value={subjectSearchValue}
                          onValueChange={setSubjectSearchValue}
                        />
                        <CommandList>
                          <CommandEmpty className="text-gray-400 p-4 text-center">
                            <div>
                              <p className="mb-2">Mata pelajaran tidak ditemukan.</p>
                              <Button
                                type="button"
                                onClick={() => {
                                  setSubjectSearchOpen(false);
                                  setShowAddSubjectModal(true);
                                }}
                                className="bg-lime-500 hover:bg-lime-600 text-white"
                                size="sm"
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Buat Mata Pelajaran Baru
                              </Button>
                            </div>
                          </CommandEmpty>
                          <CommandGroup>
                            {allSubjects
                              .filter(subject =>
                                subject.name.toLowerCase().includes(subjectSearchValue.toLowerCase()) ||
                                (subject.code && subject.code.toLowerCase().includes(subjectSearchValue.toLowerCase()))
                              )
                              .map((subject) => (
                              <CommandItem
                                key={subject.id}
                                value={subject.name}
                                onSelect={() => {
                                  setFormData(prev => ({ ...prev, subject_id: subject.id }));
                                  setSubjectSearchOpen(false);
                                  setSubjectSearchValue('');
                                }}
                                className="text-white hover:bg-gray-700"
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    formData.subject_id === subject.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                <div className="flex items-center justify-between w-full">
                                  <span>{subject.name} {subject.code && `(${subject.code})`}</span>
                                  <span className="text-xs text-gray-400 ml-2">{subject.category}</span>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                )}
              </div>

              {/* Guru Pengajar */}
              <div>
                <Label htmlFor="teacher" className="text-white">Guru Pengajar</Label>
                <Select value={formData.teacher_id} onValueChange={(value) => setFormData(prev => ({ ...prev, teacher_id: value }))}>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Pilih guru" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-700">
                    <SelectItem value="no-teacher" className="text-gray-400 hover:bg-gray-700">
                      Tidak ada guru
                    </SelectItem>
                    {teachers?.map((teacher) => (
                      <SelectItem key={teacher.id} value={teacher.id} className="text-white hover:bg-gray-700">
                        {teacher.full_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Kelas - Hidden jika sudah ada selectedClassId dari header */}
              {!selectedClassId ? (
                <div>
                  <Label htmlFor="class" className="text-white">Kelas</Label>
                  <Select value={formData.class_id} onValueChange={(value) => setFormData(prev => ({ ...prev, class_id: value }))}>
                    <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                      <SelectValue placeholder="Pilih kelas" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      {classes?.map((classItem) => (
                        <SelectItem key={classItem.id} value={classItem.id} className="text-white hover:bg-gray-700">
                          {classItem.name} - {classItem.level}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                <div>
                  <Label className="text-white">Kelas</Label>
                  <div className="bg-gray-800 border border-gray-600 rounded-md px-3 py-2 text-white">
                    {classes?.find(c => c.id === selectedClassId)?.name} - {classes?.find(c => c.id === selectedClassId)?.level}
                  </div>
                </div>
              )}

              {/* Waktu Kegiatan */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start_time" className="text-white">Waktu Mulai</Label>
                  <Input
                    id="start_time"
                    type="time"
                    value={formData.start_time}
                    onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
                    className="bg-gray-800 border-gray-600 text-white"
                    placeholder="HH:MM"
                  />
                </div>

                <div>
                  <Label htmlFor="end_time" className="text-white">Waktu Selesai</Label>
                  <Input
                    id="end_time"
                    type="time"
                    value={formData.end_time}
                    onChange={(e) => setFormData(prev => ({ ...prev, end_time: e.target.value }))}
                    className="bg-gray-800 border-gray-600 text-white"
                    placeholder="HH:MM"
                  />
                </div>
              </div>

              {/* Ruangan */}
              <div>
                <Label htmlFor="room" className="text-white">Ruangan</Label>
                <Input
                  id="room"
                  value={formData.room}
                  onChange={(e) => setFormData(prev => ({ ...prev, room: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="Masukkan nama ruangan"
                />
              </div>
            </div>

            {/* Kolom Kanan - Field Pembelajaran */}
            <div className="space-y-4">
              {/* Catatan Guru */}
              <div>
                <Label htmlFor="notes" className="text-white">Catatan Guru</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white resize-none"
                  placeholder="Tambahkan catatan khusus"
                  rows={4}
                />
              </div>

              {/* Tujuan Pembelajaran */}
              <div>
                <Label htmlFor="tujuan_pembelajaran" className="text-white">Tujuan Pembelajaran</Label>
                <Textarea
                  id="tujuan_pembelajaran"
                  value={formData.tujuan_pembelajaran}
                  onChange={(e) => setFormData(prev => ({ ...prev, tujuan_pembelajaran: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white resize-none"
                  placeholder="Masukkan tujuan pembelajaran"
                  rows={4}
                />
              </div>

              {/* Materi Pembelajaran */}
              <div>
                <Label htmlFor="materi_pembelajaran" className="text-white">Materi Pembelajaran</Label>
                <Textarea
                  id="materi_pembelajaran"
                  value={formData.materi_pembelajaran}
                  onChange={(e) => setFormData(prev => ({ ...prev, materi_pembelajaran: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white resize-none"
                  placeholder="Masukkan materi pembelajaran"
                  rows={4}
                />
              </div>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Batal
            </Button>
            <Button
              type="submit"
              disabled={createSchedule.isPending || !formData.subject_id || !formData.class_id}
              className="bg-lime-500 hover:bg-lime-600"
            >
              {createSchedule.isPending ? 'Menyimpan...' : 'Simpan'}
            </Button>
          </div>
        </form>

        {/* Modal untuk menambah mata pelajaran baru */}
        <NewAddSubjectModal
          isOpen={showAddSubjectModal}
          onClose={() => setShowAddSubjectModal(false)}
          selectedClassId={selectedClassId}
        />
      </DialogContent>
    </Dialog>
  );
};
