// =====================================================
// TYPES FOR NEW SCHEDULE SUBJECTS SYSTEM
// =====================================================

export interface ScheduleSubject {
  id: string;
  name: string;
  code?: string; // Optional
  color: string;
  total_hours_per_year: number;
  standard_duration: number; // dalam menit
  
  // Relasi
  session_category_id?: string;
  school_id: string;
  academic_year_id: string;
  
  // Metadata
  created_at: string;
  updated_at: string;
  
  // Relasi data (untuk query dengan join)
  session_category?: {
    id: string;
    name: string;
    color: string;
    description?: string;
  };
}

export interface ScheduleClassSubject {
  id: string;
  class_id: string;
  schedule_subject_id: string;
  hours_per_week: number;
  hours_per_year: number;
  
  // Metadata
  created_at: string;
  updated_at: string;
  
  // Relasi data (untuk query dengan join)
  class?: {
    id: string;
    name: string;
    level: string;
    grade: number;
    capacity?: number;
    school_id: string;
    academic_year_id: string;
  };
  schedule_subject?: ScheduleSubject;
}

// Interface untuk form input
export interface CreateScheduleSubjectData {
  name: string;
  code?: string;
  color: string;
  total_hours_per_year: number;
  standard_duration?: number;
  session_category_id: string;
}

export interface CreateScheduleClassSubjectData {
  class_id: string;
  schedule_subject_id: string;
  hours_per_week?: number;
  hours_per_year: number;
}

// Interface untuk response dengan relasi
export interface ScheduleSubjectWithRelations extends ScheduleSubject {
  session_category: {
    id: string;
    name: string;
    color: string;
    description?: string;
  };
  class_subjects: ScheduleClassSubject[];
}

// Interface untuk kategori dengan subjects
export interface CategoryWithScheduleSubjects {
  id: string;
  name: string;
  color: string;
  description?: string;
  subjects: ScheduleSubjectItem[];
}

export interface ScheduleSubjectItem {
  id: string;
  name: string;
  code?: string;
  color: string;
  total_hours_per_year: number;
  session_category_id?: string;
  type: 'schedule_subject';
}

// Interface untuk update
export interface UpdateScheduleSubjectData {
  name?: string;
  code?: string;
  color?: string;
  total_hours_per_year?: number;
  standard_duration?: number;
  session_category_id?: string;
}

export interface UpdateScheduleClassSubjectData {
  hours_per_week?: number;
  hours_per_year?: number;
}

// Enum untuk mode pemilihan kelas
export enum ClassSelectionMode {
  SINGLE = 'single',
  MULTIPLE = 'multiple',
  ALL = 'all'
}

// Interface untuk form modal
export interface ScheduleSubjectFormData {
  name: string;
  code: string;
  color: string;
  total_hours_per_year: number;
  standard_duration: number;
  session_category_id: string;
  class_selection_mode: ClassSelectionMode;
  selected_classes: string[];
}
