import React, { useState } from 'react';
import { Plus, Trash2, Edit2, GraduationCap, School, Users, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useEducationLevels, useCreateEducationLevel, useUpdateEducationLevel, useDeleteEducationLevel } from '@/hooks/useEducationLevels';
export const EducationLevelsTab: React.FC = () => {
  const [newEducationLevel, setNewEducationLevel] = useState({
    name: '',
    code: '',
    min_grade: '',
    max_grade: ''
  });
  const [editingLevel, setEditingLevel] = useState<any>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const {
    data: educationLevels
  } = useEducationLevels();
  const createEducationLevel = useCreateEducationLevel();
  const updateEducationLevel = useUpdateEducationLevel();
  const deleteEducationLevel = useDeleteEducationLevel();
  const handleAddEducationLevel = () => {
    if (newEducationLevel.name && newEducationLevel.code && newEducationLevel.min_grade && newEducationLevel.max_grade) {
      createEducationLevel.mutate({
        name: newEducationLevel.name,
        code: newEducationLevel.code,
        min_grade: parseInt(newEducationLevel.min_grade),
        max_grade: parseInt(newEducationLevel.max_grade),
        is_active: true
      });
      setNewEducationLevel({
        name: '',
        code: '',
        min_grade: '',
        max_grade: ''
      });
    }
  };
  const handleEditEducationLevel = (level: any) => {
    setEditingLevel({
      ...level,
      min_grade: level.min_grade.toString(),
      max_grade: level.max_grade.toString()
    });
    setIsEditDialogOpen(true);
  };
  const handleUpdateEducationLevel = () => {
    if (editingLevel) {
      updateEducationLevel.mutate({
        id: editingLevel.id,
        name: editingLevel.name,
        code: editingLevel.code,
        min_grade: parseInt(editingLevel.min_grade),
        max_grade: parseInt(editingLevel.max_grade)
      });
      setIsEditDialogOpen(false);
      setEditingLevel(null);
    }
  };
  const handleDeleteEducationLevel = (id: string) => {
    deleteEducationLevel.mutate(id);
  };
  return <Card className="bg-card backdrop-blur-sm border-border">
      <CardContent className="space-y-6 p-6">
        {/* Existing Education Levels */}
        <div className="space-y-3">
          {educationLevels?.map(level => <div key={level.id} className="flex items-center justify-between p-4 bg-muted/20 rounded-lg border border-border">
              <div className="flex items-center space-x-3">
                
                <div>
                  <p className="text-foreground font-medium flex items-center space-x-2">
                    <BookOpen className="h-4 w-4 text-purple-400" />
                    <span>{level.name} ({level.code})</span>
                  </p>
                  <p className="text-muted-foreground text-sm flex items-center space-x-2">
                    <Users className="h-3 w-3 text-cyan-400" />
                    <span>Kelas {level.min_grade} - {level.max_grade}</span>
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="ghost" className="text-blue-400 hover:bg-blue-400/10" onClick={() => handleEditEducationLevel(level)}>
                  <Edit2 className="h-4 w-4" />
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button size="sm" variant="ghost" className="text-red-400 hover:bg-red-400/10">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-background border-border">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-foreground">Hapus Jenjang Pendidikan</AlertDialogTitle>
                      <AlertDialogDescription className="text-muted-foreground">
                        Apakah Anda yakin ingin menghapus jenjang "{level.name}"? Tindakan ini tidak dapat dibatalkan dan akan menghapus semua tingkat kelas yang terkait.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel className="bg-background border-border text-foreground hover:bg-accent">
                        Batal
                      </AlertDialogCancel>
                      <AlertDialogAction className="bg-red-500 hover:bg-red-600 text-white" onClick={() => handleDeleteEducationLevel(level.id)}>
                        Hapus
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>)}
        </div>
        
        {/* Add New Education Level */}
        <div className="border-t border-border pt-6 space-y-4">
          <div className="flex items-center space-x-3">
            <Plus className="h-5 w-5 text-lime-400" />
            <h3 className="text-lg font-medium text-foreground">
              Tambah Jenjang Baru
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input placeholder="Nama Jenjang (contoh: SMA)" value={newEducationLevel.name} onChange={e => setNewEducationLevel(prev => ({
            ...prev,
            name: e.target.value
          }))} className="bg-background border-border text-foreground placeholder-muted-foreground" />
            <Input placeholder="Kode (contoh: SMA)" value={newEducationLevel.code} onChange={e => setNewEducationLevel(prev => ({
            ...prev,
            code: e.target.value
          }))} className="bg-background border-border text-foreground placeholder-muted-foreground" />
            <Input placeholder="Kelas Minimum (contoh: 10)" type="number" value={newEducationLevel.min_grade} onChange={e => setNewEducationLevel(prev => ({
            ...prev,
            min_grade: e.target.value
          }))} className="bg-background border-border text-foreground placeholder-muted-foreground" />
            <Input placeholder="Kelas Maksimum (contoh: 12)" type="number" value={newEducationLevel.max_grade} onChange={e => setNewEducationLevel(prev => ({
            ...prev,
            max_grade: e.target.value
          }))} className="bg-background border-border text-foreground placeholder-muted-foreground" />
          </div>
          <Button onClick={handleAddEducationLevel} className="w-full bg-lime-500 hover:bg-lime-600 text-white" disabled={createEducationLevel.isPending}>
            <Plus className="h-4 w-4 mr-2" />
            Tambah Jenjang
          </Button>
        </div>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="bg-background border-border">
            <DialogHeader>
              <DialogTitle className="text-foreground flex items-center space-x-2">
                <Edit2 className="h-5 w-5 text-blue-400" />
                <span>Edit Jenjang Pendidikan</span>
              </DialogTitle>
            </DialogHeader>
            {editingLevel && <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input placeholder="Nama Jenjang" value={editingLevel.name} onChange={e => setEditingLevel(prev => ({
              ...prev,
              name: e.target.value
            }))} className="bg-background border-border text-foreground placeholder-muted-foreground" />
                <Input placeholder="Kode" value={editingLevel.code} onChange={e => setEditingLevel(prev => ({
              ...prev,
              code: e.target.value
            }))} className="bg-background border-border text-foreground placeholder-muted-foreground" />
                <Input placeholder="Kelas Minimum" type="number" value={editingLevel.min_grade} onChange={e => setEditingLevel(prev => ({
              ...prev,
              min_grade: e.target.value
            }))} className="bg-background border-border text-foreground placeholder-muted-foreground" />
                <Input placeholder="Kelas Maksimum" type="number" value={editingLevel.max_grade} onChange={e => setEditingLevel(prev => ({
              ...prev,
              max_grade: e.target.value
            }))} className="bg-background border-border text-foreground placeholder-muted-foreground" />
              </div>}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} className="bg-background border-border text-foreground hover:bg-accent">
                Batal
              </Button>
              <Button onClick={handleUpdateEducationLevel} className="bg-blue-500 hover:bg-blue-600 text-white" disabled={updateEducationLevel.isPending}>
                Simpan
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>;
};