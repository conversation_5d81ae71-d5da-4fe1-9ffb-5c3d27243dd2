import React from 'react';
import { getSubjectColorWithCategory } from '@/utils/subjectColors';

interface DayColumnProps {
  day: string;
  dayIndex: number;
  timeSlots: any[];
  weekDates: Date[];
  selectedWeek: number;
  selectedClassId: string | null;
  schedules: any[];
  onTimeSlotClick: (timeSlot: any) => void;
  onScheduleEdit: (schedule: any) => void;
  onScheduleResize: (scheduleId: string, duration: number) => void;
}

export const DayColumn: React.FC<DayColumnProps> = ({
  day,
  dayIndex,
  timeSlots,
  weekDates,
  selectedWeek,
  selectedClassId,
  schedules,
  onTimeSlotClick,
  onScheduleEdit,
  onScheduleResize
}) => {
  // dayIndex: 0=Senin, 1=Selasa, ..., 6=Minggu (for array access)
  // day_of_week: 1=Senin, 2=Selasa, ..., 7=Minggu (database value)
  const days = ['Senin', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu', '<PERSON><PERSON>'];

  // Get schedules for this day
  const daySchedules = schedules.filter(schedule => 
    schedule.day_of_week === dayIndex + 1 && 
    schedule.academic_week === selectedWeek &&
    (!selectedClassId || schedule.class_id === selectedClassId)
  );

  const handleTimeSlotClick = (timeSlot: any) => {
    onTimeSlotClick({
      day: days[dayIndex],
      dayIndex: dayIndex + 1,
      timeSlot,
      date: weekDates[dayIndex],
      academicWeek: selectedWeek
    });
  };

  const getScheduleForTimeSlot = (timeSlot: any) => {
    return daySchedules.find(schedule => {
      const scheduleStart = schedule.start_time;
      const scheduleEnd = schedule.end_time;
      const slotTime = timeSlot.time;
      
      return slotTime >= scheduleStart && slotTime < scheduleEnd;
    });
  };

  const calculateScheduleHeight = (schedule: any) => {
    const startTime = schedule.start_time;
    const endTime = schedule.end_time;
    
    // Convert time to minutes
    const startMinutes = parseInt(startTime.split(':')[0]) * 60 + parseInt(startTime.split(':')[1]);
    const endMinutes = parseInt(endTime.split(':')[0]) * 60 + parseInt(endTime.split(':')[1]);
    
    // Calculate duration in 30-minute slots
    const durationMinutes = endMinutes - startMinutes;
    const slots = durationMinutes / 30;
    
    return slots * 24; // 24px per 30-minute slot
  };

  const calculateScheduleTop = (schedule: any, timeSlots: any[]) => {
    const startTime = schedule.start_time;
    const firstSlotTime = timeSlots[0]?.time || '07:00';
    
    // Convert times to minutes
    const startMinutes = parseInt(startTime.split(':')[0]) * 60 + parseInt(startTime.split(':')[1]);
    const firstSlotMinutes = parseInt(firstSlotTime.split(':')[0]) * 60 + parseInt(firstSlotTime.split(':')[1]);
    
    // Calculate offset in 30-minute slots
    const offsetMinutes = startMinutes - firstSlotMinutes;
    const slots = offsetMinutes / 30;
    
    return 48 + (slots * 24); // 48px for header + 24px per 30-minute slot
  };

  return (
    <div className="border-r border-gray-600/30 relative">
      {/* Day header */}
      <div className="h-12 border-b-2 border-gray-600/30 flex items-center justify-center bg-gray-800/60 backdrop-blur-sm">
        <span className="font-semibold text-gray-300 text-sm">{day}</span>
      </div>

      {/* Time slots */}
      <div className="relative">
        {timeSlots.map((timeSlot, index) => {
          const schedule = getScheduleForTimeSlot(timeSlot);
          
          return (
            <div
              key={timeSlot.id}
              className="h-6 border-b border-gray-600/30 hover:bg-gray-700/30 transition-colors cursor-pointer relative"
              onClick={() => handleTimeSlotClick(timeSlot)}
            >
              {/* Show schedule if it starts at this time slot */}
              {schedule && timeSlot.time === schedule.start_time && (
                <div
                  className="absolute inset-x-1 rounded-lg border-2 transition-all duration-200 cursor-pointer hover:scale-[1.02] hover:shadow-lg z-10"
                  style={{
                    backgroundColor: getSubjectColorWithCategory(schedule.subjects?.category?.name || 'default').background,
                    borderColor: getSubjectColorWithCategory(schedule.subjects?.category?.name || 'default').border,
                    borderLeftWidth: '6px',
                    borderLeftColor: getSubjectColorWithCategory(schedule.subjects?.category?.name || 'default').border,
                    height: `${calculateScheduleHeight(schedule)}px`,
                    top: 0,
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onScheduleEdit(schedule);
                  }}
                >
                  <div className="p-2 h-full flex flex-col justify-between relative">
                    <div className="flex-1 min-h-0">
                      <div 
                        className="font-semibold text-xs truncate mb-1" 
                        style={{ color: getSubjectColorWithCategory(schedule.subjects?.category?.name || 'default').text }}
                      >
                        {schedule.subjects?.name}
                      </div>
                      <div 
                        className="text-[10px] opacity-90 truncate" 
                        style={{ color: getSubjectColorWithCategory(schedule.subjects?.category?.name || 'default').text }}
                      >
                        {schedule.teachers?.full_name || 'Belum ada guru'}
                      </div>
                      {!selectedClassId && schedule.classes?.name && (
                        <div 
                          className="text-[9px] opacity-80 truncate mt-1" 
                          style={{ color: getSubjectColorWithCategory(schedule.subjects?.category?.name || 'default').text }}
                        >
                          {schedule.classes.name}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between text-[9px] opacity-80 mt-1">
                      <span style={{ color: getSubjectColorWithCategory(schedule.subjects?.category?.name || 'default').text }}>
                        {schedule.start_time} - {schedule.end_time}
                      </span>
                      {schedule.room && (
                        <span style={{ color: getSubjectColorWithCategory(schedule.subjects?.category?.name || 'default').text }}>
                          {schedule.room}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
