export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      academic_years: {
        Row: {
          created_at: string
          end_date: string
          id: string
          is_active: boolean
          school_id: string
          start_date: string
          updated_at: string
          year_name: string
        }
        Insert: {
          created_at?: string
          end_date: string
          id?: string
          is_active?: boolean
          school_id: string
          start_date: string
          updated_at?: string
          year_name: string
        }
        Update: {
          created_at?: string
          end_date?: string
          id?: string
          is_active?: boolean
          school_id?: string
          start_date?: string
          updated_at?: string
          year_name?: string
        }
        Relationships: [
          {
            foreignKeyName: "academic_years_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      class_subjects: {
        Row: {
          class_id: string
          created_at: string
          hours_per_week: number
          hours_per_year: number
          id: string
          subject_id: string
          updated_at: string
        }
        Insert: {
          class_id: string
          created_at?: string
          hours_per_week?: number
          hours_per_year?: number
          id?: string
          subject_id: string
          updated_at?: string
        }
        Update: {
          class_id?: string
          created_at?: string
          hours_per_week?: number
          hours_per_year?: number
          id?: string
          subject_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "class_subjects_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "classes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_subjects_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      classes: {
        Row: {
          academic_year_id: string
          capacity: number | null
          created_at: string
          grade: number
          id: string
          level: string
          name: string
          school_id: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          capacity?: number | null
          created_at?: string
          grade: number
          id?: string
          level: string
          name: string
          school_id: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          capacity?: number | null
          created_at?: string
          grade?: number
          id?: string
          level?: string
          name?: string
          school_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "classes_academic_year_id_fkey"
            columns: ["academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "classes_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      education_levels: {
        Row: {
          code: string
          created_at: string
          id: string
          is_active: boolean
          max_grade: number
          min_grade: number
          name: string
          school_id: string
          updated_at: string
        }
        Insert: {
          code: string
          created_at?: string
          id?: string
          is_active?: boolean
          max_grade: number
          min_grade: number
          name: string
          school_id: string
          updated_at?: string
        }
        Update: {
          code?: string
          created_at?: string
          id?: string
          is_active?: boolean
          max_grade?: number
          min_grade?: number
          name?: string
          school_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "education_levels_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      event_categories: {
        Row: {
          academic_year_id: string
          color: string
          created_at: string
          id: string
          name: string
          school_id: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          color: string
          created_at?: string
          id?: string
          name: string
          school_id: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          color?: string
          created_at?: string
          id?: string
          name?: string
          school_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      events: {
        Row: {
          academic_year_id: string
          cancels_lessons: boolean | null
          created_at: string
          description: string | null
          end_date: string
          event_type: string | null
          id: string
          school_id: string
          start_date: string
          title: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          cancels_lessons?: boolean | null
          created_at?: string
          description?: string | null
          end_date: string
          event_type?: string | null
          id?: string
          school_id: string
          start_date: string
          title: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          cancels_lessons?: boolean | null
          created_at?: string
          description?: string | null
          end_date?: string
          event_type?: string | null
          id?: string
          school_id?: string
          start_date?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "events_academic_year_id_fkey"
            columns: ["academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      extracurricular_classes: {
        Row: {
          class_id: string
          created_at: string
          extracurricular_id: string
          hours_per_week: number
          hours_per_year: number
          id: string
          school_id: string
          updated_at: string
        }
        Insert: {
          class_id: string
          created_at?: string
          extracurricular_id: string
          hours_per_week?: number
          hours_per_year?: number
          id?: string
          school_id: string
          updated_at?: string
        }
        Update: {
          class_id?: string
          created_at?: string
          extracurricular_id?: string
          hours_per_week?: number
          hours_per_year?: number
          id?: string
          school_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      extracurriculars: {
        Row: {
          academic_year_id: string
          color: string | null
          created_at: string
          description: string | null
          hours_per_year: number
          id: string
          name: string
          school_id: string
          session_category_id: string | null
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          color?: string | null
          created_at?: string
          description?: string | null
          hours_per_year?: number
          id?: string
          name: string
          school_id: string
          session_category_id?: string | null
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          color?: string | null
          created_at?: string
          description?: string | null
          hours_per_year?: number
          id?: string
          name?: string
          school_id?: string
          session_category_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "extracurriculars_session_category_id_fkey"
            columns: ["session_category_id"]
            isOneToOne: false
            referencedRelation: "session_categories"
            referencedColumns: ["id"]
          }
        ]
      }
      general_settings: {
        Row: {
          academic_year_id: string
          active_academic_year_id: string | null
          created_at: string
          id: string
          lesson_duration_minutes: number
          school_id: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          active_academic_year_id?: string | null
          created_at?: string
          id?: string
          lesson_duration_minutes?: number
          school_id: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          active_academic_year_id?: string | null
          created_at?: string
          id?: string
          lesson_duration_minutes?: number
          school_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "general_settings_academic_year_id_fkey"
            columns: ["academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "general_settings_active_academic_year_id_fkey"
            columns: ["active_academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "general_settings_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: true
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      grade_levels: {
        Row: {
          created_at: string
          education_level_id: string
          grade_number: number
          id: string
          is_active: boolean
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          education_level_id: string
          grade_number: number
          id?: string
          is_active?: boolean
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          education_level_id?: string
          grade_number?: number
          id?: string
          is_active?: boolean
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "grade_levels_education_level_id_fkey"
            columns: ["education_level_id"]
            isOneToOne: false
            referencedRelation: "education_levels"
            referencedColumns: ["id"]
          },
        ]
      }
      holidays: {
        Row: {
          academic_year_id: string
          category_id: string
          class_ids: string[] | null
          color: string
          created_at: string
          description: string | null
          end_date: string
          id: string
          is_national_holiday: boolean | null
          name: string
          school_id: string
          start_date: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          category_id: string
          class_ids?: string[] | null
          color: string
          created_at?: string
          description?: string | null
          end_date: string
          id?: string
          is_national_holiday?: boolean | null
          name: string
          school_id: string
          start_date: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          category_id?: string
          class_ids?: string[] | null
          color?: string
          created_at?: string
          description?: string | null
          end_date?: string
          id?: string
          is_national_holiday?: boolean | null
          name?: string
          school_id?: string
          start_date?: string
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string
          full_name: string
          id: string
          nip: string | null
          phone: string | null
          role: Database["public"]["Enums"]["user_role"]
          school_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          full_name: string
          id: string
          nip?: string | null
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          school_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          full_name?: string
          id?: string
          nip?: string | null
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          school_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      schedules: {
        Row: {
          academic_week: number | null
          academic_year_id: string
          class_id: string
          created_at: string
          day_of_week: number | null
          end_time: string | null
          id: string
          notes: string | null
          room: string | null
          schedule_date: string | null
          school_id: string
          start_time: string | null
          subject_id: string
          teacher_id: string | null
          time_session_id: string | null
          tujuan_pembelajaran: string | null
          materi_pembelajaran: string | null
          updated_at: string
        }
        Insert: {
          academic_week?: number | null
          academic_year_id: string
          class_id: string
          created_at?: string
          day_of_week?: number | null
          end_time?: string | null
          id?: string
          notes?: string | null
          room?: string | null
          schedule_date?: string | null
          school_id: string
          start_time?: string | null
          subject_id: string
          teacher_id?: string | null
          time_session_id?: string | null
          tujuan_pembelajaran?: string | null
          materi_pembelajaran?: string | null
          updated_at?: string
        }
        Update: {
          academic_week?: number | null
          academic_year_id?: string
          class_id?: string
          created_at?: string
          day_of_week?: number | null
          end_time?: string | null
          id?: string
          notes?: string | null
          room?: string | null
          schedule_date?: string | null
          school_id?: string
          start_time?: string | null
          subject_id?: string
          teacher_id?: string | null
          time_session_id?: string | null
          tujuan_pembelajaran?: string | null
          materi_pembelajaran?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "schedules_academic_year_id_fkey"
            columns: ["academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "schedules_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "classes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "schedules_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "schedules_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "schedules_teacher_id_fkey"
            columns: ["teacher_id"]
            isOneToOne: false
            referencedRelation: "teachers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "schedules_time_session_id_fkey"
            columns: ["time_session_id"]
            isOneToOne: false
            referencedRelation: "time_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      schools: {
        Row: {
          address: string | null
          created_at: string
          email: string | null
          id: string
          logo_url: string | null
          name: string
          phone: string | null
          principal_name: string | null
          principal_photo_url: string | null
          updated_at: string
        }
        Insert: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          logo_url?: string | null
          name: string
          phone?: string | null
          principal_name?: string | null
          principal_photo_url?: string | null
          updated_at?: string
        }
        Update: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          logo_url?: string | null
          name?: string
          phone?: string | null
          principal_name?: string | null
          principal_photo_url?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      semesters: {
        Row: {
          academic_year_id: string
          created_at: string
          end_date: string
          id: string
          is_active: boolean
          name: string
          school_id: string
          semester_number: number
          start_date: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          created_at?: string
          end_date: string
          id?: string
          is_active?: boolean
          name: string
          school_id: string
          semester_number: number
          start_date: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          created_at?: string
          end_date?: string
          id?: string
          is_active?: boolean
          name?: string
          school_id?: string
          semester_number?: number
          start_date?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "semesters_academic_year_id_fkey"
            columns: ["academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "semesters_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      session_categories: {
        Row: {
          academic_year_id: string
          color: string | null
          created_at: string
          description: string | null
          id: string
          name: string
          school_id: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          color?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name: string
          school_id: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          color?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          school_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      subject_categories: {
        Row: {
          academic_year_id: string
          created_at: string
          id: string
          name: string
          school_id: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          created_at?: string
          id?: string
          name: string
          school_id: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          created_at?: string
          id?: string
          name?: string
          school_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      subjects: {
        Row: {
          academic_year_id: string
          category: string | null
          code: string
          created_at: string
          id: string
          name: string
          school_id: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          category?: string | null
          code: string
          created_at?: string
          id?: string
          name: string
          school_id: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          category?: string | null
          code?: string
          created_at?: string
          id?: string
          name?: string
          school_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subjects_academic_year_id_fkey"
            columns: ["academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subjects_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_subjects: {
        Row: {
          created_at: string
          id: string
          subject_id: string
          teacher_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          subject_id: string
          teacher_id: string
        }
        Update: {
          created_at?: string
          id?: string
          subject_id?: string
          teacher_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "teacher_subjects_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_subjects_teacher_id_fkey"
            columns: ["teacher_id"]
            isOneToOne: false
            referencedRelation: "teachers"
            referencedColumns: ["id"]
          },
        ]
      }
      teachers: {
        Row: {
          academic_year_id: string
          created_at: string
          email: string | null
          full_name: string
          id: string
          is_available: boolean | null
          nip: string | null
          phone: string | null
          profile_id: string | null
          school_id: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          created_at?: string
          email?: string | null
          full_name: string
          id?: string
          is_available?: boolean | null
          nip?: string | null
          phone?: string | null
          profile_id?: string | null
          school_id: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          created_at?: string
          email?: string | null
          full_name?: string
          id?: string
          is_available?: boolean | null
          nip?: string | null
          phone?: string | null
          profile_id?: string | null
          school_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "teachers_academic_year_id_fkey"
            columns: ["academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teachers_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teachers_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      time_sessions: {
        Row: {
          academic_year_id: string
          category_id: string | null
          created_at: string
          day_of_week: number
          duration_minutes: number | null
          end_time: string
          id: string
          is_active: boolean | null
          jp_count: number | null
          school_id: string
          session_name: string | null
          session_number: number
          start_time: string
          updated_at: string
        }
        Insert: {
          academic_year_id: string
          category_id?: string | null
          created_at?: string
          day_of_week: number
          duration_minutes?: number | null
          end_time: string
          id?: string
          is_active?: boolean | null
          jp_count?: number | null
          school_id: string
          session_name?: string | null
          session_number: number
          start_time: string
          updated_at?: string
        }
        Update: {
          academic_year_id?: string
          category_id?: string | null
          created_at?: string
          day_of_week?: number
          duration_minutes?: number | null
          end_time?: string
          id?: string
          is_active?: boolean | null
          jp_count?: number | null
          school_id?: string
          session_name?: string | null
          session_number?: number
          start_time?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "time_sessions_academic_year_id_fkey"
            columns: ["academic_year_id"]
            isOneToOne: false
            referencedRelation: "academic_years"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_sessions_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "session_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "time_sessions_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      schedules_view: {
        Row: {
          id: string
          academic_week: number | null
          academic_year_id: string
          class_id: string
          created_at: string
          day_of_week: number | null
          end_time: string | null
          notes: string | null
          room: string | null
          schedule_date: string | null
          school_id: string
          start_time: string | null
          subject_id: string
          teacher_id: string | null
          time_session_id: string | null
          tujuan_pembelajaran: string | null
          materi_pembelajaran: string | null
          updated_at: string
          subject_name: string
          subject_code: string | null
          subject_color: string
          schedule_subject_hours_per_year: number | null
          schedule_subject_duration: number | null
          session_category_id: string | null
          session_category_name: string | null
          session_category_color: string | null
          session_category_description: string | null
          class_name: string | null
          class_level: string | null
          class_grade: number | null
          teacher_name: string | null
          teacher_nip: string | null
          teacher_email: string | null
        }
        Insert: {
          [_ in never]: never
        }
        Update: {
          [_ in never]: never
        }
        Relationships: []
      }
    }
    Functions: {
      get_user_school_id: {
        Args: { _user_id: string }
        Returns: string
      }
      has_role: {
        Args: {
          _user_id: string
          _role: Database["public"]["Enums"]["user_role"]
        }
        Returns: boolean
      }
    }
    Enums: {
      user_role: "superadmin" | "kepsek" | "kesiswaan" | "guru" | "siswa"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      user_role: ["superadmin", "kepsek", "kesiswaan", "guru", "siswa"],
    },
  },
} as const
