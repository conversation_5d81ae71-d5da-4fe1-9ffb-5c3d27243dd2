import React from 'react';
import { Button } from '@/components/ui/button';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';

interface ModalWeekNavigationProps {
  selectedWeek?: number;
  onWeekSelect: (week: number) => void;
  selectedTargetWeeks?: Set<number>;
  sourceWeek?: number;
  isSelectionMode?: boolean;
  variant?: 'copy' | 'delete';
}

export const ModalWeekNavigation: React.FC<ModalWeekNavigationProps> = ({
  selectedWeek = 0,
  onWeekSelect,
  selectedTargetWeeks,
  sourceWeek,
  isSelectionMode = false,
  variant = 'copy'
}) => {
  const { academicWeeks } = useAcademicWeeks();

  const getButtonStyles = (week: number) => {
    const isSelected = selectedWeek === week;
    const isTargetSelected = selectedTargetWeeks?.has(week);
    const isSourceWeek = sourceWeek === week;
    const isDisabled = isSelectionMode && isSourceWeek;

    if (isDisabled) {
      return 'bg-muted/30 border-muted text-muted-foreground cursor-not-allowed';
    }

    if (isTargetSelected && isSelectionMode) {
      if (variant === 'delete') {
        return 'bg-red-500/80 border-red-400 text-white shadow-lg scale-105 ring-1 ring-red-400/50';
      } else {
        return 'bg-green-500/80 border-green-400 text-white shadow-lg scale-105 ring-1 ring-green-400/50';
      }
    }

    if (isSelected && !isSelectionMode) {
      if (variant === 'delete') {
        return 'bg-red-500/20 border-red-400 text-red-300';
      } else {
        return 'bg-primary/20 border-primary text-primary';
      }
    }

    return 'bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground hover:scale-105';
  };

  return (
    <div className="w-full">
      {/* 🚀 FIXED: Responsive grid with proper scrolling */}
      <div className="grid grid-cols-8 sm:grid-cols-10 md:grid-cols-12 lg:grid-cols-15 xl:grid-cols-16 gap-2 w-full">
        {academicWeeks.map(week => (
          <Button
            key={week.weekNumber}
            data-week={week.weekNumber}
            variant="outline"
            size="sm"
            onClick={() => {
              const isDisabled = isSelectionMode && sourceWeek === week.weekNumber;
              if (!isDisabled) {
                onWeekSelect(week.weekNumber);
              }
            }}
            disabled={isSelectionMode && sourceWeek === week.weekNumber}
            className={`h-8 w-8 flex items-center justify-center text-xs font-medium rounded-full p-0 transition-all duration-200 ${getButtonStyles(week.weekNumber)}`}
          >
            {week.weekNumber}
          </Button>
        ))}
      </div>
    </div>
  );
};
