
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useCreateTeacher, useUpdateTeacher } from '@/hooks/useTeachers';

interface AddTeacherModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingTeacher?: any;
}

const AddTeacherModal: React.FC<AddTeacherModalProps> = ({ open, onOpenChange, editingTeacher }) => {
  const [formData, setFormData] = useState({
    full_name: '',
    nip: '',
    email: '',
    phone: '',
  });

  const createTeacher = useCreateTeacher();
  const updateTeacher = useUpdateTeacher();

  useEffect(() => {
    if (editingTeacher) {
      setFormData({
        full_name: editingTeacher.full_name || '',
        nip: editingTeacher.nip || '',
        email: editingTeacher.email || '',
        phone: editingTeacher.phone || '',
      });
    } else {
      setFormData({
        full_name: '',
        nip: '',
        email: '',
        phone: '',
      });
    }
  }, [editingTeacher, open]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingTeacher) {
      updateTeacher.mutate(
        { id: editingTeacher.id, ...formData },
        {
          onSuccess: () => {
            onOpenChange(false);
          }
        }
      );
    } else {
      createTeacher.mutate(formData, {
        onSuccess: () => {
          onOpenChange(false);
        }
      });
    }
  };

  const isLoading = createTeacher.isPending || updateTeacher.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {editingTeacher ? 'Edit Guru' : 'Tambah Guru Baru'}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="full_name">Nama Lengkap</Label>
            <Input
              id="full_name"
              value={formData.full_name}
              onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
              placeholder="Nama lengkap guru"
              
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="nip">NIP</Label>
            <Input
              id="nip"
              value={formData.nip}
              onChange={(e) => setFormData(prev => ({ ...prev, nip: e.target.value }))}
              placeholder="Nomor Induk Pegawai"
              
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              placeholder="<EMAIL>"
              
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">No. Telepon</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              placeholder="08xxxxxxxxxx"
              
            />
          </div>
          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)} 
              
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button 
              type="submit" 
              
              disabled={isLoading}
            >
              {isLoading ? 'Menyimpan...' : (editingTeacher ? 'Update' : 'Simpan')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddTeacherModal;
