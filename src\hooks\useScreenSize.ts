import { useState, useEffect, useCallback } from 'react';

interface ScreenSize {
  width: number;
  height: number;
}

interface DynamicLayoutDimensions {
  screenWidth: number;
  screenHeight: number;
  availableWidth: number;
  sidebarWidth: number;
  containerWidth: string;
  isCompact: boolean;
}

export const useScreenSize = (): ScreenSize => {
  const [screenSize, setScreenSize] = useState<ScreenSize>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1280,
    height: typeof window !== 'undefined' ? window.innerHeight : 720,
  });

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);

    // Set initial size
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return screenSize;
};

// Advanced hook for dynamic layout calculations
export const useDynamicLayout = (): DynamicLayoutDimensions => {
  const [dimensions, setDimensions] = useState<DynamicLayoutDimensions>({
    screenWidth: typeof window !== 'undefined' ? window.innerWidth : 1280,
    screenHeight: typeof window !== 'undefined' ? window.innerHeight : 720,
    availableWidth: typeof window !== 'undefined' ? window.innerWidth : 1280,
    sidebarWidth: 0,
    containerWidth: '100%',
    isCompact: false,
  });

  const calculateDimensions = useCallback(() => {
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    // Detect sidebar elements and calculate their width more accurately
    const appSidebar = document.querySelector('[data-sidebar="sidebar"]') as HTMLElement;
    const sidebarInset = document.querySelector('[data-sidebar="inset"]') as HTMLElement;
    const resizablePanelGroup = document.querySelector('[data-panel-group]') as HTMLElement;

    let totalSidebarWidth = 0;
    let mainContentWidth = screenWidth;

    // Calculate app sidebar width (main navigation sidebar)
    if (appSidebar && appSidebar.offsetWidth > 0) {
      const appSidebarWidth = appSidebar.offsetWidth;
      totalSidebarWidth += appSidebarWidth;
      mainContentWidth -= appSidebarWidth;
    }

    // For schedule sidebar, we need to calculate based on ResizablePanel
    // The schedule sidebar is part of the main content area
    let scheduleSidebarWidth = 0;
    if (resizablePanelGroup) {
      const panels = resizablePanelGroup.querySelectorAll('[data-panel]');
      if (panels.length >= 2) {
        const firstPanel = panels[0] as HTMLElement; // This should be the schedule sidebar
        if (firstPanel && firstPanel.offsetWidth > 0) {
          scheduleSidebarWidth = firstPanel.offsetWidth;
        }
      }
    }

    // Calculate available width for calendar (this is the main content area)
    const availableWidth = mainContentWidth;

    // Calculate calendar container width (excluding schedule sidebar)
    const calendarAvailableWidth = availableWidth - scheduleSidebarWidth;

    // Determine if we're in compact mode
    const isCompact = screenWidth < 1024 || calendarAvailableWidth < 600;

    // Calculate optimal container width for the entire schedule component
    let containerWidth: string;

    if (isCompact) {
      // In compact mode, use almost full main content width
      const padding = 8; // Minimal padding for mobile
      containerWidth = `${Math.max(mainContentWidth - padding, 320)}px`;
    } else {
      // In normal mode, use main content width with smart padding
      const padding = Math.min(24, mainContentWidth * 0.015); // 1.5% padding, max 24px
      containerWidth = `${Math.max(mainContentWidth - padding, 400)}px`;
    }

    // Debug logging (remove in production)
    console.log('🎯 Dynamic Layout Calculation:', {
      screenWidth,
      totalSidebarWidth,
      mainContentWidth,
      scheduleSidebarWidth,
      calendarAvailableWidth,
      containerWidth,
      isCompact
    });

    setDimensions({
      screenWidth,
      screenHeight,
      availableWidth: calendarAvailableWidth,
      sidebarWidth: totalSidebarWidth,
      containerWidth,
      isCompact,
    });
  }, []);

  useEffect(() => {
    // Initial calculation
    calculateDimensions();

    // Listen for window resize
    window.addEventListener('resize', calculateDimensions);

    // Listen for sidebar changes using ResizeObserver
    const resizeObserver = new ResizeObserver(() => {
      // Debounce the calculation to avoid too frequent updates
      setTimeout(calculateDimensions, 100);
    });

    // Observe app sidebar
    const appSidebar = document.querySelector('[data-sidebar="sidebar"]');
    if (appSidebar) {
      resizeObserver.observe(appSidebar);
    }

    // Observe ResizablePanel container and panels
    const resizablePanelGroup = document.querySelector('[data-panel-group]');
    if (resizablePanelGroup) {
      resizeObserver.observe(resizablePanelGroup);

      // Also observe individual panels
      const panels = resizablePanelGroup.querySelectorAll('[data-panel]');
      panels.forEach(panel => {
        resizeObserver.observe(panel);
      });
    }

    // Listen for ResizablePanel changes
    const handleResizablePanelChange = () => {
      setTimeout(calculateDimensions, 150);
    };

    // Add event listeners for resizable panel events
    document.addEventListener('mouseup', handleResizablePanelChange);
    document.addEventListener('touchend', handleResizablePanelChange);

    return () => {
      window.removeEventListener('resize', calculateDimensions);
      resizeObserver.disconnect();
      document.removeEventListener('mouseup', handleResizablePanelChange);
      document.removeEventListener('touchend', handleResizablePanelChange);
    };
  }, [calculateDimensions]);

  return dimensions;
};

// Function to get container max width based on screen size
export const getContainerMaxWidth = (screenWidth: number): string => {
  // Use almost full screen width with minimal padding for maximum space utilization
  if (screenWidth <= 640) {
    // Mobile: sm breakpoint
    return `${screenWidth - 16}px`; // 8px padding on each side
  } else if (screenWidth <= 768) {
    // Tablet: md breakpoint
    return `${screenWidth - 24}px`; // 12px padding on each side
  } else if (screenWidth <= 1024) {
    // Small desktop: lg breakpoint
    return `${screenWidth - 32}px`; // 16px padding on each side
  } else if (screenWidth <= 1280) {
    // Medium desktop: xl breakpoint
    return `${screenWidth - 40}px`; // 20px padding on each side
  } else if (screenWidth <= 1536) {
    // Large desktop: 2xl breakpoint
    return `${screenWidth - 48}px`; // 24px padding on each side
  } else {
    // Extra large screens
    return `${screenWidth - 64}px`; // 32px padding on each side
  }
};

// Function to get responsive breakpoint
export const getBreakpoint = (screenWidth: number): string => {
  if (screenWidth < 640) return 'sm';
  if (screenWidth < 768) return 'md';
  if (screenWidth < 1024) return 'lg';
  if (screenWidth < 1280) return 'xl';
  if (screenWidth < 1536) return '2xl';
  return '3xl';
};
