# Test Copy Schedule Functionality

## Perbaikan yang Telah Dilakukan:

### 1. ✅ Perbaikan Nama Tabel
- Semua operasi copy sekarang menggunakan tabel `class_schedules` yang benar
- Operasi delete conflict juga menggunakan `class_schedules`

### 2. ✅ Perbaikan Field Data
- Ditambahkan field `tujuan_pembelajaran` dan `materi_pembelajaran` dengan null fallback
- <PERSON><PERSON><PERSON> field yang diperlukan untuk insert operation

### 3. ✅ Perbaikan Logging dan Debugging
- Logging detail di `handleCopy` untuk debugging
- Logging detail di `getPreviewSchedules` untuk memahami data filtering
- Logging detail di `useCopySchedule` untuk memahami data availability

### 4. ✅ Perbaikan Validasi
- Validasi yang lebih detail dengan error messages yang jelas

## Langkah Test:

1. **Buka aplikasi di browser**: http://localhost:8082
2. **Login ke aplikasi**
3. **Navigasi ke halaman jadwal**
4. **<PERSON><PERSON><PERSON> kelas tertentu**
5. **<PERSON><PERSON> tombol "Salin Jadwal" (ikon Copy)**
6. **Test tab "Salin Hari":**
   - <PERSON><PERSON><PERSON> hari sumber (contoh: <PERSON><PERSON>)
   - <PERSON>lih pekan sumber
   - Pilih hari tujuan (contoh: Selasa)
   - Pilih pekan tujuan
   - Klik "Salin Jadwal"
7. **Periksa console browser** untuk melihat logging
8. **Periksa apakah jadwal berhasil disalin**

## Expected Behavior:

### Jika Berhasil:
- Toast notification "✅ Berhasil" muncul
- Jadwal baru muncul di hari/pekan tujuan
- Modal tertutup otomatis

### Jika Ada Masalah:
- Console browser menampilkan logging detail
- Error message yang jelas di toast notification

## Debug Information:

Periksa console browser untuk logging berikut:
- `🔍 useCopySchedule - Schedules data:` - Info tentang data schedules
- `🔍 getPreviewSchedules called with:` - Info tentang filtering data
- `🚀 Copy operation started:` - Info tentang operasi copy
- `📅 Copying day with params:` - Parameter yang dikirim ke API

## Kemungkinan Masalah:

1. **Data schedules kosong** - Periksa apakah ada jadwal di database
2. **Class ID tidak valid** - Periksa apakah kelas dipilih dengan benar
3. **Database connection issues** - Periksa koneksi Supabase
4. **Permission issues** - Periksa RLS policies di Supabase
