
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CalendarPlus, Clock, GraduationCap, Calendar, BookOpen } from 'lucide-react';
import { useAcademicYears, useActiveAcademicYear, useUpdateActiveAcademicYear } from '@/hooks/useAcademicYears';
import { AddAcademicYearModal } from '@/components/modals/AddAcademicYearModal';
import SemesterSelector from '@/components/semester/SemesterSelector';
import SemesterManagement from '@/components/semester/SemesterManagement';
interface AcademicSettingsTabProps {
  lessonDuration: number;
  setLessonDuration: (duration: number) => void;
}
export const AcademicSettingsTab: React.FC<AcademicSettingsTabProps> = ({
  lessonDuration,
  setLessonDuration
}) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [showSemesterManagement, setShowSemesterManagement] = useState(false);
  const {
    data: academicYears
  } = useAcademicYears();
  const {
    data: activeAcademicYear
  } = useActiveAcademicYear();
  const updateActiveAcademicYear = useUpdateActiveAcademicYear();
  const handleActiveAcademicYearChange = (academicYearId: string) => {
    updateActiveAcademicYear.mutate(academicYearId);
  };

  if (showSemesterManagement) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            onClick={() => setShowSemesterManagement(false)}
            variant="outline"
            className="bg-card border-border text-foreground hover:bg-accent rounded-xl"
          >
            ← Kembali ke Pengaturan Akademik
          </Button>
        </div>
        <SemesterManagement />
      </div>
    );
  }

  return <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Active Academic Year */}
      <Card className="bg-card backdrop-blur-sm border-border">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <GraduationCap className="h-6 w-6 text-purple-400" />
              <div>
                <Label htmlFor="active-academic-year" className="text-card-foreground font-medium mb-2 block">
                  <span className="font-bold text-base text-inherit">Tahun Ajaran Aktif</span>
                </Label>
              </div>
            </div>
            <Button onClick={() => setIsAddModalOpen(true)} className="bg-purple-500 hover:bg-purple-600 text-white font-semibold" size="sm">
              <CalendarPlus className="h-4 w-4 mr-2" />
              Tambah
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div>
            <Select value={activeAcademicYear?.id || ''} onValueChange={handleActiveAcademicYearChange}>
              <SelectTrigger className="bg-background border-border text-foreground">
                <SelectValue placeholder="Pilih tahun ajaran" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                {academicYears?.map(year => <SelectItem key={year.id} value={year.id} className="text-popover-foreground hover:bg-accent">
                    <div className="flex items-center justify-between w-full">
                      <span>{year.year_name}</span>
                      {year.is_active && <span className="ml-2 px-2 py-1 bg-green-500 text-white text-xs rounded-full font-medium">
                          Aktif
                        </span>}
                    </div>
                  </SelectItem>)}
              </SelectContent>
            </Select>
            {academicYears && academicYears.length === 0 && <p className="text-muted-foreground text-sm mt-2">
                Belum ada tahun ajaran. Klik tombol "Tambah" untuk membuat tahun ajaran baru.
              </p>}
          </div>
        </CardContent>
      </Card>

      {/* Active Semester */}
      <Card className="bg-card backdrop-blur-sm border-border">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <BookOpen className="h-6 w-6 text-cyan-400" />
              <div>
                <Label className="text-card-foreground font-medium mb-2 block">
                  <span className="font-bold text-base text-inherit">Semester Aktif</span>
                </Label>
              </div>
            </div>
            <Button
              onClick={() => setShowSemesterManagement(true)}
              className="bg-cyan-500 hover:bg-cyan-600 text-white font-semibold"
              size="sm"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Kelola
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <SemesterSelector />
        </CardContent>
      </Card>

      {/* Lesson Duration */}
      <Card className="bg-card backdrop-blur-sm border-border">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <Clock className="h-6 w-6 text-orange-400" />
            <div>
              <Label htmlFor="lesson-duration" className="text-card-foreground font-medium mb-2 block">
                <span className="font-bold text-base">Durasi Sesi/Pertemuan (Menit)</span>
              </Label>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div>
            <Input id="lesson-duration" type="number" value={lessonDuration} onChange={e => setLessonDuration(parseInt(e.target.value) || 45)} className="bg-background border-border text-foreground" min="15" max="180" step="5" />
          </div>
        </CardContent>
      </Card>

      <AddAcademicYearModal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} />
    </div>;
};
