
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import AppSidebar from './AppSidebar';
import Dashboard from './Dashboard';
import { useAuth } from '@/contexts/AuthContext';
import { useRealTimeSync } from '@/hooks/useRealTimeSync';

// Import the new page components
import ClassesPage from '@/pages/ClassesPage';
import TeachersPage from '@/pages/TeachersPage';
import TimeSessionsPage from '@/pages/TimeSessionsPage';

import EventsPage from '@/pages/EventsPage';
import GeneralSettingsPage from '@/pages/GeneralSettingsPage';
import SchoolInfoPage from '@/pages/SchoolInfoPage';
import SubjectsContentPage from '@/components/subjects/SubjectsContentPage';

// Import schedule sub-components
import { ScheduleCalendar } from '@/components/schedule/ScheduleCalendar';
import ScheduleOverview from '@/components/schedule/ScheduleOverview';
import ExternalView from '@/components/schedule/ExternalView';
import CalendarSync from '@/components/schedule/CalendarSync';
import PrintSchedule from '@/components/schedule/PrintSchedule';
import EffectiveDaysPage from '@/pages/EffectiveDaysPage';

const DashboardLayout: React.FC = () => {
  // ✅ ENHANCED: Enable real-time sync at dashboard level for all pages
  useRealTimeSync();

  const location = useLocation();
  const navigate = useNavigate();
  const [activeItem, setActiveItem] = useState('dashboard');
  const [isNavigating, setIsNavigating] = useState(false);

  // ✅ URL-based routing support
  useEffect(() => {
    // Skip URL-based updates if we're currently navigating
    if (isNavigating) {
      console.log('🔄 Skipping URL update during navigation');
      return;
    }

    const path = location.pathname;
    console.log('🔍 Current path:', path);

    // Map URL paths to activeItem values
    const pathToItem: Record<string, string> = {
      '/': 'dashboard',
      '/dashboard': 'dashboard',
      '/classes': 'classes',
      '/teachers': 'teachers',
      '/subjects': 'subjects',
      '/time-sessions': 'time-sessions',
      '/schedules': 'schedules',
      '/schedules/overview': 'schedules-overview',
      '/schedules/sync': 'schedules-sync',
      '/schedules/print': 'schedules-print',
      '/events': 'events',

      '/settings': 'general-settings',
      '/school-info': 'school-info'
    };

    const newActiveItem = pathToItem[path] || 'dashboard';
    
    // Only update if the activeItem actually needs to change
    if (newActiveItem !== activeItem) {
      console.log('🎯 Setting activeItem to:', newActiveItem, 'from:', activeItem);
      setActiveItem(newActiveItem);
    }
  }, [location.pathname, isNavigating, activeItem]);

  const handleItemChange = (item: string) => {
    console.log('🔄 handleItemChange called with:', item, 'current activeItem:', activeItem, 'isNavigating:', isNavigating);
    
    // 🎯 FIX: Prevent multiple rapid navigation calls
    if (isNavigating) {
      console.log('⚠️ Navigation in progress, ignoring call');
      return;
    }

    // 🎯 FIX: Prevent unnecessary navigation if already on the same item
    if (activeItem === item) {
      console.log('⚠️ Already on the same item, skipping navigation');
      return;
    }

    // Set navigation flag to prevent race conditions
    setIsNavigating(true);
    setActiveItem(item);

    // Update URL based on activeItem
    const itemToPath: Record<string, string> = {
      'dashboard': '/',
      'classes': '/classes',
      'teachers': '/teachers',
      'subjects': '/subjects',
      'time-sessions': '/time-sessions',
      'schedules': '/schedules',
      'schedules-overview': '/schedules/overview',
      'schedules-sync': '/schedules/sync',
      'schedules-print': '/schedules/print',
      'events': '/events',

      'general-settings': '/settings',
      'school-info': '/school-info'
    };

    const newPath = itemToPath[item] || '/';
    console.log('🚀 Navigating to:', newPath, 'from item:', item);
    
    // Navigate and reset flag after a short delay
    navigate(newPath, { replace: false });
    
    // Reset navigation flag after navigation completes
    setTimeout(() => {
      setIsNavigating(false);
      console.log('✅ Navigation completed, flag reset');
    }, 150);
  };

  const { profile } = useAuth();

  const renderContent = () => {
    console.log('🎯 DashboardLayout renderContent - activeItem:', activeItem);
    switch (activeItem) {
      case 'dashboard':
        return <Dashboard username={profile?.full_name || 'User'} />;
      case 'classes':
        return <ClassesPage />;
      case 'teachers':
        return <TeachersPage />;
      case 'subjects':
        return <SubjectsContentPage />;
      case 'time-sessions':
        return <TimeSessionsPage />;
      case 'schedules':
        return <ScheduleCalendar />;
      case 'schedules-overview':
        return <ScheduleOverview />;
      case 'schedules-sync':
        return <CalendarSync />;
      case 'schedules-print':
        return <PrintSchedule />;
      case 'events':
        return <EventsPage />;

      case 'general-settings':
        return <GeneralSettingsPage />;
      case 'school-info':
        return <SchoolInfoPage />;
      default:
        return <Dashboard username={profile?.full_name || 'User'} />;
    }
  };

  const getPageTitle = () => {
    switch (activeItem) {
      case 'dashboard':
        return 'Dashboard';
      case 'classes':
        return 'Manajemen Kelas';
      case 'teachers':
        return 'Manajemen Guru';
      case 'subjects':
        return 'Manajemen Mata Pelajaran';
      case 'time-sessions':
        return 'Manajemen Sesi dan Waktu';
      case 'schedules':
        return 'Jadwal';
      case 'schedules-overview':
        return 'Jadwal - Overview';
      case 'schedules-sync':
        return 'Jadwal - Sinkronisasi Kalender';
      case 'schedules-print':
        return 'Jadwal - Print';
      case 'events':
        return 'Manajemen Event';

      case 'general-settings':
        return 'Pengaturan Umum';
      case 'school-info':
        return 'Informasi Sekolah';
      default:
        return 'Dashboard';
    }
  };

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full bg-background">
        <AppSidebar activeItem={activeItem} onItemChange={handleItemChange} />
        <SidebarInset className="flex-1 min-w-0">
          <header className="flex h-16 shrink-0 items-center gap-2 bg-background border-b border-border px-4">
            <SidebarTrigger className="text-foreground hover:text-foreground/80" />
            <div className="text-xl font-semibold text-foreground ml-4 flex-1">
              {getPageTitle()}
            </div>
          </header>
          <main className="flex-1 overflow-auto bg-background w-full">
            <div className="w-full h-full">
              {renderContent()}
            </div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default DashboardLayout;
