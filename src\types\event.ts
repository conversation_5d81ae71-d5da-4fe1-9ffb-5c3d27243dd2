
export interface EventCategory {
  id: string;
  name: string;
  color: string;
  school_id?: string;
  academic_year_id?: string;
}

export interface Holiday {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  description?: string;
  color: string;
  category_id: string;
  is_national_holiday?: boolean;
  class_ids?: string[];
  school_id: string;
  academic_year_id: string;
  created_at?: string;
  updated_at?: string;
}

export interface NationalHoliday {
  id: string;
  name: string;
  date: string;
  description?: string;
}

// Kategori default
export const DEFAULT_CATEGORIES: EventCategory[] = [
  { id: 'event-sekolah', name: 'Event Sekolah', color: '#3b82f6' },
  { id: 'liburan-semester', name: '<PERSON><PERSON><PERSON>', color: '#10b981' },
  { id: 'sumatif-akhir', name: '<PERSON><PERSON><PERSON>', color: '#f59e0b' },
  { id: 'ujian', name: '<PERSON><PERSON><PERSON>', color: '#ef4444' },
  { id: 'libur-nasional', name: '<PERSON><PERSON>', color: '#8b5cf6' },
  { id: 'kegiatan-khusus', name: '<PERSON>gia<PERSON>hus<PERSON>', color: '#ec4899' },
];

// Data libur nasional Indonesia 2024-2025
export const NATIONAL_HOLIDAYS_2024: NationalHoliday[] = [
  { id: 'tahun-baru-2024', name: 'Tahun Baru 2024', date: '2024-01-01' },
  { id: 'imlek-2024', name: 'Tahun Baru Imlek', date: '2024-02-10' },
  { id: 'nyepi-2024', name: 'Hari Raya Nyepi', date: '2024-03-11' },
  { id: 'wafat-isa-2024', name: 'Wafat Isa Al Masih', date: '2024-03-29' },
  { id: 'idul-fitri-2024-1', name: 'Idul Fitri 1445 H', date: '2024-04-10' },
  { id: 'idul-fitri-2024-2', name: 'Idul Fitri 1445 H', date: '2024-04-11' },
  { id: 'buruh-2024', name: 'Hari Buruh', date: '2024-05-01' },
  { id: 'kenaikan-isa-2024', name: 'Kenaikan Isa Al Masih', date: '2024-05-09' },
  { id: 'waisak-2024', name: 'Hari Raya Waisak', date: '2024-05-23' },
  { id: 'pancasila-2024', name: 'Hari Lahir Pancasila', date: '2024-06-01' },
  { id: 'idul-adha-2024', name: 'Idul Adha 1445 H', date: '2024-06-17' },
  { id: 'muharram-2024', name: 'Tahun Baru Islam 1446 H', date: '2024-07-07' },
  { id: 'kemerdekaan-2024', name: 'Hari Kemerdekaan RI', date: '2024-08-17' },
  { id: 'maulid-2024', name: 'Maulid Nabi Muhammad SAW', date: '2024-09-16' },
  { id: 'natal-2024', name: 'Hari Raya Natal', date: '2024-12-25' },
];

export const NATIONAL_HOLIDAYS_2025: NationalHoliday[] = [
  { id: 'tahun-baru-2025', name: 'Tahun Baru 2025', date: '2025-01-01' },
  { id: 'imlek-2025', name: 'Tahun Baru Imlek', date: '2025-01-29' },
  { id: 'nyepi-2025', name: 'Hari Raya Nyepi', date: '2025-03-29' },
  { id: 'wafat-isa-2025', name: 'Wafat Isa Al Masih', date: '2025-04-18' },
  { id: 'idul-fitri-2025-1', name: 'Idul Fitri 1446 H', date: '2025-03-31' },
  { id: 'idul-fitri-2025-2', name: 'Idul Fitri 1446 H', date: '2025-04-01' },
  { id: 'buruh-2025', name: 'Hari Buruh', date: '2025-05-01' },
  { id: 'kenaikan-isa-2025', name: 'Kenaikan Isa Al Masih', date: '2025-05-29' },
  { id: 'waisak-2025', name: 'Hari Raya Waisak', date: '2025-05-12' },
  { id: 'pancasila-2025', name: 'Hari Lahir Pancasila', date: '2025-06-01' },
  { id: 'idul-adha-2025', name: 'Idul Adha 1446 H', date: '2025-06-07' },
  { id: 'muharram-2025', name: 'Tahun Baru Islam 1447 H', date: '2025-06-26' },
  { id: 'kemerdekaan-2025', name: 'Hari Kemerdekaan RI', date: '2025-08-17' },
  { id: 'maulid-2025', name: 'Maulid Nabi Muhammad SAW', date: '2025-09-05' },
  { id: 'natal-2025', name: 'Hari Raya Natal', date: '2025-12-25' },
];
