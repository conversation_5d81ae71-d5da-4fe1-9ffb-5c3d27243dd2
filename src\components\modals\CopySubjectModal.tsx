
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useClasses } from '@/hooks/useClasses';
import { useSubjects } from '@/hooks/useSubjects';
import { useCopyClassSubjects } from '@/hooks/useClassSubjects';

interface CopySubjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CopySubjectModal: React.FC<CopySubjectModalProps> = ({ open, onOpenChange }) => {
  const [fromClassId, setFromClassId] = useState('');
  const [toClassIds, setToClassIds] = useState<string[]>([]);
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);

  const { data: classes } = useClasses();
  const { data: subjects } = useSubjects();
  const copyClassSubjects = useCopyClassSubjects();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!fromClassId || toClassIds.length === 0 || selectedSubjects.length === 0) {
      return;
    }

    copyClassSubjects.mutate({
      fromClassId,
      toClassIds,
      subjectIds: selectedSubjects
    }, {
      onSuccess: () => {
        setFromClassId('');
        setToClassIds([]);
        setSelectedSubjects([]);
        onOpenChange(false);
      }
    });
  };

  const handleToClassChange = (classId: string, checked: boolean) => {
    if (checked) {
      setToClassIds(prev => [...prev, classId]);
    } else {
      setToClassIds(prev => prev.filter(id => id !== classId));
    }
  };

  const handleSubjectChange = (subjectId: string, checked: boolean) => {
    if (checked) {
      setSelectedSubjects(prev => [...prev, subjectId]);
    } else {
      setSelectedSubjects(prev => prev.filter(id => id !== subjectId));
    }
  };

  const isLoading = copyClassSubjects.isPending;
  const availableTargetClasses = classes?.filter(cls => cls.id !== fromClassId) || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-lime-400">
            Salin Mata Pelajaran Antar Kelas
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label className="text-gray-300">Kelas Sumber</Label>
            <Select value={fromClassId} onValueChange={setFromClassId}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Pilih kelas sumber" />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600">
                {classes?.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.level} {cls.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label className="text-gray-300">Kelas Tujuan</Label>
            <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
              {availableTargetClasses.map((cls) => (
                <div key={cls.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`class-${cls.id}`}
                    checked={toClassIds.includes(cls.id)}
                    onCheckedChange={(checked) => handleToClassChange(cls.id, checked as boolean)}
                  />
                  <Label htmlFor={`class-${cls.id}`} className="text-sm text-gray-300">
                    {cls.level} {cls.name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-gray-300">Mata Pelajaran</Label>
            <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
              {subjects?.map((subject) => (
                <div key={subject.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`subject-${subject.id}`}
                    checked={selectedSubjects.includes(subject.id)}
                    onCheckedChange={(checked) => handleSubjectChange(subject.id, checked as boolean)}
                  />
                  <Label htmlFor={`subject-${subject.id}`} className="text-sm text-gray-300">
                    {subject.name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)} 
              className="border-gray-600 text-gray-300"
              disabled={isLoading}
            >
              Batal
            </Button>
            <Button 
              type="submit" 
              className="bg-lime-400 hover:bg-lime-500 text-gray-900"
              disabled={isLoading || !fromClassId || toClassIds.length === 0 || selectedSubjects.length === 0}
            >
              {isLoading ? 'Menyalin...' : 'Salin'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CopySubjectModal;
