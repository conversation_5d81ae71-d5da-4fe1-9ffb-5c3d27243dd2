
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Edit, Trash2, Users, GraduationCap, Filter, School, DoorOpen, DoorOpenIcon, Grid3X3, List } from 'lucide-react';
import { useClasses, useDeleteClass } from '@/hooks/useClasses';
import { useGradeLevels } from '@/hooks/useGradeLevels';
import AddClassModal from '@/components/modals/AddClassModal';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const ClassesPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingClass, setEditingClass] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'card' | 'table'>('card');

  const { data: classes, isLoading } = useClasses();
  const { data: gradeLevels } = useGradeLevels();
  const deleteClass = useDeleteClass();

  const handleEdit = (classItem: any) => {
    setEditingClass(classItem);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus kelas ini?')) {
      deleteClass.mutate(id);
    }
  };

  const filteredClasses = classes?.filter(classItem => {
    const matchesSearch = classItem.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = selectedLevel === 'all' || classItem.level === selectedLevel;
    return matchesSearch && matchesLevel;
  }) || [];

  const getColorByLevel = (level: string) => {
    const colors = [
      'from-lime-400 to-lime-600 bg-lime-400/10 border-lime-400/20 text-lime-400',
      'from-blue-400 to-blue-600 bg-blue-400/10 border-blue-400/20 text-blue-400',
      'from-purple-400 to-purple-600 bg-purple-400/10 border-purple-400/20 text-purple-400',
      'from-cyan-400 to-cyan-600 bg-cyan-400/10 border-cyan-400/20 text-cyan-400',
      'from-orange-400 to-orange-600 bg-orange-400/10 border-orange-400/20 text-orange-400',
      'from-green-400 to-green-600 bg-green-400/10 border-green-400/20 text-green-400',
    ];
    // Use grade number to determine color
    const index = (parseInt(level) || 1) - 1;
    return colors[index % colors.length];
  };

  const levels = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'];

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center space-x-2">
                <DoorOpen className="h-8 w-8 text-blue-400" />
                <h1 className="text-3xl font-bold text-foreground">Kelas</h1>
              </div>
            </div>
            <p className="text-muted-foreground">Kelola kelas dan tingkat pendidikan</p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* View Toggle */}
            <div className="flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
              <Button
                variant={viewMode === 'card' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('card')}
                className={`${viewMode === 'card' ? 'bg-blue-500 text-white' : 'text-muted-foreground hover:text-foreground'}`}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('table')}
                className={`${viewMode === 'table' ? 'bg-blue-500 text-white' : 'text-muted-foreground hover:text-foreground'}`}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            <Button
              onClick={() => {
                setEditingClass(null);
                setIsModalOpen(true);
              }}
              className="bg-gradient-to-r from-blue-400 to-blue-600 hover:from-blue-500 hover:to-blue-700 text-white font-semibold shadow-lg shadow-blue-400/25 transition-all duration-300 transform hover:scale-105"
            >
              <Plus className="mr-2 h-5 w-5" />
              Tambah Kelas
            </Button>
          </div>
        </div>

        {/* Filters Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Cari kelas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-background border-border text-foreground placeholder-muted-foreground focus:border-blue-400/50 focus:ring-blue-400/20"
            />
          </div>

          <Select value={selectedLevel} onValueChange={setSelectedLevel}>
            <SelectTrigger className="bg-background border-border text-foreground">
              <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
              <SelectValue placeholder="Filter tingkat" />
            </SelectTrigger>
            <SelectContent className="bg-popover border-border">
              <SelectItem value="all" className="text-popover-foreground hover:bg-accent">Semua Tingkat</SelectItem>
              {levels.map((level) => (
                <SelectItem key={level} value={level} className="text-popover-foreground hover:bg-accent">
                  Kelas {level}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <DoorOpenIcon className="h-4 w-4" />
            <span>Total: {filteredClasses.length} kelas</span>
          </div>
        </div>

        {/* Classes Display */}
        {isLoading ? (
          viewMode === 'card' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="bg-card border-border animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="bg-card border-border">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="border-border">
                      <TableHead className="text-blue-400">Nama Kelas</TableHead>
                      <TableHead className="text-blue-400">Tingkat</TableHead>
                      <TableHead className="text-blue-400">Grade</TableHead>
                      <TableHead className="text-blue-400">Kapasitas</TableHead>
                      <TableHead className="text-blue-400">Dibuat</TableHead>
                      <TableHead className="text-blue-400 text-center">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Array.from({ length: 6 }).map((_, i) => (
                      <TableRow key={i} className="border-border">
                        <TableCell><div className="h-4 bg-muted rounded w-3/4 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-1/2 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-1/3 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-1/2 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-2/3 animate-pulse"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted rounded w-1/4 animate-pulse"></div></TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )
        ) : (
          viewMode === 'card' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredClasses.map((classItem) => {
                const colorClasses = getColorByLevel(classItem.level);
                return (
                  <Card key={classItem.id} className="bg-card backdrop-blur-sm border-border hover:border-blue-400/30 transition-all duration-300 group">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-card-foreground text-lg font-semibold mb-1">
                            {classItem.name}
                          </CardTitle>
                          <p className="text-muted-foreground text-sm">
                            Kelas {classItem.level}
                          </p>
                        </div>
                        <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(classItem)}
                            className="h-8 w-8 p-0 text-blue-400 hover:bg-blue-400/20"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(classItem.id)}
                            className="h-8 w-8 p-0 text-red-400 hover:bg-red-400/20"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        <Badge className={`${colorClasses.split(' ').slice(2).join(' ')} border`}>
                          <GraduationCap className="h-3 w-3 mr-1" />
                          Kelas {classItem.level}
                        </Badge>

                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Kapasitas:</span>
                          <span className="text-card-foreground font-medium">{classItem.capacity || 30} siswa</span>
                        </div>

                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Dibuat:</span>
                          <span className="text-card-foreground">
                            {new Date(classItem.created_at).toLocaleDateString('id-ID')}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <Card className="bg-card border-border">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="border-border">
                      <TableHead className="text-blue-400">Nama Kelas</TableHead>
                      <TableHead className="text-blue-400">Tingkat</TableHead>
                      <TableHead className="text-blue-400">Grade</TableHead>
                      <TableHead className="text-blue-400">Kapasitas</TableHead>
                      <TableHead className="text-blue-400">Dibuat</TableHead>
                      <TableHead className="text-blue-400 text-center">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredClasses.map((classItem) => {
                      const colorClasses = getColorByLevel(classItem.level);
                      return (
                        <TableRow key={classItem.id} className="border-border hover:bg-accent/50">
                          <TableCell className="text-foreground font-medium">{classItem.name}</TableCell>
                          <TableCell className="text-muted-foreground">{classItem.level}</TableCell>
                          <TableCell className="text-muted-foreground">{classItem.grade}</TableCell>
                          <TableCell className="text-muted-foreground">{classItem.capacity || 30} siswa</TableCell>
                          <TableCell className="text-muted-foreground">
                            {new Date(classItem.created_at).toLocaleDateString('id-ID')}
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex justify-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEdit(classItem)}
                                className="h-8 w-8 p-0 text-blue-400 hover:bg-blue-400/20"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDelete(classItem.id)}
                                className="h-8 w-8 p-0 text-red-400 hover:bg-red-400/20"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )
        )}

        {filteredClasses.length === 0 && !isLoading && (
          <Card className="bg-card backdrop-blur-sm border-border">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Users className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">Belum ada kelas</h3>
              <p className="text-muted-foreground text-center mb-6">
                {searchTerm || selectedLevel !== 'all'
                  ? 'Tidak ditemukan kelas yang sesuai dengan filter'
                  : 'Mulai dengan menambahkan kelas pertama'
                }
              </p>
              {!searchTerm && selectedLevel === 'all' && (
                <Button 
                  onClick={() => {
                    setEditingClass(null);
                    setIsModalOpen(true);
                  }}
                  className="bg-gradient-to-r from-blue-400 to-blue-600 hover:from-blue-500 hover:to-blue-700 text-white font-semibold"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Tambah Kelas
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        <AddClassModal
          open={isModalOpen}
          onOpenChange={(open) => {
            setIsModalOpen(open);
            if (!open) setEditingClass(null);
          }}
          editingClass={editingClass}
        />
      </div>
    </div>
  );
};

export default ClassesPage;
