import React, { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { useActiveAcademicYear } from '@/hooks/useAcademicYears';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, getDay, isSameDay } from 'date-fns';
import { id } from 'date-fns/locale';

interface YearlyCalendarViewProps {
  schedules: any[];
  isLoading: boolean;
  selectedSemester?: string | null;
}

export const YearlyCalendarView: React.FC<YearlyCalendarViewProps> = ({
  schedules,
  isLoading,
  selectedSemester
}) => {
  const { data: activeAcademicYear } = useActiveAcademicYear();
  const { academicWeeks } = useAcademicWeeks();

  // Generate months for academic year (July to June)
  const months = useMemo(() => {
    if (!activeAcademicYear || !activeAcademicYear.year_name) return [];

    const yearParts = activeAcademicYear.year_name.split('/');
    if (yearParts.length !== 2) return [];

    const startYear = parseInt(yearParts[0]);
    const endYear = parseInt(yearParts[1]);

    const allMonths = [
      // July to December of start year (Semester 1)
      { name: 'Juli', year: startYear, monthIndex: 6, semester: 1 },
      { name: 'Agustus', year: startYear, monthIndex: 7, semester: 1 },
      { name: 'September', year: startYear, monthIndex: 8, semester: 1 },
      { name: 'Oktober', year: startYear, monthIndex: 9, semester: 1 },
      { name: 'November', year: startYear, monthIndex: 10, semester: 1 },
      { name: 'Desember', year: startYear, monthIndex: 11, semester: 1 },
      // January to June of end year (Semester 2)
      { name: 'Januari', year: endYear, monthIndex: 0, semester: 2 },
      { name: 'Februari', year: endYear, monthIndex: 1, semester: 2 },
      { name: 'Maret', year: endYear, monthIndex: 2, semester: 2 },
      { name: 'April', year: endYear, monthIndex: 3, semester: 2 },
      { name: 'Mei', year: endYear, monthIndex: 4, semester: 2 },
      { name: 'Juni', year: endYear, monthIndex: 5, semester: 2 },
    ];

    // Filter by semester if selected
    if (selectedSemester) {
      const semesterNumber = parseInt(selectedSemester);
      return allMonths.filter(month => month.semester === semesterNumber);
    }

    return allMonths;
  }, [activeAcademicYear]);

  // Convert academic_week and day_of_week to actual dates
  const scheduleDates = useMemo(() => {
    const dates = new Set<string>();

    schedules.forEach(schedule => {
      // Use academic_week and day_of_week to calculate actual date
      if (schedule.academic_week && schedule.day_of_week && academicWeeks.length > 0) {
        const academicWeek = academicWeeks.find(week => week.weekNumber === schedule.academic_week);

        if (academicWeek) {
          // Calculate the actual date based on week start and day of week
          const weekStartDate = new Date(academicWeek.startDate);
          const dayOffset = schedule.day_of_week === 7 ? 6 : schedule.day_of_week - 1; // Convert to 0-based, Sunday = 6

          const scheduleDate = new Date(weekStartDate);
          scheduleDate.setDate(weekStartDate.getDate() + dayOffset);

          const dateString = format(scheduleDate, 'yyyy-MM-dd');
          dates.add(dateString);
        }
      }

      // Fallback: if schedule_date is available, use it directly
      if (schedule.schedule_date) {
        dates.add(schedule.schedule_date);
      }
    });

    return dates;
  }, [schedules, academicWeeks]);

  const renderMonth = (month: { name: string; year: number; monthIndex: number }) => {
    const monthDate = new Date(month.year, month.monthIndex, 1);
    const monthStart = startOfMonth(monthDate);
    const monthEnd = endOfMonth(monthDate);
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });

    // Create grid with proper spacing for days of week
    const firstDayOfWeek = getDay(monthStart); // 0 = Sunday, 1 = Monday, etc.
    const adjustedFirstDay = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1; // Convert to Monday = 0

    return (
      <div key={`${month.name}-${month.year}`} className="bg-card border border-border rounded-lg p-4">
        {/* Month Row - Horizontal layout like the image */}
        <div className="flex items-center gap-4">
          {/* Month Name */}
          <div className="w-20 text-left">
            <h3 className="text-sm font-semibold text-foreground">{month.name}</h3>
          </div>

          {/* Days Grid - Horizontal layout */}
          <div className="flex flex-wrap gap-1">
            {Array.from({ length: 31 }, (_, i) => {
              const dayNumber = i + 1;
              const monthDate = new Date(month.year, month.monthIndex, dayNumber);

              // Check if this day exists in the month
              if (monthDate.getMonth() !== month.monthIndex) {
                return null;
              }

              const dayString = format(monthDate, 'yyyy-MM-dd');
              const hasSchedule = scheduleDates.has(dayString);

              return (
                <div
                  key={dayNumber}
                  className={`
                    h-8 w-8 flex items-center justify-center text-xs rounded cursor-pointer transition-colors
                    ${hasSchedule
                      ? 'bg-blue-500 text-white font-semibold hover:bg-blue-600'
                      : 'text-foreground hover:bg-accent border border-border'
                    }
                  `}
                  title={hasSchedule ? `Ada jadwal pada ${format(monthDate, 'dd MMMM yyyy', { locale: id })}` : ''}
                >
                  {dayNumber}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className="bg-card border-border shadow-sm">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-foreground">
            Kalender Tahunan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-muted-foreground">Memuat data kalender...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-card border-border shadow-sm">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-foreground">
          Kalender Tahunan
          {activeAcademicYear && (
            <span className="text-lg font-normal text-muted-foreground ml-2">
              {activeAcademicYear.year_name}
            </span>
          )}
          {selectedSemester && (
            <span className="text-base font-normal text-primary ml-2">
              - Semester {selectedSemester}
            </span>
          )}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Hari yang berwarna menunjukkan adanya jadwal/kegiatan
          {selectedSemester && (
            <span className="ml-2 text-primary">
              (Semester {selectedSemester}: {selectedSemester === '1' ? 'Juli - Desember' : 'Januari - Juni'})
            </span>
          )}
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {months.map(renderMonth)}
        </div>
      </CardContent>
    </Card>
  );
};
