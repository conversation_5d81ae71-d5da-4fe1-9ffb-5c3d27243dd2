/* Make calendar more compact */
.fc .fc-daygrid-day-frame {
  padding: 0 !important;
  min-height: 0 !important;
  height: auto !important;
}

.fc .fc-daygrid-day-top {
  justify-content: center;
  padding: 0 !important;
  width: 100%;
}

.fc .fc-daygrid-day-number {
  padding: 0 !important;
  font-size: 0.85rem;
  text-align: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.fc .fc-col-header-cell {
  padding: 0 !important;
}

.fc .fc-col-header-cell-cushion {
  padding: 2px 0 !important;
  width: 100%;
  text-align: center;
  font-size: 0.85rem;
}

.fc .fc-daygrid-body {
  height: auto !important;
  width: 100% !important;
}

/* Make day cells more compact */
.compact-day-cell {
  padding: 0 !important;
}

.compact-day-header {
  padding: 1px 0 !important;
  text-align: center;
}

/* Let FullCalendar handle event styling naturally */

/* Fix calendar height */
.fc-daygrid-body-balanced {
  height: auto !important;
}

/* Reduce padding in month view */
.fc .fc-daygrid-day-events {
  padding: 0 !important;
  margin: 0 !important;
  min-height: 0 !important;
}

.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
  position: relative !important;
  min-height: 0 !important;
}

/* Fix the event positioning */
.fc .fc-daygrid-event-harness {
  margin-top: 1px !important;
}

/* For better visualization of today */
.fc .fc-day-today {
  background-color: rgba(236, 240, 254, 0.5) !important;
}

/* Make the calendar cells more precise */
.fc table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
}

.fc-theme-standard td, .fc-theme-standard th {
  border: 1px solid #ddd;
}

/* Ensure date numbers are centered */
.fc .fc-daygrid-day {
  position: relative;
  min-height: 1.8rem;
  max-height: 2.2rem;
  height: auto;
}

/* Fix day cell width to be consistent */
.fc-day {
  width: calc(100% / 7) !important;
}

/* Make the calendar fill its container better */
.calendar-container .fc {
  height: 100%;
  width: 100%;
  min-height: 100%;
}

/* Ensure calendar container fits viewport properly */
.calendar-container {
  padding: 0 !important;
  margin: 0 !important;
  height: 100%;
  min-height: 100%;
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important; /* Remove all scrollbars from container */
  box-sizing: border-box;
}

/* Dynamic height support for time grid */
.fc-timegrid {
  height: auto !important;
  min-height: 100%;
}

.fc-view-harness {
  height: 100% !important;
  min-height: 100%;
}



/* Ensure headers are aligned with day cells - now includes Sunday */
.fc-col-header-cell {
  width: calc(100% / 7) !important;
}

/* Responsive container adjustments */
@media (max-width: 640px) {
  .fc-col-header-cell {
    font-size: 0.75rem;
    padding: 0.25rem;
  }

  .fc-timegrid-slot-label {
    font-size: 0.75rem;
  }

  .fc-event {
    font-size: 0.75rem;
  }
}

@media (max-width: 768px) {
  .fc-col-header-cell {
    font-size: 0.875rem;
    padding: 0.5rem;
  }

  .fc-timegrid-slot-label {
    font-size: 0.875rem;
  }

  .fc-event {
    font-size: 0.875rem;
  }
}

/* Ensure calendar adapts to container width */
@media (max-width: 1024px) {
  .calendar-container {
    padding: 0.25rem !important;
  }
}

@media (max-width: 768px) {
  .calendar-container {
    padding: 0.125rem !important;
  }
}

/* CRITICAL: Remove any div constraints that might limit calendar height */
.calendar-container > div {
  height: 100% !important;
  overflow: visible !important;
}

/* Ensure parent containers don't limit height */
.calendar-container,
.calendar-container * {
  box-sizing: border-box !important;
}

/* Force FullCalendar to use all available space */
.calendar-container .fc,
.calendar-container .fc-view-harness,
.calendar-container .fc-view-harness-active {
  height: 100% !important;
  min-height: 100% !important;
  overflow: visible !important;
}

/* Fill available space better */
.fc-daygrid-month-frame {
  min-height: 0 !important;
}

/* Fix calendar width issue */
.fc-scrollgrid-sync-table {
  width: 100% !important;
}

/* Make all cells take equal space */
.fc-scrollgrid-sync-inner {
  min-height: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Adjust the calendar month view */
.fc .fc-view-harness-active > .fc-view {
  width: 100% !important;
  height: 100% !important;
}

/* Better fit numbers in cells */
.fc-daygrid-day-number {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* More compact day grid */
.fc-dayGridMonth-view .fc-daygrid-body {
  width: 100% !important;
}

/* Control the overall calendar width to fit viewport */
.fc {
  width: 100% !important;
  max-width: 100% !important;
  height: 100% !important;
  box-sizing: border-box;
}

/* Prevent horizontal overflow */
.fc-view-harness {
  overflow-x: hidden !important;
  max-width: 100% !important;
}

.fc-scrollgrid {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Ensure all calendar tables fit within container */
.fc table {
  max-width: 100% !important;
  table-layout: fixed !important;
}

/* Prevent column headers from overflowing */
.fc-col-header-cell {
  max-width: calc(100% / 7) !important;
  overflow: hidden !important;
}

/* Ensure resizable panels don't cause overflow */
[data-panel-group] {
  overflow: hidden !important;
}

[data-panel] {
  overflow: hidden !important;
}

/* Time grid specific styles */
.fc-timegrid-slots {
  height: auto !important;
}

/* REMOVED: Conflicting 60px height rule - using 30px for 30-minute slots */

/* REMOVED: Conflicting 60px height rule for labels */

/* Light theme styles for schedule calendar (default) */
.fc {
  background-color: transparent;
  color: #1f2937;
}

.fc-theme-standard .fc-scrollgrid {
  border-color: #d1d5db;
}

.fc-theme-standard td, .fc-theme-standard th {
  border-color: #d1d5db;
}

.fc .fc-col-header-cell {
  background-color: #f9fafb;
  color: #10b981;
}

.fc .fc-daygrid-day {
  background-color: rgba(249, 250, 251, 0.5);
}

.fc .fc-day-today {
  background-color: rgba(16, 185, 129, 0.1) !important;
}

.fc-event {
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.fc .fc-button-primary {
  background-color: #10b981;
  border-color: #10b981;
  color: #ffffff;
}

/* Dark theme styles for schedule calendar */
.dark .fc {
  background-color: transparent;
  color: #ffffff;
}

.dark .fc-theme-standard .fc-scrollgrid {
  border-color: #4b5563;
}

.dark .fc-theme-standard td, .dark .fc-theme-standard th {
  border-color: #4b5563;
}

.dark .fc .fc-col-header-cell {
  background-color: #374151;
  color: #10b981;
}

.dark .fc .fc-daygrid-day {
  background-color: rgba(31, 41, 55, 0.2);
}

.dark .fc .fc-day-today {
  background-color: rgba(16, 185, 129, 0.1) !important;
}

.fc .fc-button-primary:hover {
  background-color: #059669;
  border-color: #059669;
}

.fc .fc-button-primary:disabled {
  background-color: #6b7280;
  border-color: #6b7280;
}

.fc .fc-toolbar-title {
  color: #1f2937;
  font-weight: 600;
}

.fc .fc-timegrid-slot-label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.fc .fc-timegrid-axis {
  background-color: #f9fafb;
}

/* Dark theme overrides */
.dark .fc .fc-toolbar-title {
  color: #ffffff;
}

.dark .fc .fc-timegrid-slot-label {
  color: #9ca3af;
}

.dark .fc .fc-timegrid-axis {
  background-color: #374151;
}

/* WORLD-CLASS FIX: Remove all height constraints */
.fc-timegrid-body {
  overflow: visible !important; /* ✅ Critical: Let content flow naturally */
  max-height: 10px !important;
  height: 10px !important; /* ✅ Auto height for natural extension */
  min-height: 100% !important; /* ✅ Ensure minimum coverage */
}

/* COMPACT: 30-minute slots with consistent height */
.fc-timegrid-slot {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  border-bottom: 1px solid rgba(209, 213, 219, 0.5) !important;
  line-height: 30px !important;
  font-size: 11px !important;
}

/* Dark theme slot borders */
.dark .fc-timegrid-slot {
  border-bottom: 1px solid rgba(75, 85, 99, 0.3) !important;
}

/* Enhanced drag and drop styles */
.fc-event-draggable {
  transition: all 0.2s ease-in-out;
  position: relative;
}

.fc-event-draggable:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.fc-event-draggable:active {
  transform: scale(0.98);
  cursor: grabbing !important;
}

/* Drag ghost styling */
.fc-event-draggable.fc-event-mirror {
  opacity: 0.8;
  transform: rotate(2deg);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border: 2px dashed #10b981;
}

/* Drop zone highlighting */
.fc-highlight {
  background-color: rgba(16, 185, 129, 0.1) !important;
  border: 2px dashed #10b981 !important;
}

.fc-timegrid-slot.fc-timegrid-slot-label {
  border-bottom: 1px solid #4b5563;
}

/* Enhanced event styling - minimal overrides */
.fc-event {
  border-radius: 8px !important;
  border: 2px solid white !important;
  font-weight: 500 !important;
  transition: all 0.2s ease-in-out;
  overflow: hidden;
  margin: 0 !important; /* CRITICAL: Ensure no margins */
  box-sizing: border-box !important; /* CRITICAL: Include borders in height calculation */
}

.fc-event:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.fc-event .fc-event-title {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 2px 4px;
}

/* Business hours styling */
.fc-non-business {
  background-color: rgba(107, 114, 128, 0.1) !important;
}

/* Selection styling */
.fc-highlight {
  background-color: rgba(16, 185, 129, 0.15) !important;
  border: 2px solid #10b981 !important;
  border-radius: 4px !important;
}

/* OPTIMIZED: Clean time grid styling - removed to avoid conflicts */

/* External events container */
#external-events-container {
  user-select: none;
}

#external-events-container .fc-event-draggable {
  cursor: grab;
  margin-bottom: 4px;
}

#external-events-container .fc-event-draggable:active {
  cursor: grabbing;
}

/* Disabled state for draggable items */
.fc-event-draggable[draggable="false"] {
  cursor: not-allowed !important;
  opacity: 0.5 !important;
}

.fc-event-draggable[draggable="false"]:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Drop feedback animation */
@keyframes dropSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.drop-success {
  animation: dropSuccess 0.3s ease-in-out;
}

/* Time slot hover effects */
.fc-timegrid-slot:hover {
  background-color: rgba(16, 185, 129, 0.05);
}

/* Today column highlighting */
.fc-day-today {
  background-color: rgba(16, 185, 129, 0.02) !important;
}

/* Weekend styling (if enabled) */
.fc-day-sat, .fc-day-sun {
  background-color: rgba(107, 114, 128, 0.05);
}

/* Event resize handles */
.fc-event .fc-event-resizer {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.3);
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.fc-event:hover .fc-event-resizer {
  background-color: #10b981;
  border-color: #059669;
}

/* ELITE ARCHITECTURE: Natural height flow system */
.fc-timegrid-body {
  min-height: 100% !important;
  height: auto !important; /* ✅ Let content determine height */
}

/* PROFESSIONAL: Timegrid slots natural extension */
.fc-timegrid-slots {
  height: auto !important; /* ✅ Natural height based on content */
  min-height: 100% !important;
}

/* MASTER-LEVEL: Column natural height */
.fc-timegrid-col {
  height: auto !important; /* ✅ Auto height for natural flow */
  min-height: 100% !important;
}

/* CRITICAL FIX: The root cause solution */
.fc-scroller {
  overflow: visible !important; /* ✅ KEY FIX: Allow content to extend naturally */
  max-height: none !important;
  height: auto !important; /* ✅ Auto height instead of fixed */
}

/* REMOVED: Conflicting rule - using main definition above */

/* Ensure calendar container takes full available height */
.calendar-container .fc {
  height: 100% !important;
}

.calendar-container .fc-view-harness {
  height: 100% !important;
}

/* Advanced responsive layout transitions */
.fc-timegrid {
  transition: width 0.3s ease-in-out !important;
}

.fc-col-header-cell {
  transition: width 0.3s ease-in-out !important;
}

.fc-timegrid-col {
  transition: width 0.3s ease-in-out !important;
}

/* Dynamic width adjustments for different sidebar states */
@media (max-width: 768px) {
  .fc-col-header-cell {
    min-width: 80px !important;
  }

  .fc-timegrid-slot-label {
    font-size: 0.75rem !important;
    padding: 0.25rem !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .fc-col-header-cell {
    min-width: 100px !important;
  }

  .fc-timegrid-slot-label {
    font-size: 0.875rem !important;
    padding: 0.5rem !important;
  }
}

@media (min-width: 1025px) {
  .fc-col-header-cell {
    min-width: 120px !important;
  }

  .fc-timegrid-slot-label {
    font-size: 1rem !important;
    padding: 0.75rem !important;
  }
}

/* Smooth resizing for calendar container */
.calendar-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Optimize calendar rendering during resize */
.fc-timegrid-body {
  will-change: width !important;
}

.fc-scrollgrid {
  will-change: width !important;
}

/* WORLD-CLASS: Grid lines extending to full height */
.fc-timegrid-col-frame {
  border-right: 1px solid #4b5563 !important;
  height: auto !important; /* ✅ Natural height flow */
  min-height: 100% !important;
}

/* ELITE: Day columns natural extension */
.fc-timegrid-col-bg {
  height: auto !important; /* ✅ Auto height for natural flow */
  min-height: 100% !important;
  border-right: 1px solid #4b5563 !important;
}

/* Force timegrid axis to extend full height */
.fc-timegrid-axis {
  height: 100% !important;
  min-height: 100% !important;
}

/* EXPERT-LEVEL: Optimized height constraints removal */
.fc-timegrid-body {
  max-height: none !important;
  overflow: visible !important;
}

/* WORLD-CLASS: Main timegrid natural flow */
.fc-timegrid {
  height: auto !important; /* ✅ Natural height flow */
  min-height: 100% !important;
  overflow: visible !important;
}

/* ELITE: Table elements natural extension */
.fc-timegrid table {
  height: auto !important; /* ✅ Let table content determine height */
  min-height: 100% !important;
}

/* LEGENDARY: Scrollgrid sections natural flow */
.fc-scrollgrid-section {
  height: auto !important; /* ✅ Natural section height */
  min-height: 100% !important;
}

.fc-scrollgrid-section-body {
  height: auto !important; /* ✅ Auto height for natural extension */
  min-height: 100% !important;
  overflow: visible !important;
}

/* Removed duplicate - already defined above */

/* ELITE: Force complete grid rendering */
.fc-timegrid-axis-chunk {
  height: auto !important;
  min-height: 100% !important;
}

/* MASTER: Ensure day headers don't interfere */
.fc-col-header {
  position: relative !important;
  z-index: 1 !important;
}

/* CONSISTENT: Override FullCalendar's internal height calculations */
:root {
  --fc-timegrid-slot-height: 30px;
  --fc-small-font-size: 0.85em;
  --fc-page-bg-color: transparent;
}

/* CRITICAL: Force FullCalendar to respect our slot height */
.fc-timegrid {
  --fc-timegrid-slot-height: 30px !important;
}

/* CONSISTENT: Additional slot elements to 30px height */
.fc-timegrid-slot-lane,
.fc-timegrid-slots table tr,
.fc-timegrid-slots table tr td {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  line-height: 30px !important;
  font-size: 11px !important;
  padding: 0 !important;
}

/* CRITICAL: Override FullCalendar's computed styles */
.fc-timegrid-slots table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

/* ESSENTIAL: Hour labels should span 2 slots (60px total) but not affect slot height */
.fc-timegrid-axis .fc-timegrid-slot-label {
  height: 60px !important;
  line-height: 60px !important;
  font-size: 12px !important;
  /* Ensure labels don't interfere with slot height calculations */
  position: relative !important;
}

/* CONSISTENT: Force 30px height on ALL possible elements */
.fc-timegrid-slots td,
.fc-timegrid-slots th,
.fc-timegrid-slot-minor,
.fc-timegrid-slot-major,
.fc-timegrid-col-events,
.fc-timegrid-col-bg {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* ULTIMATE: Override any inline styles to maintain consistency */
.fc-timegrid-slots [style*="height"] {
  height: 30px !important;
}

/* CRITICAL: Ensure all slots have identical height regardless of position */
.fc-timegrid-slot:nth-child(odd),
.fc-timegrid-slot:nth-child(even) {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  box-sizing: border-box !important;
  border-bottom: 1px solid rgba(75, 85, 99, 0.3) !important;
}

/* Let FullCalendar handle event height calculations naturally - removed duplicate styling */

/* ENHANCED: Event hover effects */
.fc-event:hover {
  transform: scale(1.02) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  z-index: 10 !important;
}

/* OPTIMIZED: Event resizing handles */
.fc-event .fc-event-resizer {
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(0, 0, 0, 0.3) !important;
  width: 100% !important;
  height: 4px !important;
  bottom: 0 !important;
  cursor: ns-resize !important;
}

/* BALANCED: Let FullCalendar handle height but fix margins */
.fc-timegrid-event {
  /* Let FullCalendar calculate height naturally */
  margin: 1px 0 !important; /* Small margin for visual separation */
}

/* BALANCED: Keep harness functionality but reduce spacing */
.fc-timegrid-event-harness {
  /* Let FullCalendar handle height calculations */
  margin: 0 !important; /* Remove harness margins only */
}

/* ✅ SIMPLE: Keep original styling - no ugly effects */
.fc-event-resizing-preview {
  /* Keep original beautiful styling */
  transition: none !important; /* Only disable transition for smooth resize tracking */
}

/* Keep resize handle simple and clean */
.fc-event-resizing-preview .fc-event-resizer {
  /* Keep original resize handle styling */
}

/* Smooth transition back after resize */
.fc-event:not(.fc-event-resizing-preview) {
  transition: all 0.2s ease !important; /* Re-enable transitions when not resizing */
}

/* Real-time preview title styling */
.fc-event-resizing-preview .fc-event-title {
  font-weight: bold !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* Better text visibility */
}

/* Grid snap indicators (subtle visual guides) */
.fc-timegrid-slot {
  position: relative !important;
}

/* ✅ REMOVED: Custom slot styling - let FullCalendar handle it */

/* ✅ REMOVED: Custom resize visual feedback - let FullCalendar handle it */

/* ✅ REMOVED: Custom resize CSS - let FullCalendar handle resize naturally */

/* Removed debug styling for consistent slot appearance */

/* ✅ REMOVED: Copy functionality CSS - restored to FullCalendar default behavior */

/* Hide default event dots in month view for custom pink circle highlight */
.fc-dayGridMonth-view .fc-daygrid-day-events {
  display: none !important;
}
