import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash2, Calendar, Clock, CheckCircle } from 'lucide-react';
import { useSemesters, useDeleteSemester, useUpdateActiveSemester } from '@/hooks/useSemesters';
import { Semester, getSemesterDescription } from '@/types/semester';
import AddSemesterModal from '@/components/modals/AddSemesterModal';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

interface SemesterManagementProps {
  className?: string;
}

export const SemesterManagement: React.FC<SemesterManagementProps> = ({ className = '' }) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingSemester, setEditingSemester] = useState<Semester | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const { data: semesters, isLoading } = useSemesters();
  const deleteSemester = useDeleteSemester();
  const updateActiveSemester = useUpdateActiveSemester();

  const handleEdit = (semester: Semester) => {
    setEditingSemester(semester);
    setIsAddModalOpen(true);
  };

  const handleDelete = async (semesterId: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus semester ini?')) {
      setDeletingId(semesterId);
      try {
        await deleteSemester.mutateAsync(semesterId);
      } finally {
        setDeletingId(null);
      }
    }
  };

  const handleSetActive = (semesterId: string) => {
    updateActiveSemester.mutate(semesterId);
  };

  const handleCloseModal = () => {
    setIsAddModalOpen(false);
    setEditingSemester(null);
  };

  const groupedSemesters = semesters?.reduce((acc, semester) => {
    const yearName = semester.academic_year?.year_name || 'Kelola Semester';
    if (!acc[yearName]) {
      acc[yearName] = [];
    }
    acc[yearName].push(semester);
    return acc;
  }, {} as Record<string, typeof semesters>);

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {[1, 2, 3].map((i) => (
          <Card key={i} className="bg-gray-800/40 backdrop-blur-sm border-gray-600/30 animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-700/50 rounded w-1/3"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-4 bg-gray-700/50 rounded w-1/2"></div>
                <div className="h-4 bg-gray-700/50 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Manajemen Semester</h2>
          <p className="text-gray-400">Kelola semester untuk setiap tahun ajaran</p>
        </div>
        <Button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl"
        >
          <Plus className="h-4 w-4 mr-2" />
          Tambah Semester
        </Button>
      </div>

      {!semesters || semesters.length === 0 ? (
        <Card className="bg-gray-800/40 backdrop-blur-sm border-gray-600/30">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Calendar className="h-12 w-12 text-gray-500 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Belum Ada Semester</h3>
            <p className="text-gray-400 text-center mb-4">
              Mulai dengan menambahkan semester untuk tahun ajaran Anda
            </p>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl"
            >
              <Plus className="h-4 w-4 mr-2" />
              Tambah Semester Pertama
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedSemesters || {}).map(([yearName, yearSemesters]) => (
            <Card key={yearName} className="bg-gray-800/40 backdrop-blur-sm border-gray-600/30">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-purple-400" />
                  {yearName}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {yearSemesters?.map((semester) => (
                    <div
                      key={semester.id}
                      className={`p-4 rounded-xl border transition-all duration-300 ${
                        semester.is_active
                          ? 'bg-purple-500/20 border-purple-400/50'
                          : 'bg-gray-700/30 border-gray-600/30 hover:border-gray-500/50'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-white">{semester.name}</h4>
                            {semester.is_active && (
                              <Badge className="bg-green-500/20 text-green-400 text-xs">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Aktif
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-400 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {getSemesterDescription(semester.semester_number)}
                          </p>
                        </div>
                        <div className="flex gap-1">
                          {!semester.is_active && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleSetActive(semester.id)}
                              disabled={updateActiveSemester.isPending}
                              className="h-8 w-8 p-0 text-green-400 hover:bg-green-400/20"
                              title="Aktifkan semester"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEdit(semester)}
                            className="h-8 w-8 p-0 text-blue-400 hover:bg-blue-400/20"
                            title="Edit semester"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDelete(semester.id)}
                            disabled={deletingId === semester.id || semester.is_active}
                            className="h-8 w-8 p-0 text-red-400 hover:bg-red-400/20 disabled:opacity-50"
                            title={semester.is_active ? "Tidak dapat menghapus semester aktif" : "Hapus semester"}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-xs text-gray-400 space-y-1">
                        <div>
                          Mulai: {format(new Date(semester.start_date), 'dd MMMM yyyy', { locale: id })}
                        </div>
                        <div>
                          Selesai: {format(new Date(semester.end_date), 'dd MMMM yyyy', { locale: id })}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <AddSemesterModal
        isOpen={isAddModalOpen}
        onClose={handleCloseModal}
        editingSemester={editingSemester}
      />
    </div>
  );
};

export default SemesterManagement;
