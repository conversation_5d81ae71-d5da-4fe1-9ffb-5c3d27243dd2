import { useMemo } from 'react';
import { useActiveAcademicYear } from './useAcademicYears';
import { useActiveSemester } from './useSemesters';
import { getCurrentSemester } from '@/types/semester';

/**
 * Hook untuk mendapatkan konteks akademik lengkap (tahun ajaran + semester)
 * Berguna untuk komponen yang membutuhkan informasi akademik terkini
 */
export const useAcademicContext = () => {
  const { data: activeAcademicYear, isLoading: isLoadingYear } = useActiveAcademicYear();
  const { data: activeSemester, isLoading: isLoadingSemester } = useActiveSemester();

  const isLoading = isLoadingYear || isLoadingSemester;

  // Determine current semester based on current date if no active semester
  const currentSemesterNumber = useMemo(() => {
    if (activeSemester) {
      return activeSemester.semester_number;
    }
    return getCurrentSemester();
  }, [activeSemester]);

  // Check if we're in the correct semester period
  const isInCorrectSemesterPeriod = useMemo(() => {
    if (!activeSemester) return false;
    
    const now = new Date();
    const semesterStart = new Date(activeSemester.start_date);
    const semesterEnd = new Date(activeSemester.end_date);
    
    return now >= semesterStart && now <= semesterEnd;
  }, [activeSemester]);

  // Get academic year period info
  const academicYearInfo = useMemo(() => {
    if (!activeAcademicYear) return null;
    
    const startYear = new Date(activeAcademicYear.start_date).getFullYear();
    const endYear = new Date(activeAcademicYear.end_date).getFullYear();
    
    return {
      startYear,
      endYear,
      displayName: `${startYear}/${endYear}`,
      isActive: activeAcademicYear.is_active
    };
  }, [activeAcademicYear]);

  // Get semester period info
  const semesterInfo = useMemo(() => {
    if (!activeSemester) return null;
    
    return {
      number: activeSemester.semester_number,
      name: activeSemester.name,
      startDate: activeSemester.start_date,
      endDate: activeSemester.end_date,
      isActive: activeSemester.is_active,
      isInPeriod: isInCorrectSemesterPeriod
    };
  }, [activeSemester, isInCorrectSemesterPeriod]);

  // Check if academic setup is complete
  const isAcademicSetupComplete = useMemo(() => {
    return !!(activeAcademicYear && activeSemester);
  }, [activeAcademicYear, activeSemester]);

  // Get academic period display string
  const academicPeriodDisplay = useMemo(() => {
    if (!academicYearInfo || !semesterInfo) return 'Belum ada periode akademik';
    
    return `${semesterInfo.name} - ${academicYearInfo.displayName}`;
  }, [academicYearInfo, semesterInfo]);

  return {
    // Raw data
    activeAcademicYear,
    activeSemester,
    
    // Loading states
    isLoading,
    isLoadingYear,
    isLoadingSemester,
    
    // Computed info
    academicYearInfo,
    semesterInfo,
    currentSemesterNumber,
    isInCorrectSemesterPeriod,
    isAcademicSetupComplete,
    academicPeriodDisplay,
    
    // Helper functions
    getCurrentSemester: () => getCurrentSemester(),
    
    // Status checks
    hasActiveAcademicYear: !!activeAcademicYear,
    hasActiveSemester: !!activeSemester,
    isAcademicYearActive: activeAcademicYear?.is_active || false,
    isSemesterActive: activeSemester?.is_active || false
  };
};

export default useAcademicContext;
