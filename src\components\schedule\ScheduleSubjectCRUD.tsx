// =====================================================
// SCHEDULE SUBJECT CRUD COMPONENT
// =====================================================
// Komponen CRUD untuk mata pelajaran di setiap kategori

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Edit, Trash2, X, Save, Plus } from 'lucide-react';
import { useUpdateScheduleSubject, useDeleteScheduleSubject } from '@/hooks/useScheduleSubjects';
import { useClasses } from '@/hooks/useClasses';
import { useScheduleClassSubjects, useCreateScheduleClassSubject, useDeleteScheduleClassSubject } from '@/hooks/useScheduleSubjects';
import { toast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { SCHEDULE_CATEGORY_COLORS } from '@/types/scheduleCategory';

interface ScheduleSubjectCRUDProps {
  subject: {
    id: string;
    name: string;
    code?: string;
    color: string;
    total_hours_per_year: number;
    category: string;
    type: string;
    source: string;
  };
  selectedClassId?: string;
}

interface EditFormData {
  name: string;
  code: string;
  color: string;
  total_hours_per_year: number;
  assigned_classes: string[];
}

export const ScheduleSubjectCRUD: React.FC<ScheduleSubjectCRUDProps> = ({
  subject,
  selectedClassId
}) => {
  const queryClient = useQueryClient();
  
  // Hooks
  const { data: classes = [] } = useClasses();
  const { data: scheduleClassSubjects = [] } = useScheduleClassSubjects();
  const updateSubjectMutation = useUpdateScheduleSubject(false);
  const deleteSubjectMutation = useDeleteScheduleSubject(false);
  const createClassSubjectMutation = useCreateScheduleClassSubject(false);
  const deleteClassSubjectMutation = useDeleteScheduleClassSubject(false);

  // State
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [editFormData, setEditFormData] = useState<EditFormData>({
    name: subject.name,
    code: subject.code || '',
    color: subject.color,
    total_hours_per_year: subject.total_hours_per_year,
    assigned_classes: []
  });

  // Get assigned classes for this subject
  const assignedClassIds = scheduleClassSubjects
    .filter(cs => cs.schedule_subject_id === subject.id)
    .map(cs => cs.class_id);

  // Initialize form data when modal opens
  const handleEditClick = () => {
    setEditFormData({
      name: subject.name,
      code: subject.code || '',
      color: subject.color,
      total_hours_per_year: subject.total_hours_per_year,
      assigned_classes: assignedClassIds
    });
    setIsEditModalOpen(true);
  };

  // Handle form submission
  const handleSave = async () => {
    try {
      console.log('🚀 Updating schedule subject:', editFormData);

      // Update subject data
      await updateSubjectMutation.mutateAsync({
        id: subject.id,
        name: editFormData.name.trim(),
        code: editFormData.code?.trim() || undefined,
        color: editFormData.color,
        total_hours_per_year: editFormData.total_hours_per_year
      });

      // Handle class assignments
      const currentAssignedClasses = assignedClassIds;
      const newAssignedClasses = editFormData.assigned_classes;

      // Classes to add
      const classesToAdd = newAssignedClasses.filter(classId => 
        !currentAssignedClasses.includes(classId)
      );

      // Classes to remove
      const classesToRemove = currentAssignedClasses.filter(classId => 
        !newAssignedClasses.includes(classId)
      );

      // Add new class assignments
      for (const classId of classesToAdd) {
        await createClassSubjectMutation.mutateAsync({
          schedule_subject_id: subject.id,
          class_id: classId,
          hours_per_week: 0,
          hours_per_year: 0
        });
      }

      // Remove class assignments
      for (const classId of classesToRemove) {
        const relationToDelete = scheduleClassSubjects.find(cs =>
          cs.schedule_subject_id === subject.id && cs.class_id === classId
        );
        if (relationToDelete) {
          await deleteClassSubjectMutation.mutateAsync(relationToDelete.id);
        }
      }

      // Refresh data
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-subjects-by-category'] });
      await queryClient.invalidateQueries({ queryKey: ['schedule-class-subjects'] });

      toast({
        title: "Berhasil",
        description: `Mata pelajaran "${editFormData.name}" berhasil diperbarui`
      });

      setIsEditModalOpen(false);

    } catch (error) {
      console.error('Error updating subject:', error);
      toast({
        title: "Gagal",
        description: `Gagal memperbarui mata pelajaran: ${error?.message || 'Terjadi kesalahan'}`,
        variant: "destructive"
      });
    }
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      console.log('🗑️ Deleting schedule subject:', subject.id);

      await deleteSubjectMutation.mutateAsync(subject.id);

      toast({
        title: "Berhasil",
        description: `Mata pelajaran "${subject.name}" berhasil dihapus`
      });

      setIsDeleteConfirmOpen(false);

    } catch (error) {
      console.error('Error deleting subject:', error);
      toast({
        title: "Gagal",
        description: `Gagal menghapus mata pelajaran: ${error?.message || 'Terjadi kesalahan'}`,
        variant: "destructive"
      });
    }
  };

  // Handle class selection
  const handleClassSelection = (classId: string, checked: boolean) => {
    setEditFormData(prev => ({
      ...prev,
      assigned_classes: checked
        ? [...prev.assigned_classes, classId]
        : prev.assigned_classes.filter(id => id !== classId)
    }));
  };

  // Handle select all classes
  const handleSelectAllClasses = () => {
    const allClassIds = classes.map(cls => cls.id);
    setEditFormData(prev => ({
      ...prev,
      assigned_classes: allClassIds
    }));
  };

  // Handle deselect all classes
  const handleDeselectAllClasses = () => {
    setEditFormData(prev => ({
      ...prev,
      assigned_classes: []
    }));
  };

  // Check if all classes are selected
  const isAllClassesSelected = classes.length > 0 && editFormData.assigned_classes.length === classes.length;

  // ✅ FIXED: Show CRUD for all schedule-related subjects (schedule_subject, ekstrakurikuler, mata_pelajaran)
  // Only hide CRUD for subjects that cannot be edited (e.g., system subjects)
  const isEditableSubject =
    subject.source === 'schedule' ||
    subject.type === 'ekstrakurikuler' ||
    subject.type === 'schedule_subject' ||
    subject.type === 'mata_pelajaran';

  if (!isEditableSubject) {
    return null;
  }

  return (
    <>
      {/* CRUD Buttons - WHITE STYLING WITH TRANSPARENT BACKGROUND */}
      <div className="flex gap-1 bg-black/20 rounded-md p-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleEditClick}
          className="h-6 w-6 p-0 text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200"
          title="Edit"
        >
          <Edit className="h-3 w-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsDeleteConfirmOpen(true)}
          className="h-6 w-6 p-0 text-white/80 hover:text-red-400 hover:bg-red-500/10 transition-all duration-200"
          title="Delete"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white flex items-center justify-between">
              <span>Edit Mata Pelajaran</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Nama Mata Pelajaran */}
            <div>
              <Label className="text-white">Nama Mata Pelajaran *</Label>
              <Input
                value={editFormData.name}
                onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Masukkan nama mata pelajaran"
                className="bg-gray-800 border-gray-600 text-white"
              />
            </div>

            {/* Kode Mata Pelajaran */}
            <div>
              <Label className="text-white">Kode Mata Pelajaran</Label>
              <Input
                value={editFormData.code}
                onChange={(e) => setEditFormData(prev => ({ ...prev, code: e.target.value }))}
                placeholder="Masukkan kode mata pelajaran (opsional)"
                className="bg-gray-800 border-gray-600 text-white"
              />
            </div>

            {/* Warna */}
            <div>
              <Label className="text-white">Warna</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {SCHEDULE_CATEGORY_COLORS.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => setEditFormData(prev => ({ ...prev, color }))}
                    className={`w-8 h-8 rounded-full border-2 ${
                      editFormData.color === color ? 'border-white' : 'border-gray-600'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>

            {/* JP/Tahun */}
            <div>
              <Label className="text-white">JP/Tahun</Label>
              <Input
                type="number"
                value={editFormData.total_hours_per_year}
                onChange={(e) => setEditFormData(prev => ({ ...prev, total_hours_per_year: parseInt(e.target.value) || 0 }))}
                placeholder="0"
                className="bg-gray-800 border-gray-600 text-white"
              />
            </div>

            {/* Pilih Kelas */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-white">Pilih Kelas *</Label>
                <div className="flex space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={isAllClassesSelected ? handleDeselectAllClasses : handleSelectAllClasses}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white text-xs"
                  >
                    {isAllClassesSelected ? (
                      <>
                        <X className="h-3 w-3 mr-1" />
                        Batal Semua
                      </>
                    ) : (
                      <>
                        <Plus className="h-3 w-3 mr-1" />
                        Pilih Semua
                      </>
                    )}
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border border-gray-600 rounded-md p-2 bg-gray-800">
                {classes.map((cls) => (
                  <div key={cls.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`edit-${cls.id}`}
                      checked={editFormData.assigned_classes.includes(cls.id)}
                      onCheckedChange={(checked) => handleClassSelection(cls.id, checked as boolean)}
                      className="border-gray-600 data-[state=checked]:bg-blue-600"
                    />
                    <Label htmlFor={`edit-${cls.id}`} className="text-white text-sm cursor-pointer">
                      {cls.name}
                    </Label>
                  </div>
                ))}
              </div>
              <div className="text-xs text-gray-400 mt-1">
                {editFormData.assigned_classes.length} dari {classes.length} kelas dipilih
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setIsEditModalOpen(false)}
                className="border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                Batal
              </Button>
              <Button
                onClick={handleSave}
                disabled={updateSubjectMutation.isPending}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {updateSubjectMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Simpan
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent className="bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">
              Konfirmasi Hapus
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <p className="text-gray-300">
              Apakah Anda yakin ingin menghapus mata pelajaran <strong>"{subject.name}"</strong>?
            </p>
            <p className="text-sm text-yellow-400">
              Tindakan ini tidak dapat dibatalkan dan akan menghapus semua data terkait.
            </p>
            
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsDeleteConfirmOpen(false)}
                className="border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                Batal
              </Button>
              <Button
                onClick={handleDelete}
                disabled={deleteSubjectMutation.isPending}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {deleteSubjectMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Menghapus...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Hapus
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
