import React, { useState, useMemo } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Copy,
  Calendar,
  Clock,
  AlertTriangle,
  X,
  CalendarDays,
  CalendarRange
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useCopySchedule } from '@/hooks/useCopySchedule';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { ModalWeekNavigation } from './ModalWeekNavigation';

interface CopyScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedClassId?: string;
  currentWeek?: number;
}

export const CopyScheduleModal: React.FC<CopyScheduleModalProps> = ({
  isOpen,
  onClose,
  selectedClassId,
  currentWeek = 1
}) => {
  const [activeTab, setActiveTab] = useState<'day' | 'week'>('day');
  const [overwriteConflicts, setOverwriteConflicts] = useState(false);

  // Day copy states
  const [sourceDay, setSourceDay] = useState<number>(1);
  const [selectedTargetDays, setSelectedTargetDays] = useState<Set<number>>(new Set([2])); // Multiple target days
  const [sourceDayWeek, setSourceDayWeek] = useState<number>(currentWeek);
  const [targetDayWeek, setTargetDayWeek] = useState<number>(currentWeek);

  // Week copy states
  const [sourceWeek, setSourceWeek] = useState<number>(currentWeek);
  const [selectedTargetWeeks, setSelectedTargetWeeks] = useState<Set<number>>(new Set([currentWeek + 1]));

  const { academicWeeks } = useAcademicWeeks();
  const { copyDay, copyMultipleWeeks, copyMultipleDays, getPreviewSchedules, checkCopyConflicts, isLoading } = useCopySchedule();

  // Handle target week selection
  const handleTargetWeekToggle = (week: number) => {
    const newSelected = new Set(selectedTargetWeeks);
    if (newSelected.has(week)) {
      newSelected.delete(week);
    } else {
      newSelected.add(week);
    }
    setSelectedTargetWeeks(newSelected);
  };

  // Convert Set to Array for processing
  const targetWeeksArray = Array.from(selectedTargetWeeks).sort((a, b) => a - b);
  const targetDaysArray = Array.from(selectedTargetDays).sort((a, b) => a - b);

  // Function to toggle target day selection
  const handleTargetDayToggle = (day: number) => {
    const newSelectedDays = new Set(selectedTargetDays);
    if (newSelectedDays.has(day)) {
      newSelectedDays.delete(day);
    } else {
      newSelectedDays.add(day);
    }
    setSelectedTargetDays(newSelectedDays);
  };

  const dayNames = [
    { value: 1, label: 'Senin' },
    { value: 2, label: 'Selasa' },
    { value: 3, label: 'Rabu' },
    { value: 4, label: 'Kamis' },
    { value: 5, label: 'Jumat' },
    { value: 6, label: 'Sabtu' },
    { value: 7, label: 'Minggu' }
  ];

  // Get preview schedules
  const previewSchedules = useMemo(() => {
    if (!selectedClassId) {
      console.log('🔍 CopyScheduleModal: No selectedClassId');
      return [];
    }

    let result;
    if (activeTab === 'day') {
      result = getPreviewSchedules(sourceDay, sourceDayWeek, selectedClassId, false);
      console.log('🔍 CopyScheduleModal Day Preview:', {
        sourceDay,
        sourceDayWeek,
        selectedClassId,
        resultCount: result.length,
        sampleSchedule: result[0]
      });

      // Additional debugging for empty results
      if (result.length === 0) {
        console.log('⚠️ No schedules found for day copy. Debug info:', {
          sourceDay,
          sourceDayWeek,
          selectedClassId,
          totalSchedulesAvailable: getPreviewSchedules(undefined, sourceDayWeek, selectedClassId, true).length
        });
      }
    } else {
      result = getPreviewSchedules(undefined, sourceWeek, selectedClassId, true);
      console.log('🔍 CopyScheduleModal Week Preview:', {
        sourceWeek,
        selectedClassId,
        resultCount: result.length,
        sampleSchedule: result[0]
      });

      // Additional debugging for empty results
      if (result.length === 0) {
        console.log('⚠️ No schedules found for week copy. Debug info:', {
          sourceWeek,
          selectedClassId
        });
      }
    }

    return result;
  }, [activeTab, sourceDay, sourceDayWeek, sourceWeek, selectedClassId, getPreviewSchedules]);

  // Check conflicts
  const conflicts = useMemo(() => {
    if (!selectedClassId || previewSchedules.length === 0) return [];

    if (activeTab === 'day') {
      // Check conflicts for all selected target days
      const allConflicts: any[] = [];
      targetDaysArray.forEach(day => {
        const dayConflicts = checkCopyConflicts(previewSchedules, day, targetDayWeek, selectedClassId);
        allConflicts.push(...dayConflicts);
      });
      return allConflicts;
    } else {
      // For week copy (single or multiple), check all target weeks
      const allConflicts: any[] = [];
      const schedulesByDay = previewSchedules.reduce((acc, schedule) => {
        const day = schedule.day_of_week;
        if (!acc[day]) acc[day] = [];
        acc[day].push(schedule);
        return acc;
      }, {} as Record<number, any[]>);

      // Check conflicts for all selected target weeks
      targetWeeksArray.forEach(week => {
        Object.entries(schedulesByDay).forEach(([day, daySchedules]) => {
          // 🚀 FIXED: Type assertion to ensure daySchedules is any[]
          const dayConflicts = checkCopyConflicts(daySchedules as any[], parseInt(day), week, selectedClassId);
          allConflicts.push(...dayConflicts);
        });
      });

      return allConflicts;
    }
  }, [activeTab, previewSchedules, targetDaysArray, targetDayWeek, targetWeeksArray, selectedClassId, checkCopyConflicts]);

  const handleCopy = async () => {
    console.log('🚀 Copy operation started:', {
      activeTab,
      selectedClassId,
      previewSchedules: previewSchedules.length,
      sourceDay,
      sourceDayWeek,
      targetDaysArray,
      targetDayWeek,
      sourceWeek,
      targetWeeksArray,
      overwriteConflicts,
      samplePreviewSchedule: previewSchedules[0]
    });

    if (!selectedClassId) {
      console.error('❌ No class selected');
      return;
    }

    if (previewSchedules.length === 0) {
      console.error('❌ No schedules to copy - previewSchedules is empty');
      console.log('Debug info:', {
        activeTab,
        sourceDay,
        sourceDayWeek,
        sourceWeek,
        selectedClassId
      });

      // Show user-friendly error message
      const dayNames = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
      const sourceDayName = activeTab === 'day' ? dayNames[sourceDay] : '';
      const sourceWeekText = activeTab === 'day' ? sourceDayWeek : sourceWeek;

      alert(`Tidak ada jadwal untuk disalin dari ${activeTab === 'day' ? `${sourceDayName} pekan ${sourceWeekText}` : `pekan ${sourceWeekText}`}. Silakan pilih hari/pekan lain yang memiliki jadwal.`);
      return;
    }

    // Validasi untuk copy hari
    if (activeTab === 'day') {
      if (targetDaysArray.length === 0) {
        console.error('❌ No target days specified');
        return;
      }
      if (targetDaysArray.includes(sourceDay) && sourceDayWeek === targetDayWeek) {
        console.error('❌ Source day cannot be in target days for the same week');
        return;
      }
    }

    // Validasi untuk copy pekan
    if (activeTab === 'week') {
      if (targetWeeksArray.length === 0) {
        console.error('❌ No target weeks specified');
        return;
      }
      if (targetWeeksArray.includes(sourceWeek)) {
        console.error('❌ Source week cannot be in target weeks');
        return;
      }
    }

    try {
      console.log('✅ All validations passed, starting copy operation...');

      if (activeTab === 'day') {
        console.log('📅 Copying day with params:', {
          sourceDay,
          targetDays: targetDaysArray,
          sourceWeek: sourceDayWeek,
          targetWeek: targetDayWeek,
          classId: selectedClassId,
          overwriteConflicts
        });

        // Use copyMultipleDays for multiple target days
        await copyMultipleDays.mutateAsync({
          sourceDay,
          targetDays: targetDaysArray,
          sourceWeek: sourceDayWeek,
          targetWeek: targetDayWeek,
          classId: selectedClassId,
          overwriteConflicts
        });
      } else {
        console.log('📅 Copying week with params:', {
          sourceWeek,
          targetWeeks: targetWeeksArray,
          classId: selectedClassId,
          overwriteConflicts
        });

        // Use multiple weeks copy for better performance
        await copyMultipleWeeks.mutateAsync({
          sourceWeek,
          targetWeeks: targetWeeksArray,
          classId: selectedClassId,
          overwriteConflicts
        });
      }

      console.log('✅ Copy operation completed successfully');

      // ✅ CLOSE MODAL AND LET CACHE INVALIDATION WORK
      onClose();

    } catch (error) {
      console.error('❌ Copy error:', error);
    }
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] bg-background border-border backdrop-blur-sm">
        {/* 🚀 ENHANCED: Custom scrollbar styles */}
        {/* <style>
          {`
            .custom-scrollbar {
              scrollbar-width: thin;
              scrollbar-color: #4B5563 #1F2937;
            }
            .custom-scrollbar::-webkit-scrollbar {
              width: 6px;
            }
            .custom-scrollbar::-webkit-scrollbar-track {
              background: #1F2937;
              border-radius: 3px;
            }
            .custom-scrollbar::-webkit-scrollbar-thumb {
              background: #4B5563;
              border-radius: 3px;
            }
            .custom-scrollbar::-webkit-scrollbar-thumb:hover {
              background: #6B7280;
            }
          `}
        </style> */}
        <DialogHeader className="pb-1">
          <DialogTitle className="text-xl font-bold text-foreground flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-500/20">
              <Copy className="h-6 w-6 text-green-400" />
            </div>
            Salin Jadwal
          </DialogTitle>
        </DialogHeader>

        <div className="flex gap-6">
          {/* Left Sidebar - Conflicts Warning */}
          <div className="w-80 space-y-4">
            {/* Conflicts Warning */}
            {conflicts.length > 0 && (
              <Card className="bg-red-500/10 border-red-500/30">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h4 className="font-medium text-red-600 mb-2">
                        {conflicts.length} Konflik Jadwal
                        {activeTab === 'day' && targetDaysArray.length > 0 && (
                          <span className="text-red-500 text-sm font-normal block">
                            di {targetDaysArray.length} hari tujuan
                          </span>
                        )}
                        {activeTab === 'week' && targetWeeksArray.length > 0 && (
                          <span className="text-red-200 text-sm font-normal block">
                            di {targetWeeksArray.length} pekan tujuan
                          </span>
                        )}
                      </h4>
                      <div className="text-sm text-red-200 space-y-1 max-h-40 overflow-y-auto">
                        {conflicts.slice(0, 5).map((conflict, index) => (
                          <div key={index} className="text-xs">
                            • {conflict.conflictTime} - {conflict.newSchedule.subjects?.name}
                          </div>
                        ))}
                        {conflicts.length > 5 && (
                          <div className="text-red-300 text-xs">
                            ... dan {conflicts.length - 5} konflik lainnya
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Options */}
            <Card className="bg-card border-border">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="overwrite"
                    checked={overwriteConflicts}
                    onCheckedChange={(checked) => {
                      // 🚀 FIXED: Handle CheckedState properly
                      setOverwriteConflicts(checked === true);
                    }}
                  />
                  <label htmlFor="overwrite" className="text-sm text-foreground">
                    Timpa jadwal yang konflik
                  </label>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="space-y-3">
              <Button
                onClick={handleCopy}
                disabled={isLoading || (conflicts.length > 0 && !overwriteConflicts)}
                className="w-full bg-green-500 hover:bg-green-600 text-white"
              >
                {isLoading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Menyalin...
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    Salin Jadwal
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={onClose}
                className="w-full border-border text-foreground hover:bg-accent"
              >
                Batal
              </Button>
            </div>

            {/* Source Day - Only show when activeTab is 'day' */}
            {activeTab === 'day' && (
              <Card className="bg-card border-border">
                <CardHeader className="pb-3">
                  <CardTitle className="text-foreground text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-primary" />
                    Hari Sumber
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Select value={sourceDay.toString()} onValueChange={(value) => setSourceDay(parseInt(value))}>
                        <SelectTrigger className="bg-background border-border text-foreground">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-popover border-border">
                          {dayNames.map(day => (
                            <SelectItem key={day.value} value={day.value.toString()}>
                              {day.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <div className="text-center">
                        <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
                          Pekan {sourceDayWeek}
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">
                          {academicWeeks.find(w => w.weekNumber === sourceDayWeek)?.dateRange || 'Tanggal tidak tersedia'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Week Navigation */}
                  <div className="max-h-40 overflow-y-auto border border-border rounded-lg p-3 bg-muted/30 custom-scrollbar">
                    <ModalWeekNavigation
                      selectedWeek={sourceDayWeek}
                      onWeekSelect={setSourceDayWeek}
                      isSelectionMode={false}
                      variant="copy"
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          

          {/* Main Content */}
          <div className="flex-1 space-y-6">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'day' | 'week')}>
              <TabsList className="grid w-full grid-cols-2 bg-muted">
                <TabsTrigger
                  value="day"
                  className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground"
                >
                  <CalendarDays className="h-4 w-4" />
                  Salin Hari
                </TabsTrigger>
                <TabsTrigger
                  value="week"
                  className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground"
                >
                  <CalendarRange className="h-4 w-4" />
                  Salin Pekan
                </TabsTrigger>
              </TabsList>

              <TabsContent value="day" className="space-y-6 mt-6">
                {/* 🚀 FIXED: Simple and elegant layout like "Salin Pekan" */}
                <div className="space-y-6">
                  

                  {/* Target Days */}
                  <Card className="bg-card border-border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-foreground text-sm flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-green-500" />
                        Hari Tujuan (Pilih Multiple)
                      </CardTitle>
                      <div className="text-xs text-muted-foreground mt-1">
                        Klik hari untuk memilih/membatalkan. Bisa pilih beberapa hari sekaligus.
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Day Selection */}
                      <div>
                        <label className="text-sm font-medium text-foreground mb-3 block">Pilih Hari Tujuan</label>
                        <div className="grid grid-cols-4 gap-2">
                          {dayNames.map(day => {
                            const isSelected = selectedTargetDays.has(day.value);
                            const isSourceDay = day.value === sourceDay;
                            return (
                              <Button
                                key={day.value}
                                variant={isSelected ? "default" : "outline"}
                                size="sm"
                                onClick={() => handleTargetDayToggle(day.value)}
                                disabled={isSourceDay && sourceDayWeek === targetDayWeek}
                                className={`h-10 text-xs transition-all duration-200 ${
                                  isSelected
                                    ? 'bg-green-500/80 border-green-400 text-white shadow-lg scale-105 ring-1 ring-green-400/50'
                                    : isSourceDay && sourceDayWeek === targetDayWeek
                                    ? 'bg-gray-600/30 border-gray-600/30 text-gray-500 cursor-not-allowed'
                                    : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600 hover:border-gray-500'
                                }`}
                              >
                                {day.label}
                                {isSourceDay && sourceDayWeek === targetDayWeek && (
                                  <span className="ml-1 text-xs">(Sumber)</span>
                                )}
                              </Button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Target Week */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-foreground block">Pekan Tujuan</label>
                        <div className="text-center">
                          <Badge variant="secondary" className="bg-green-500/20 text-green-600 border-green-500/30">
                            Pekan {targetDayWeek}
                          </Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            {academicWeeks.find(w => w.weekNumber === targetDayWeek)?.dateRange || 'Tanggal tidak tersedia'}
                          </div>
                        </div>
                      </div>

                      {/* Week Navigation */}
                      <div className="max-h-40 overflow-y-auto border border-border rounded-lg p-3 bg-muted/30 custom-scrollbar">
                        <ModalWeekNavigation
                          selectedWeek={targetDayWeek}
                          onWeekSelect={setTargetDayWeek}
                          isSelectionMode={false}
                          variant="copy"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="week" className="space-y-6 mt-6">
                <div className="space-y-6">
                  {/* Source Week */}
                  <Card className="bg-card border-border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-foreground text-sm flex items-center gap-2">
                        <CalendarRange className="h-4 w-4 text-primary" />
                        Pekan Sumber
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Selected Week Display */}
                      <div className="text-center">
                        <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
                          Pekan {sourceWeek}
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">
                          {academicWeeks.find(w => w.weekNumber === sourceWeek)?.dateRange || 'Tanggal tidak tersedia'}
                        </div>
                      </div>

                      {/* Week Navigation */}
                      <div className="max-h-40 overflow-y-auto border border-border rounded-lg p-3 bg-muted/30 custom-scrollbar">
                        <ModalWeekNavigation
                          selectedWeek={sourceWeek}
                          onWeekSelect={setSourceWeek}
                          isSelectionMode={false}
                          variant="copy"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Target Weeks with Navigation */}
                  <Card className="bg-card border-border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-foreground text-sm flex items-center gap-2">
                        <Copy className="h-4 w-4 text-green-500" />
                        Pekan Tujuan (Pilih Multiple)
                      </CardTitle>
                      <div className="text-xs text-muted-foreground mt-1">
                        Klik angka pekan untuk memilih/membatalkan. Bisa pilih beberapa pekan sekaligus.
                      </div>
                    </CardHeader>
                    <CardContent>
                      {/* 🚀 FIXED: Better scroll container for week navigation */}
                      <div className="space-y-4">
                        <div className="max-h-48 overflow-y-auto border border-border rounded-lg p-3 bg-muted/30 custom-scrollbar">
                          <ModalWeekNavigation
                            selectedWeek={sourceWeek}
                            onWeekSelect={handleTargetWeekToggle}
                            selectedTargetWeeks={selectedTargetWeeks}
                            sourceWeek={sourceWeek}
                            isSelectionMode={true}
                            variant="copy"
                          />
                        </div>

                        {/* Selected Weeks Preview */}
                        {targetWeeksArray.length > 0 && (
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-foreground">
                              Pekan Terpilih ({targetWeeksArray.length} pekan):
                            </label>
                            <div className="flex flex-wrap gap-2">
                              {targetWeeksArray.map(week => (
                                <Badge
                                  key={week}
                                  variant="secondary"
                                  className="bg-green-500/20 text-green-600 border-green-500/30 cursor-pointer hover:bg-green-500/30"
                                  onClick={() => handleTargetWeekToggle(week)}
                                >
                                  Pekan {week} ✕
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>

            
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
