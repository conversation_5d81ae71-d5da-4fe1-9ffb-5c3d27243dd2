-- =====================================================
-- CREATE SCHEDULES_VIEW FOR CALENDAR DISPLAY
-- =====================================================
-- This view provides a comprehensive join of schedules with subjects,
-- categories, classes, and teachers for calendar display

-- Drop existing view if it exists
DROP VIEW IF EXISTS schedules_view;

-- Create the comprehensive schedules view
CREATE OR REPLACE VIEW schedules_view AS
SELECT 
  s.id,
  s.academic_week,
  s.academic_year_id,
  s.class_id,
  s.created_at,
  s.day_of_week,
  s.end_time,
  s.notes,
  s.room,
  s.schedule_date,
  s.school_id,
  s.start_time,
  s.subject_id,
  s.teacher_id,
  s.time_session_id,
  s.tujuan_pembelajaran,
  s.materi_pembelajaran,
  s.updated_at,
  
  -- Subject information (prioritize schedule_subjects, then extracurriculars, then legacy subjects)
  COALESCE(ss.name, ext.name, subj.name, '<PERSON>') as subject_name,
  COALESCE(ss.code, subj.code) as subject_code,
  COALESCE(ss.color, ext.color, subj.color, '#6B7280') as subject_color,

  -- Schedule subject specific information
  ss.total_hours_per_year as schedule_subject_hours_per_year,
  ss.standard_duration as schedule_subject_duration,

  -- Session category information (prioritize schedule_subjects, then extracurriculars, then legacy subjects)
  COALESCE(sc_ss.id, sc_ext.id, sc_subj.id) as session_category_id,
  COALESCE(sc_ss.name, sc_ext.name, sc_subj.name) as session_category_name,
  COALESCE(sc_ss.color, sc_ext.color, sc_subj.color) as session_category_color,
  COALESCE(sc_ss.description, sc_ext.description, sc_subj.description) as session_category_description,
  
  -- Class information
  c.name as class_name,
  c.level as class_level,
  c.grade as class_grade,
  
  -- Teacher information
  t.full_name as teacher_name,
  t.nip as teacher_nip,
  t.email as teacher_email
  
FROM class_schedules s
-- Join with legacy subjects table
LEFT JOIN subjects subj ON s.subject_id = subj.id
-- Join with new schedule_subjects table
LEFT JOIN schedule_subjects ss ON s.subject_id = ss.id
-- Join with extracurriculars table (for EKSKUL items)
LEFT JOIN extracurriculars ext ON s.subject_id = ext.id
-- Join session categories from all paths
LEFT JOIN session_categories sc_subj ON subj.session_categories_id = sc_subj.id
LEFT JOIN session_categories sc_ss ON ss.session_category_id = sc_ss.id
LEFT JOIN session_categories sc_ext ON ext.session_category_id = sc_ext.id
-- Join with classes and teachers
LEFT JOIN classes c ON s.class_id = c.id
LEFT JOIN teachers t ON s.teacher_id = t.id;
-- Removed ORDER BY from view for better performance with 70k+ rows
-- Ordering will be handled in application queries as needed

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_class_schedules_view_lookup
ON class_schedules(subject_id, class_id, academic_week, day_of_week);

CREATE INDEX IF NOT EXISTS idx_class_schedules_view_time
ON class_schedules(day_of_week, start_time, end_time)
WHERE day_of_week IS NOT NULL;

-- Add comments
COMMENT ON VIEW schedules_view IS 'Comprehensive view for schedule display with joined subject, category, class, and teacher information. Handles both legacy subjects and new schedule_subjects systems.';

-- Grant permissions (adjust as needed for your RLS setup)
-- Note: Views inherit permissions from underlying tables
