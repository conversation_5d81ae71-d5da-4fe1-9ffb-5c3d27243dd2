import React, { useState } from 'react';
import { Settings, Save, RotateCcw, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { AcademicSettingsTab } from '@/components/settings/AcademicSettingsTab';
import { EducationLevelsTab } from '@/components/settings/EducationLevelsTab';
import { useGeneralSettings, useUpdateGeneralSettings } from '@/hooks/useGeneralSettings';
const GeneralSettingsPage: React.FC = () => {
  const [lessonDuration, setLessonDuration] = useState(45);
  const {
    data: generalSettings,
    isLoading
  } = useGeneralSettings();
  const updateGeneralSettings = useUpdateGeneralSettings();
  const handleSaveSettings = () => {
    if (generalSettings) {
      updateGeneralSettings.mutate({
        ...generalSettings,
        lesson_duration_minutes: lessonDuration
      });
    } else {
      updateGeneralSettings.mutate({
        lesson_duration_minutes: lessonDuration
      });
    }
  };
  React.useEffect(() => {
    if (generalSettings) {
      setLessonDuration(generalSettings.lesson_duration_minutes);
    }
  }, [generalSettings]);
  if (isLoading) {
    return <div className="min-h-screen bg-background p-6 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-purple-400 mx-auto mb-4" />
          <p className="text-muted-foreground">Memuat pengaturan...</p>
        </div>
      </div>;
  }
  return <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between px-[40px]">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <div className="flex items-center space-x-2">
                <Settings className="h-8 w-8 text-purple-400" />
                <h1 className="text-3xl font-bold text-foreground">Pengaturan Umum</h1>
              </div>
            </div>
            <p className="text-muted-foreground">Kelola pengaturan sistem dan preferensi aplikasi</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="bg-red-400/10 border border-red-400/20 text-red-400 hover:bg-ref-400/20 flex items-center justify-center space-x-2">
              <RotateCcw className="h-5 w-5 mr-2" />
              Reset
            </Button>
            <Button onClick={handleSaveSettings} disabled={updateGeneralSettings.isPending} className="bg-lime-400/10 border border-lime-400/20 text-lime-400 hover:bg-lime-400/20 flex items-center justify-center space-x-2">
              {updateGeneralSettings.isPending ? <Loader2 className="h-5 w-5 mr-2 animate-spin" /> : <Save className="h-5 w-5 mr-2" />}
              Simpan
            </Button>
          </div>
        </div>

        {/* Main Content Card */}
        <Card className="bg-card backdrop-blur-sm border-border">
          <CardContent className="p-6">


            {/* Tabbed Interface */}
            <Tabs defaultValue="academic" className="space-y-6">
              <TabsList className="grid w-full grid-cols-2 bg-muted border-border rounded-xl p-1 backdrop-blur-sm">
                <TabsTrigger value="academic" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white text-muted-foreground rounded-lg font-medium transition-all duration-300">
                  Pengaturan Akademik
                </TabsTrigger>
                <TabsTrigger value="education-levels" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white text-muted-foreground rounded-lg font-medium transition-all duration-300">
                  Jenjang Pendidikan
                </TabsTrigger>
              </TabsList>

              <TabsContent value="academic" className="space-y-6 mt-6">
                <AcademicSettingsTab lessonDuration={lessonDuration} setLessonDuration={setLessonDuration} />
              </TabsContent>

              <TabsContent value="education-levels" className="space-y-6 mt-6">
                <EducationLevelsTab />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>;
};
export default GeneralSettingsPage;