
import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useUpdateSchedule, useDeleteSchedule } from '@/hooks/useSchedules';
import { useSubjects } from '@/hooks/useSubjects';
import { useScheduleSubjectsByCategory } from '@/hooks/useScheduleSubjectsByCategory';
import { useSessionCategories } from '@/hooks/useSessionCategories';
import { useTeachers } from '@/hooks/useTeachers';
import { useClasses } from '@/hooks/useClasses';
import { useToast } from '@/hooks/use-toast';
import { Trash2, Save } from 'lucide-react';

interface EditScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  schedule: any;
  onScheduleUpdated?: () => void; // ✅ ADDED: Optional callback for schedule update
}

export const EditScheduleModal: React.FC<EditScheduleModalProps> = ({
  isOpen,
  onClose,
  schedule,
  onScheduleUpdated // ✅ ADDED: Destructure the callback
}) => {
  const { data: subjects } = useSubjects();
  const { data: scheduleSubjectsByCategory = [] } = useScheduleSubjectsByCategory(schedule?.class_id);
  const { data: sessionCategories = [] } = useSessionCategories();
  const { data: teachers } = useTeachers();
  const { data: classes } = useClasses();

  // ✅ Combine subjects from both sources for comprehensive dropdown
  const allSubjects = React.useMemo(() => {
    const subjectsFromCategories = scheduleSubjectsByCategory.flatMap(category =>
      category.subjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        code: subject.code,
        category: category.name
      }))
    );

    const subjectsFromTable = subjects?.map(subject => ({
      id: subject.id,
      name: subject.name,
      code: subject.code,
      category: subject.category || 'Lainnya'
    })) || [];

    // Merge and deduplicate by id
    const merged = [...subjectsFromCategories, ...subjectsFromTable];
    const unique = merged.filter((subject, index, self) =>
      index === self.findIndex(s => s.id === subject.id)
    );

    console.log('🔍 EditScheduleModal - All subjects:', {
      fromCategories: subjectsFromCategories.length,
      fromTable: subjectsFromTable.length,
      merged: merged.length,
      unique: unique.length,
      p5Subjects: unique.filter(s => s.category === 'P5')
    });

    return unique;
  }, [subjects, scheduleSubjectsByCategory]);
  const updateScheduleMutation = useUpdateSchedule();
  const deleteScheduleMutation = useDeleteSchedule();
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    subject_id: '',
    teacher_id: '',
    class_id: '',
    session_category_id: '',
    start_time: '',
    end_time: '',
    room: '',
    notes: '',
    tujuan_pembelajaran: '',
    materi_pembelajaran: ''
  });

  // Pre-populate form when schedule changes
  useEffect(() => {
    if (schedule) {
      setFormData({
        subject_id: schedule.subject_id || '',
        teacher_id: schedule.teacher_id || 'no-teacher',
        class_id: schedule.class_id || '',
        session_category_id: schedule.session_category_id || '',
        start_time: schedule.start_time || '',
        end_time: schedule.end_time || '',
        room: schedule.room || '',
        notes: schedule.notes || '',
        tujuan_pembelajaran: schedule.tujuan_pembelajaran || '',
        materi_pembelajaran: schedule.materi_pembelajaran || ''
      });
    }
  }, [schedule]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!schedule?.id) {
      toast({
        title: "Error",
        description: "ID jadwal tidak ditemukan",
        variant: "destructive",
      });
      return;
    }

    // Validation - Remove teacher requirement
    if (!formData.subject_id || !formData.class_id) {
      toast({
        title: "Validasi Gagal",
        description: "Mata pelajaran dan kelas harus dipilih",
        variant: "destructive",
      });
      return;
    }

    if (!formData.start_time || !formData.end_time) {
      toast({
        title: "Validasi Gagal",
        description: "Waktu mulai dan selesai harus diisi",
        variant: "destructive",
      });
      return;
    }

    if (formData.start_time >= formData.end_time) {
      toast({
        title: "Validasi Gagal",
        description: "Waktu mulai harus lebih awal dari waktu selesai",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('🔄 Updating schedule with data:', {
        id: schedule.id,
        formData
      });

      // Remove session_category_id from update data as it's not a direct field in class_schedules table
      // The session_category_id comes from the related subject through joins in schedules_view
      const { session_category_id, ...updateData } = formData;

      // ✅ FIXED: Convert empty string or "no-teacher" to null for database compatibility
      const finalUpdateData = {
        ...updateData,
        teacher_id: (updateData.teacher_id === '' || updateData.teacher_id === 'no-teacher') ? null : updateData.teacher_id
      };

      await updateScheduleMutation.mutateAsync({
        id: schedule.id,
        ...finalUpdateData
      });

      toast({
        title: "Berhasil",
        description: "Jadwal berhasil diperbarui",
      });

      // ✅ ADDED: Call the callback if provided
      if (onScheduleUpdated) {
        onScheduleUpdated();
      }

      onClose();
    } catch (error) {
      console.error('❌ Error updating schedule:', error);
      toast({
        title: "Gagal",
        description: `Gagal memperbarui jadwal: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!schedule?.id) return;
    
    if (confirm('Apakah Anda yakin ingin menghapus jadwal ini?')) {
      try {
        await deleteScheduleMutation.mutateAsync(schedule.id);
        toast({
          title: "Berhasil",
          description: "Jadwal berhasil dihapus",
        });

        // ✅ ADDED: Call the callback if provided
        if (onScheduleUpdated) {
          onScheduleUpdated();
        }

        onClose();
      } catch (error) {
        console.error('Error deleting schedule:', error);
        toast({
          title: "Gagal",
          description: "Gagal menghapus jadwal",
          variant: "destructive",
        });
      }
    }
  };

  if (!schedule) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] w-[90vw] max-h-[90vh] overflow-y-auto bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white">Edit Jadwal</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="subject" className="text-white">Mata Pelajaran</Label>
              <Select value={formData.subject_id} onValueChange={(value) => setFormData(prev => ({...prev, subject_id: value}))}>
                <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                  <SelectValue placeholder="Pilih mata pelajaran" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {allSubjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id} className="text-white hover:bg-gray-700">
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="category" className="text-white">Kategori</Label>
              <Select value={formData.session_category_id} onValueChange={(value) => setFormData(prev => ({...prev, session_category_id: value}))}>
                <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {sessionCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id} className="text-white hover:bg-gray-700">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        {category.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="teacher" className="text-white">Guru</Label>
              <Select value={formData.teacher_id} onValueChange={(value) => setFormData(prev => ({...prev, teacher_id: value}))}>
                <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                  <SelectValue placeholder="Pilih guru (opsional)" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="no-teacher" className="text-gray-400 hover:bg-gray-700">
                    Tidak ada guru
                  </SelectItem>
                  {teachers?.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id} className="text-white hover:bg-gray-700">
                      {teacher.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="class" className="text-white">Kelas</Label>
            <Select value={formData.class_id} onValueChange={(value) => setFormData(prev => ({...prev, class_id: value}))}>
              <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                <SelectValue placeholder="Pilih kelas" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                {classes?.map((classItem) => (
                  <SelectItem key={classItem.id} value={classItem.id} className="text-white hover:bg-gray-700">
                    {classItem.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start_time" className="text-white">Waktu Mulai</Label>
              <Input
                id="start_time"
                type="time"
                value={formData.start_time}
                onChange={(e) => setFormData(prev => ({...prev, start_time: e.target.value}))}
                className="bg-gray-800 border-gray-600 text-white"
              />
            </div>

            <div>
              <Label htmlFor="end_time" className="text-white">Waktu Selesai</Label>
              <Input
                id="end_time"
                type="time"
                value={formData.end_time}
                onChange={(e) => setFormData(prev => ({...prev, end_time: e.target.value}))}
                className="bg-gray-800 border-gray-600 text-white"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="room" className="text-white">Ruangan</Label>
            <Input
              id="room"
              value={formData.room}
              onChange={(e) => setFormData(prev => ({...prev, room: e.target.value}))}
              placeholder="Masukkan ruangan"
              className="bg-gray-800 border-gray-600 text-white"
            />
          </div>

          {/* Grid layout untuk field yang lebih panjang */}
          <div className="grid grid-cols-2 gap-4">
            {/* Kolom Kiri */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="notes" className="text-white">Catatan Guru</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({...prev, notes: e.target.value}))}
                  placeholder="Masukkan catatan"
                  className="bg-gray-800 border-gray-600 text-white"
                  rows={4}
                />
              </div>
            </div>

            {/* Kolom Kanan */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="tujuan_pembelajaran" className="text-white">Tujuan Pembelajaran</Label>
                <Textarea
                  id="tujuan_pembelajaran"
                  value={formData.tujuan_pembelajaran}
                  onChange={(e) => setFormData(prev => ({...prev, tujuan_pembelajaran: e.target.value}))}
                  placeholder="Masukkan tujuan pembelajaran"
                  className="bg-gray-800 border-gray-600 text-white"
                  rows={4}
                />
              </div>
            </div>
          </div>

          <div>
            <Label htmlFor="materi_pembelajaran" className="text-white">Materi Pembelajaran</Label>
            <Textarea
              id="materi_pembelajaran"
              value={formData.materi_pembelajaran}
              onChange={(e) => setFormData(prev => ({...prev, materi_pembelajaran: e.target.value}))}
              placeholder="Masukkan materi pembelajaran"
              className="bg-gray-800 border-gray-600 text-white"
              rows={3}
            />
          </div>

          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteScheduleMutation.isPending}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Hapus
            </Button>
            
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Batal
              </Button>
              <Button
                type="submit"
                disabled={updateScheduleMutation.isPending}
                className="bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50"
              >
                {updateScheduleMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Simpan
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
