
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUpdateClassSubject, useDeleteClassSubject } from '@/hooks/useClassSubjects';
import { useUpdateExtracurricularClass, useDeleteExtracurricularClass } from '@/hooks/useExtracurricularClasses';
import { useUpdateMatrixClassSubject, useDeleteMatrixClassSubject } from '@/hooks/useMatrixSubjects';
import { Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface EditSubjectAssignmentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  assignment: any;
  subjectName: string;
  className: string;
  isExtracurricular?: boolean;
  isMatrixAssignment?: boolean; // ✅ NEW: Flag to indicate if this is from matrix (schedule_class_subjects)
}

const EditSubjectAssignmentModal: React.FC<EditSubjectAssignmentModalProps> = ({
  open,
  onOpenChange,
  assignment,
  subjectName,
  className,
  isExtracurricular = false,
  isMatrixAssignment = false // ✅ NEW: Default to false for backward compatibility
}) => {
  const [hoursPerYear, setHoursPerYear] = useState(0);

  const updateClassSubject = useUpdateClassSubject();
  const deleteClassSubject = useDeleteClassSubject();
  const updateExtracurricularClass = useUpdateExtracurricularClass();
  const deleteExtracurricularClass = useDeleteExtracurricularClass();

  // ✅ FIXED: Add matrix-specific hooks for schedule_class_subjects table
  const updateMatrixClassSubject = useUpdateMatrixClassSubject();
  const deleteMatrixClassSubject = useDeleteMatrixClassSubject();

  const { toast } = useToast();

  useEffect(() => {
    if (assignment) {
      // Use hours_per_year if available, otherwise calculate from hours_per_week
      setHoursPerYear(assignment.hours_per_year || (assignment.hours_per_week || 0) * 36);
    }
  }, [assignment]);

  const handleSave = async () => {
    try {
      console.log('🔄 Saving assignment with hours per year:', hoursPerYear);
      console.log('🔍 Assignment ID:', assignment.id);
      console.log('🎯 Is extracurricular:', isExtracurricular);
      console.log('📊 Assignment data:', assignment);

      if (isExtracurricular) {
        // ✅ FIXED: Update ekstrakurikuler assignment via extracurricular_classes table
        console.log('🎨 Updating extracurricular class assignment...');
        await updateExtracurricularClass.mutateAsync({
          id: assignment.id,
          hours_per_year: hoursPerYear
        });
      } else if (isMatrixAssignment) {
        // ✅ FIXED: Update matrix assignment via schedule_class_subjects table
        console.log('📊 Updating matrix class subject assignment...');
        await updateMatrixClassSubject.mutateAsync({
          id: assignment.id,
          hours_per_year: hoursPerYear
        });
      } else {
        // ✅ FIXED: Update regular assignment via class_schedules table
        console.log('📚 Updating class subject assignment...');
        await updateClassSubject.mutateAsync({
          id: assignment.id,
          hours_per_year: hoursPerYear
        });
      }

      toast({
        title: "✅ Berhasil",
        description: `Jam pelajaran berhasil diperbarui menjadi ${hoursPerYear} JP/Tahun`,
      });

      console.log('✅ Assignment update successful');
      onOpenChange(false);
    } catch (error) {
      console.error('❌ Error updating assignment:', error);
      toast({
        title: "❌ Gagal",
        description: "Gagal memperbarui jam pelajaran: " + (error as Error).message,
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    const itemType = isExtracurricular ? 'ekstrakurikuler' : 'mata pelajaran';
    if (confirm(`Apakah Anda yakin ingin menghapus ${itemType} "${subjectName}" dari kelas "${className}"?`)) {
      try {
        console.log('🗑️ Deleting assignment:', {
          id: assignment.id,
          type: itemType,
          isExtracurricular
        });

        if (isExtracurricular) {
          // ✅ FIXED: Delete ekstrakurikuler assignment from extracurricular_classes table
          console.log('🎨 Deleting extracurricular class assignment...');
          await deleteExtracurricularClass.mutateAsync(assignment.id);
        } else if (isMatrixAssignment) {
          // ✅ FIXED: Delete matrix assignment from schedule_class_subjects table
          console.log('📊 Deleting matrix class subject assignment...');
          await deleteMatrixClassSubject.mutateAsync(assignment.id);
        } else {
          // ✅ FIXED: Delete regular assignment from class_schedules table
          console.log('📚 Deleting class subject assignment...');
          await deleteClassSubject.mutateAsync(assignment.id);
        }

        console.log('✅ Assignment deletion successful');
        onOpenChange(false);
      } catch (error) {
        console.error('❌ Error deleting assignment:', error);
        toast({
          title: "❌ Gagal",
          description: `Gagal menghapus ${itemType}: ${error.message}`,
          variant: "destructive",
        });
      }
    }
  };

  const handleHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow empty string temporarily while typing
    if (value === '') {
      setHoursPerYear(0);
    } else {
      const numValue = parseInt(value);
      // Allow 0 and positive numbers, prevent negative numbers
      if (!isNaN(numValue) && numValue >= 0) {
        setHoursPerYear(numValue);
      }
    }
  };

  const isLoading = updateClassSubject.isPending || deleteClassSubject.isPending ||
                    updateExtracurricularClass.isPending || deleteExtracurricularClass.isPending ||
                    updateMatrixClassSubject.isPending || deleteMatrixClassSubject.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className={isExtracurricular ? "text-orange-400" : "text-lime-400"}>
            Edit Penugasan {isExtracurricular ? 'Ekstrakurikuler' : 'Mata Pelajaran'}
          </DialogTitle>
          <div className="text-gray-300 text-sm">
            <div>{subjectName}</div>
            <div>Kelas {className}</div>
          </div>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="hoursPerYear" className="text-gray-300">Jam Pelajaran per Tahun</Label>
            <Input
              id="hoursPerYear"
              type="number"
              min="0"
              max="360"
              value={hoursPerYear}
              onChange={handleHoursChange}
              className="bg-gray-700 border-gray-600 text-white"
              placeholder="0"
            />
            <p className="text-xs text-gray-400">Total jam pelajaran dalam satu tahun akademik (0 untuk mata pelajaran yang tidak dijadwalkan)</p>
          </div>

          <div className="border-t border-gray-600 pt-4">
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
              className="w-full bg-red-600 hover:bg-red-700 text-white"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Hapus dari Kelas
            </Button>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => onOpenChange(false)} 
            className="border-gray-600 text-gray-300"
            disabled={isLoading}
          >
            Batal
          </Button>
          <Button 
            type="button"
            onClick={handleSave}
            className="bg-lime-400 hover:bg-lime-500 text-gray-900"
            disabled={isLoading}
          >
            {isLoading ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditSubjectAssignmentModal;
