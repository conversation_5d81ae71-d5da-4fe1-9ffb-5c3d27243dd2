
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Calendar } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { id } from 'date-fns/locale';
import { NationalHoliday, NATIONAL_HOLIDAYS_2024, NATIONAL_HOLIDAYS_2025, DEFAULT_CATEGORIES } from '@/types/event';

interface NationalHolidaysDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentYear: number;
  onSave: (holidays: any[]) => void;
  existingHolidays: string[]; // Array of existing national holiday IDs
}

export const NationalHolidaysDialog: React.FC<NationalHolidaysDialogProps> = ({
  open,
  onOpenChange,
  currentYear,
  onSave,
  existingHolidays,
}) => {
  const [selectedHolidays, setSelectedHolidays] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const nationalHolidays = currentYear === 2024 ? NATIONAL_HOLIDAYS_2024 : NATIONAL_HOLIDAYS_2025;
  const nationalHolidayCategory = DEFAULT_CATEGORIES.find(cat => cat.id === 'libur-nasional');
  
  // Filter out holidays that already exist
  const availableHolidays = nationalHolidays.filter(holiday => 
    !existingHolidays.includes(holiday.id)
  );

  const handleHolidaySelection = (holidayId: string, checked: boolean) => {
    if (checked) {
      setSelectedHolidays(prev => [...prev, holidayId]);
    } else {
      setSelectedHolidays(prev => prev.filter(id => id !== holidayId));
      setSelectAll(false);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedHolidays(availableHolidays.map(holiday => holiday.id));
    } else {
      setSelectedHolidays([]);
    }
  };

  const handleSave = () => {
    const holidaysToAdd = availableHolidays
      .filter(holiday => selectedHolidays.includes(holiday.id))
      .map(holiday => ({
        name: holiday.name,
        start_date: holiday.date,
        end_date: holiday.date,
        description: holiday.description,
        color: nationalHolidayCategory?.color || '#8b5cf6',
        category_id: 'libur-nasional',
        is_national_holiday: true,
        class_ids: [], // All classes
      }));

    onSave(holidaysToAdd);
    setSelectedHolidays([]);
    setSelectAll(false);
    onOpenChange(false);
  };

  const isFormValid = selectedHolidays.length > 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-gradient-to-br from-gray-800/95 via-gray-800/90 to-gray-900/95 border-lime-400/20">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Calendar className="h-5 w-5 text-lime-400" />
            Tambah Libur Nasional {currentYear}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {availableHolidays.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400">
                Semua libur nasional untuk tahun {currentYear} sudah ditambahkan.
              </p>
            </div>
          ) : (
            <>
              <div className="flex items-center space-x-2 pb-2 border-b border-gray-600">
                <Checkbox
                  id="select-all"
                  checked={selectAll}
                  onCheckedChange={handleSelectAll}
                  className="border-lime-400/30"
                />
                <Label htmlFor="select-all" className="text-white font-medium">
                  Pilih Semua ({availableHolidays.length} libur)
                </Label>
              </div>

              <div className="space-y-2 max-h-96 overflow-y-auto">
                {availableHolidays.map(holiday => (
                  <div
                    key={holiday.id}
                    className="flex items-center justify-between p-3 bg-gray-900/50 rounded border border-gray-600/30 hover:border-lime-400/30 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id={holiday.id}
                        checked={selectedHolidays.includes(holiday.id)}
                        onCheckedChange={(checked) => handleHolidaySelection(holiday.id, checked as boolean)}
                        className="border-lime-400/30"
                      />
                      <div>
                        <Label htmlFor={holiday.id} className="text-white font-medium cursor-pointer">
                          {holiday.name}
                        </Label>
                        {holiday.description && (
                          <p className="text-gray-400 text-sm">{holiday.description}</p>
                        )}
                      </div>
                    </div>
                    <div className="text-lime-400 text-sm">
                      {format(parseISO(holiday.date), 'dd MMMM yyyy', { locale: id })}
                    </div>
                  </div>
                ))}
              </div>

              {selectedHolidays.length > 0 && (
                <div className="p-3 bg-lime-400/10 border border-lime-400/30 rounded">
                  <p className="text-lime-400 text-sm">
                    {selectedHolidays.length} libur nasional akan ditambahkan ke kalender
                  </p>
                </div>
              )}
            </>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            Batal
          </Button>
          {availableHolidays.length > 0 && (
            <Button
              type="button"
              onClick={handleSave}
              disabled={!isFormValid}
              className="bg-gradient-to-r from-lime-400 to-lime-500 hover:from-lime-500 hover:to-lime-600 text-gray-900"
            >
              Tambah {selectedHolidays.length} Libur
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
