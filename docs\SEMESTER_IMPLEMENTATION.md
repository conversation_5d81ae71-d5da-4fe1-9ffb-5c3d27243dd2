# Implementasi Fitur Semester

## Overview
Fitur semester telah diimplementasikan untuk mendukung sistem pendidikan Indonesia yang menggunakan sistem 2 semester per tahun ajaran:
- **Semester 1**: Juli - Desember
- **Semester 2**: <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> (tahun berikutnya)

## Struktur Database

### Tabel `semesters`
```sql
CREATE TABLE semesters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,                    -- 'Semester 1', 'Semester 2'
  semester_number INTEGER NOT NULL,      -- 1 atau 2
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT FALSE,       -- Hanya satu semester aktif per sekolah
  academic_year_id UUID NOT NULL,        -- <PERSON><PERSON><PERSON> ke academic_years
  school_id UUID NOT NULL,               -- <PERSON>lasi ke schools
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Constraints & Indexes
- `UNIQUE(semester_number, academic_year_id, school_id)` - <PERSON><PERSON><PERSON><PERSON> duplikasi semester
- `CHECK (semester_number IN (1, 2))` - Validasi nomor semester
- `CHECK (start_date < end_date)` - Validasi tanggal
- Indexes untuk performance pada `academic_year_id`, `school_id`, dan `is_active`

### Auto-Generation
- Semester otomatis dibuat saat academic year baru ditambahkan
- Semester 1 aktif secara default jika academic year aktif
- Trigger otomatis mengatur status aktif semester

## TypeScript Types

### Interface Utama
```typescript
export interface Semester {
  id: string;
  name: string;
  semester_number: 1 | 2;
  start_date: string;
  end_date: string;
  is_active: boolean;
  academic_year_id: string;
  school_id: string;
  created_at: string;
  updated_at: string;
}
```

### Helper Types
- `SemesterNumber`: `1 | 2`
- `SemesterName`: `'Semester 1' | 'Semester 2'`
- `CreateSemesterData`: Interface untuk form input
- `UpdateSemesterData`: Interface untuk update

### Constants
```typescript
export const SEMESTER_CONSTANTS = {
  SEMESTER_1: {
    number: 1,
    name: 'Semester 1',
    description: 'Juli - Desember'
  },
  SEMESTER_2: {
    number: 2,
    name: 'Semester 2', 
    description: 'Januari - Juni'
  }
};
```

## Hooks

### `useSemesters()`
- Mengambil semua semester untuk sekolah
- Termasuk relasi dengan academic_years
- Diurutkan berdasarkan academic_year dan semester_number

### `useActiveSemester()`
- Mengambil semester yang sedang aktif
- Termasuk informasi academic year
- Digunakan untuk konteks aplikasi

### `useSemestersByAcademicYear(academicYearId)`
- Mengambil semester berdasarkan tahun ajaran tertentu
- Berguna untuk manajemen semester per tahun

### `useCreateSemester()`, `useUpdateSemester()`, `useDeleteSemester()`
- CRUD operations untuk semester
- Dengan validasi dan error handling
- Auto-refresh cache setelah operasi

### `useUpdateActiveSemester()`
- Mengaktifkan semester tertentu
- Otomatis menonaktifkan semester lain di sekolah yang sama

### `useAcademicContext()`
- Hook gabungan untuk academic year + semester
- Menyediakan konteks akademik lengkap
- Helper functions dan status checks

## Komponen UI

### `SemesterSelector`
- Dropdown untuk memilih semester aktif
- Menampilkan deskripsi periode (Juli-Desember, dll)
- Badge untuk semester aktif
- Support untuk berbagai ukuran (sm, md, lg)

### `SemesterManagement`
- Interface lengkap untuk manajemen semester
- Grouped by academic year
- CRUD operations dengan konfirmasi
- Visual indicators untuk semester aktif

### `AddSemesterModal`
- Modal untuk tambah/edit semester
- Auto-generate tanggal berdasarkan academic year
- Validasi form dan error handling
- Support untuk edit mode

### `SemesterBadge`
- Komponen untuk menampilkan semester aktif
- Berbagai mode tampilan (dengan/tanpa deskripsi)
- Responsive design
- Loading states

## Integrasi dengan Sistem

### Academic Settings
- Semester selector ditambahkan ke Academic Settings Tab
- Tombol "Kelola" untuk membuka SemesterManagement
- Layout 3-kolom: Academic Year, Semester, Lesson Duration

### Database Migration
- Script `migrate_semesters.sql` untuk database yang sudah ada
- Auto-generate semester untuk academic years existing
- Backward compatibility

### RLS (Row Level Security)
- Policy untuk view, insert, update, delete
- Berdasarkan school_id user
- Konsisten dengan tabel lain

## Helper Functions

### `getSemesterName(semesterNumber)`
- Konversi nomor semester ke nama

### `getSemesterDescription(semesterNumber)`
- Deskripsi periode semester

### `getCurrentSemester(date?)`
- Deteksi semester berdasarkan tanggal
- Default menggunakan tanggal hari ini

### `getSemesterDateRange(academicYearStart, semesterNumber)`
- Generate tanggal mulai/selesai semester
- Berdasarkan tahun ajaran dan nomor semester

## Penggunaan

### Basic Usage
```typescript
// Mengambil semester aktif
const { data: activeSemester } = useActiveSemester();

// Mengambil konteks akademik lengkap
const { 
  academicPeriodDisplay,
  isAcademicSetupComplete,
  semesterInfo 
} = useAcademicContext();

// Mengaktifkan semester
const updateActiveSemester = useUpdateActiveSemester();
updateActiveSemester.mutate(semesterId);
```

### Komponen
```tsx
// Selector semester
<SemesterSelector className="w-full" showDescription={true} />

// Badge semester
<SemesterBadge size="sm" showDateRange={true} />

// Manajemen semester
<SemesterManagement />
```

## Migration Steps

1. **Database**: Jalankan `migrate_semesters.sql`
2. **Types**: Update Supabase types jika diperlukan
3. **Components**: Import komponen semester yang dibutuhkan
4. **Integration**: Tambahkan ke Academic Settings atau komponen lain

## Best Practices

1. **Selalu gunakan `useAcademicContext()`** untuk konteks akademik lengkap
2. **Validasi semester aktif** sebelum operasi yang bergantung semester
3. **Handle loading states** untuk UX yang baik
4. **Gunakan helper functions** untuk konsistensi
5. **Test dengan berbagai skenario** (semester kosong, multiple academic years, dll)

## Troubleshooting

### Semester tidak muncul
- Pastikan academic year sudah dibuat
- Check trigger auto-generation berjalan
- Verify RLS policies

### Semester tidak bisa diaktifkan
- Check user permissions
- Verify school_id consistency
- Check database constraints

### UI tidak update
- Verify query invalidation
- Check loading states
- Ensure proper error handling
