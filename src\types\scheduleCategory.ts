// =====================================================
// SCHEDULE CATEGORY TYPES
// =====================================================
// Type definitions untuk sistem kategori jadwal yang fleksibel

// Base interface untuk schedule category
export interface ScheduleCategory {
  id: string;
  name: string;
  color: string;
  description?: string;
  is_system_category: boolean;
  school_id: string;
  academic_year_id: string;
  created_at: string;
  updated_at: string;
}

// Interface untuk schedule subject (mata pelajaran khusus jadwal)
export interface ScheduleSubject {
  id: string;
  name: string;
  code?: string;
  color: string;
  total_hours_per_year: number;
  standard_duration: number;
  schedule_category_id: string;
  school_id: string;
  academic_year_id: string;
  created_at: string;
  updated_at: string;
}

// Interface untuk schedule subject classes (relasi)
export interface ScheduleSubjectClass {
  id: string;
  schedule_subject_id: string;
  class_id: string;
  hours_per_week: number;
  hours_per_year: number;
  created_at: string;
  updated_at: string;
  // Relations
  class?: {
    id: string;
    name: string;
    level: string;
    grade: string;
    capacity?: number;
    school_id: string;
    academic_year_id: string;
  };
  schedule_subject?: ScheduleSubjectWithRelations;
}

// Interface untuk schedule subject dengan relasi
export interface ScheduleSubjectWithRelations extends ScheduleSubject {
  schedule_category?: {
    id: string;
    name: string;
    color: string;
    description?: string;
  };
}

// Interface untuk create schedule class subject
export interface CreateScheduleClassSubjectData {
  schedule_subject_id: string;
  class_id: string;
  hours_per_week?: number;
  hours_per_year?: number;
}

// Interface untuk form input schedule category
export interface CreateScheduleCategoryData {
  name: string;
  color: string;
  description?: string;
  is_system_category?: boolean;
}

// Interface untuk form input schedule subject
export interface CreateScheduleSubjectData {
  name: string;
  code?: string;
  color: string;
  total_hours_per_year: number;
  standard_duration: number;
  schedule_category_id: string;
  selected_classes: string[];
}

// Interface untuk update schedule category
export interface UpdateScheduleCategoryData {
  name?: string;
  color?: string;
  description?: string;
}

// Interface untuk update schedule subject
export interface UpdateScheduleSubjectData {
  name?: string;
  code?: string;
  color?: string;
  total_hours_per_year?: number;
  standard_duration?: number;
  schedule_category_id?: string;
}

// Interface untuk schedule subject item (untuk UI)
export interface ScheduleSubjectItem {
  id: string;
  name: string;
  code?: string;
  color: string;
  total_hours_per_year: number;
  category: string; // Nama kategori untuk display
  type: 'schedule_subject' | 'mata_pelajaran' | 'ekstrakurikuler';
  source: 'schedule' | 'mata_pelajaran'; // Sumber data
}

// Interface untuk kategori dengan subjects
export interface CategoryWithScheduleSubjects {
  id: string;
  name: string;
  color: string;
  description?: string;
  is_system_category: boolean;
  subjects: ScheduleSubjectItem[];
}

// Interface untuk form modal schedule subject
export interface ScheduleSubjectFormData {
  name: string;
  code: string;
  color: string;
  total_hours_per_year: number;
  standard_duration: number;
  schedule_category_id: string;
  selected_classes: string[];
}

// Interface untuk statistik schedule subjects
export interface ScheduleSubjectStats {
  totalSubjects: number;
  categoryStats: Record<string, number>;
  totalClasses: number;
  averageHoursPerYear: number;
  systemCategories: number;
  customCategories: number;
}

// Enum untuk class selection mode
export enum ScheduleClassSelectionMode {
  SINGLE = 'single',
  MULTIPLE = 'multiple',
  ALL = 'all'
}

// Interface untuk mata pelajaran dari menu MATA PELAJARAN (untuk integrasi)
export interface MataPelajaranItem {
  id: string;
  name: string;
  code?: string;
  color: string;
  total_hours_per_year: number;
  category: string; // 'KBM' atau 'EKSTRAKURIKULER'
  type: 'mata_pelajaran';
  source: 'mata_pelajaran';
}

// Interface untuk ekstrakurikuler dari menu MATA PELAJARAN (untuk integrasi)
export interface EkstrakurikulerItem {
  id: string;
  name: string;
  color: string;
  category: string; // 'EKSTRAKURIKULER'
  type: 'ekstrakurikuler';
  source: 'mata_pelajaran';
}

// Union type untuk semua item yang bisa ditampilkan di sidebar
export type SidebarItem = ScheduleSubjectItem | MataPelajaranItem | EkstrakurikulerItem;

// Interface untuk response API yang menggabungkan data
export interface CombinedScheduleData {
  categories: CategoryWithScheduleSubjects[];
  stats: ScheduleSubjectStats;
}

// Interface untuk filter dan search
export interface ScheduleFilter {
  categoryId?: string;
  classId?: string;
  searchTerm?: string;
  showSystemOnly?: boolean;
  showCustomOnly?: boolean;
}

// Default colors untuk schedule categories
export const SCHEDULE_CATEGORY_COLORS = [
  '#10B981', // Emerald
  '#F59E0B', // Amber
  '#8B5CF6', // Violet
  '#EF4444', // Red
  '#F97316', // Orange
  '#3B82F6', // Blue
  '#EC4899', // Pink
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#6366F1', // Indigo
];

// System category names
export const SYSTEM_CATEGORIES = {
  KBM: 'KBM',
  EKSKUL: 'Ekskul',
  KEASRAMAAN: 'Keasramaan',
  LIBURAN: 'Liburan',
  P5: 'P5'
} as const;

export type SystemCategoryName = typeof SYSTEM_CATEGORIES[keyof typeof SYSTEM_CATEGORIES];
