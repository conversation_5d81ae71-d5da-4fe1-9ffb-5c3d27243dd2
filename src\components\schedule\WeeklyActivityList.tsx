import React, { useMemo } from 'react';
import { useSchedulesComplete } from '@/hooks/useSchedulesPaginated';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';
import { format, startOfWeek, addDays } from 'date-fns';
import { id } from 'date-fns/locale';
import { BookOpen, Clock, Target, FileText, MessageSquare } from 'lucide-react';

interface WeeklyActivityListProps {
  selectedWeek: number;
  selectedClassId: string | null;
  selectedDate: Date | undefined;
}

export const WeeklyActivityList: React.FC<WeeklyActivityListProps> = ({
  selectedWeek,
  selectedClassId,
  selectedDate
}) => {
  // 🚀 FIXED: Use paginated hook to get ALL data including weeks 14-24
  const { data: schedules = [], isLoading: schedulesLoading, error: schedulesError } = useSchedulesComplete();
  const { academicWeeks } = useAcademicWeeks();

  // Get current week date range
  const currentWeek = academicWeeks.find(week => week.weekNumber === selectedWeek);

  // Filter schedules for the selected week and class
  const weeklyActivities = useMemo(() => {
    console.log('🔍 WeeklyActivityList Debug (PAGINATED):', {
      selectedDate,
      selectedClassId,
      selectedWeek,
      totalSchedules: schedules?.length || 0,
      isLoading: schedulesLoading,
      error: schedulesError,
      sampleSchedules: schedules?.slice(0, 3).map(s => ({
        id: s.id,
        academic_week: s.academic_week,
        class_id: s.class_id,
        subject_name: s.subject_name,
        day_of_week: s.day_of_week
      }))
    });

    if (!selectedDate || !selectedClassId) {
      console.log('❌ WeeklyActivityList: Missing selectedDate or selectedClassId');
      return [];
    }

    const weekStart = startOfWeek(selectedDate, { weekStartsOn: 1 });

    const filteredSchedules = schedules
      .filter(schedule => {
        // Filter by class
        if (schedule.class_id !== selectedClassId) return false;

        // Filter by academic week
        if (schedule.academic_week !== selectedWeek) return false;

        return true;
      });

    console.log('📋 WeeklyActivityList Filtered (PAGINATED):', {
      filteredCount: filteredSchedules.length,
      selectedWeek,
      selectedClassId,
      filteredSchedules: filteredSchedules.map(s => ({
        id: s.id,
        subject_name: s.subject_name,
        day_of_week: s.day_of_week,
        start_time: s.start_time,
        end_time: s.end_time,
        academic_week: s.academic_week
      }))
    });

    // 🔍 ENHANCED DEBUG: Analisis khusus untuk minggu 14+
    if (selectedWeek >= 14) {
      console.log(`🔍 MINGGU ${selectedWeek} ANALYSIS:`, {
        totalSchedulesInData: schedules?.length || 0,
        schedulesForThisWeek: schedules?.filter(s => s.academic_week === selectedWeek).length || 0,
        schedulesForThisClass: schedules?.filter(s => s.class_id === selectedClassId).length || 0,
        schedulesForThisWeekAndClass: filteredSchedules.length,
        weekDistribution: schedules?.reduce((acc: any, s: any) => {
          acc[s.academic_week] = (acc[s.academic_week] || 0) + 1;
          return acc;
        }, {}),
        availableWeeks: [...new Set(schedules?.map(s => s.academic_week))].sort((a, b) => a - b)
      });
    }

    return filteredSchedules
      .map(schedule => {
        // ✅ FIXED: Use subject data directly from schedule object (from schedules_view or joins)
        const subjectName = schedule.subject_name ||
                           schedule.schedule_subject_name ||
                           schedule.subjects?.name ||
                           schedule.schedule_subjects?.name ||
                           schedule.extracurriculars?.name ||
                           'Mata Pelajaran';

        const subjectColor = schedule.subject_color ||
                            schedule.schedule_subject_color ||
                            schedule.subjects?.color ||
                            schedule.schedule_subjects?.color ||
                            schedule.extracurriculars?.color ||
                            '#6B7280';

        // Calculate JP (hours) - convert minutes to hours
        const startTime = new Date(`2000-01-01 ${schedule.start_time}`);
        const endTime = new Date(`2000-01-01 ${schedule.end_time}`);
        const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
        const jp = Math.round(durationMinutes / 45); // 45 minutes = 1 JP

        // Get day name - day_of_week: 1=Senin, 2=Selasa, ..., 7=Minggu
        const dayNames = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
        const dayName = dayNames[schedule.day_of_week || 0];



        return {
          id: schedule.id,
          mapel: subjectName,
          jp: jp,
          tujuan_pembelajaran: schedule.tujuan_pembelajaran || '-',
          materi_pembelajaran: schedule.materi_pembelajaran || '-',
          catatan_guru: schedule.notes || '-',
          day: dayName,
          time: `${schedule.start_time} - ${schedule.end_time}`,
          color: subjectColor
        };
      })
      .sort((a, b) => {
        // Sort by day of week, then by time
        const dayOrder = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
        const dayDiff = dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day);
        if (dayDiff !== 0) return dayDiff;
        return a.time.localeCompare(b.time);
      });
  }, [schedules, selectedWeek, selectedClassId, selectedDate]);

  if (!selectedClassId) {
    return (
      <div className="mt-8 p-6 bg-card rounded-lg border border-border">
        <div className="text-center text-muted-foreground">
          <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>Pilih kelas untuk melihat daftar kegiatan mingguan</p>
        </div>
      </div>
    );
  }

  // 🚀 LOADING STATE: Show loading while fetching paginated data
  if (schedulesLoading) {
    return (
      <div className="mt-8 p-6 bg-card rounded-lg border border-border">
        <div className="text-center text-muted-foreground">
          <div className="flex items-center justify-center space-x-3 mb-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <BookOpen className="h-8 w-8 opacity-50" />
          </div>
          <p>Memuat daftar kegiatan mingguan...</p>
        </div>
      </div>
    );
  }

  // 🚨 ERROR STATE: Show error if data fetching fails
  if (schedulesError) {
    return (
      <div className="mt-8 p-6 bg-red-500/10 border border-red-500/30 rounded-lg">
        <div className="text-center">
          <BookOpen className="h-12 w-12 mx-auto mb-3 text-red-500" />
          <div className="text-red-600 mb-2">❌ Gagal memuat daftar kegiatan</div>
          <div className="text-muted-foreground text-sm">{schedulesError.message}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BookOpen className="h-6 w-6 text-primary" />
          <h2 className="text-xl font-bold text-foreground">Daftar Kegiatan Mingguan</h2>
        </div>
        {currentWeek && (
          <div className="text-sm text-muted-foreground">
            Minggu {selectedWeek} • {currentWeek.dateRange}
          </div>
        )}
      </div>

      {/* Activity List */}
      {weeklyActivities.length === 0 ? (
        <div className="p-8 bg-card rounded-lg border border-border text-center">
          <Clock className="h-12 w-12 mx-auto mb-3 text-muted-foreground" />
          <p className="text-muted-foreground">Belum ada kegiatan terjadwal untuk minggu ini</p>
        </div>
      ) : (
        <div className="bg-card rounded-lg border border-border overflow-hidden">
          {/* Table Header */}
          <div className="bg-muted/50 px-6 py-4 border-b border-border">
            <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-foreground">
              <div className="col-span-2 flex items-center space-x-2">
                <BookOpen className="h-4 w-4 text-primary" />
                <span>MAPEL</span>
              </div>
              <div className="col-span-1 flex items-center space-x-2">
                <Clock className="h-4 w-4 text-blue-500" />
                <span>JP</span>
              </div>
              <div className="col-span-3 flex items-center space-x-2">
                <Target className="h-4 w-4 text-green-500" />
                <span>TUJUAN PEMBELAJARAN</span>
              </div>
              <div className="col-span-3 flex items-center space-x-2">
                <FileText className="h-4 w-4 text-purple-500" />
                <span>MATERI PEMBELAJARAN</span>
              </div>
              <div className="col-span-3 flex items-center space-x-2">
                <MessageSquare className="h-4 w-4 text-orange-500" />
                <span>CATATAN GURU</span>
              </div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-border">
            {weeklyActivities.map((activity, index) => (
              <div key={activity.id} className="px-6 py-4 hover:bg-accent/30 transition-colors">
                <div className="grid grid-cols-12 gap-4 text-sm">
                  {/* MAPEL */}
                  <div className="col-span-2">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-3 h-3 rounded-full flex-shrink-0"
                        style={{ backgroundColor: activity.color }}
                      />
                      <div>
                        <div className="font-medium text-foreground">{activity.mapel}</div>
                        <div className="text-xs text-muted-foreground">{activity.day} • {activity.time}</div>
                      </div>
                    </div>
                  </div>

                  {/* JP */}
                  <div className="col-span-1">
                    <div className="inline-flex items-center px-2 py-1 rounded-full bg-blue-500/20 text-blue-600 text-xs font-medium">
                      {activity.jp} JP
                    </div>
                  </div>

                  {/* TUJUAN PEMBELAJARAN */}
                  <div className="col-span-3">
                    <div className="text-foreground text-sm leading-relaxed">
                      {activity.tujuan_pembelajaran}
                    </div>
                  </div>

                  {/* MATERI PEMBELAJARAN */}
                  <div className="col-span-3">
                    <div className="text-foreground text-sm leading-relaxed">
                      {activity.materi_pembelajaran}
                    </div>
                  </div>

                  {/* CATATAN GURU */}
                  <div className="col-span-3">
                    <div className="text-foreground text-sm leading-relaxed">
                      {activity.catatan_guru}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Summary Footer */}
          <div className="bg-muted/30 px-6 py-3 border-t border-border">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Total kegiatan: {weeklyActivities.length}</span>
              <span>Total JP: {weeklyActivities.reduce((sum, activity) => sum + activity.jp, 0)}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
