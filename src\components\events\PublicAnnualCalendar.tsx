import React from 'react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { Holiday } from '@/types/event';
import { PublicMonthCalendar } from '@/components/events/PublicMonthCalendar';

interface PublicAnnualCalendarProps {
  year: number;
  holidays: Holiday[];
  selectedClassId?: string;
}

const PublicAnnualCalendar: React.FC<PublicAnnualCalendarProps> = ({
  year,
  holidays,
  selectedClassId,
}) => {
  // Filter holidays based on selected class - same logic as AnnualCalendar
  const filteredHolidays = holidays.filter(holiday => {
    if (!selectedClassId || selectedClassId === 'all') return true;
    if (!holiday.class_ids || holiday.class_ids.length === 0) return true; // All classes
    return holiday.class_ids.includes(selectedClassId);
  });

  // Generate months for academic year (July to June) - same logic as AnnualCalendar
  const generateAcademicMonths = (academicYear: number) => {
    const months = [];
    
    // First semester: July - December
    for (let i = 6; i < 12; i++) {
      months.push(new Date(academicYear, i, 1));
    }
    
    // Second semester: January - June
    for (let i = 0; i < 6; i++) {
      months.push(new Date(academicYear + 1, i, 1));
    }
    
    return months;
  };

  const months = generateAcademicMonths(year);
  const firstSemester = months.slice(0, 6);
  const secondSemester = months.slice(6, 12);

  const renderSemester = (semesterMonths: Date[], title: string) => (
    <div className="mb-8">
      <h3 className="text-xl font-semibold text-foreground mb-4 text-center">
        {title}
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {semesterMonths.map((month, index) => (
          <PublicMonthCalendar
            key={`${format(month, 'yyyy-MM')}-${index}`}
            month={month}
            holidays={filteredHolidays}
          />
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      {renderSemester(firstSemester, `Semester 1`)}
      {renderSemester(secondSemester, `Semester 2`)}
    </div>
  );
};

export default PublicAnnualCalendar;