-- =====================================================
-- INSERT DEFAULT SCHEDULE CATEGORIES
-- =====================================================
-- Script untuk insert kategori sistem default untuk setiap sekolah dan tahun akademik

DO $$
DECLARE
    rec RECORD;
    category_exists INTEGER;
BEGIN
    -- Loop untuk setiap kombinasi school_id dan academic_year_id yang aktif
    FOR rec IN 
        SELECT DISTINCT s.id as school_id, ay.id as academic_year_id, s.name as school_name, ay.year as academic_year
        FROM schools s
        CROSS JOIN academic_years ay
        WHERE ay.is_active = true
    LOOP
        RAISE NOTICE 'Processing school: % (%) - Academic Year: %', rec.school_name, rec.school_id, rec.academic_year;
        
        -- 1. Insert KBM category
        SELECT COUNT(*) INTO category_exists
        FROM schedule_categories 
        WHERE name = 'KBM' 
          AND school_id = rec.school_id 
          AND academic_year_id = rec.academic_year_id;
          
        IF category_exists = 0 THEN
            INSERT INTO public.schedule_categories (name, color, is_system_category, school_id, academic_year_id, description) 
            VALUES ('KBM', '#10B981', true, rec.school_id, rec.academic_year_id, 'Kegiatan Belajar Mengajar');
            RAISE NOTICE '✅ Created KBM category for school %', rec.school_name;
        ELSE
            RAISE NOTICE '⚠️ KBM category already exists for school %', rec.school_name;
        END IF;
        
        -- 2. Insert Ekskul category
        SELECT COUNT(*) INTO category_exists
        FROM schedule_categories 
        WHERE name = 'Ekskul' 
          AND school_id = rec.school_id 
          AND academic_year_id = rec.academic_year_id;
          
        IF category_exists = 0 THEN
            INSERT INTO public.schedule_categories (name, color, is_system_category, school_id, academic_year_id, description) 
            VALUES ('Ekskul', '#F59E0B', true, rec.school_id, rec.academic_year_id, 'Ekstrakurikuler');
            RAISE NOTICE '✅ Created Ekskul category for school %', rec.school_name;
        ELSE
            RAISE NOTICE '⚠️ Ekskul category already exists for school %', rec.school_name;
        END IF;
        
        -- 3. Insert Keasramaan category
        SELECT COUNT(*) INTO category_exists
        FROM schedule_categories 
        WHERE name = 'Keasramaan' 
          AND school_id = rec.school_id 
          AND academic_year_id = rec.academic_year_id;
          
        IF category_exists = 0 THEN
            INSERT INTO public.schedule_categories (name, color, is_system_category, school_id, academic_year_id, description) 
            VALUES ('Keasramaan', '#8B5CF6', true, rec.school_id, rec.academic_year_id, 'Kegiatan Keasramaan');
            RAISE NOTICE '✅ Created Keasramaan category for school %', rec.school_name;
        ELSE
            RAISE NOTICE '⚠️ Keasramaan category already exists for school %', rec.school_name;
        END IF;
        
        -- 4. Insert Liburan category
        SELECT COUNT(*) INTO category_exists
        FROM schedule_categories 
        WHERE name = 'Liburan' 
          AND school_id = rec.school_id 
          AND academic_year_id = rec.academic_year_id;
          
        IF category_exists = 0 THEN
            INSERT INTO public.schedule_categories (name, color, is_system_category, school_id, academic_year_id, description) 
            VALUES ('Liburan', '#EF4444', true, rec.school_id, rec.academic_year_id, 'Periode Liburan');
            RAISE NOTICE '✅ Created Liburan category for school %', rec.school_name;
        ELSE
            RAISE NOTICE '⚠️ Liburan category already exists for school %', rec.school_name;
        END IF;
        
        -- 5. Insert P5 category
        SELECT COUNT(*) INTO category_exists
        FROM schedule_categories 
        WHERE name = 'P5' 
          AND school_id = rec.school_id 
          AND academic_year_id = rec.academic_year_id;
          
        IF category_exists = 0 THEN
            INSERT INTO public.schedule_categories (name, color, is_system_category, school_id, academic_year_id, description) 
            VALUES ('P5', '#F97316', true, rec.school_id, rec.academic_year_id, 'Projek Penguatan Profil Pelajar Pancasila');
            RAISE NOTICE '✅ Created P5 category for school %', rec.school_name;
        ELSE
            RAISE NOTICE '⚠️ P5 category already exists for school %', rec.school_name;
        END IF;
        
        RAISE NOTICE '🎉 Completed processing for school: %', rec.school_name;
        RAISE NOTICE '---';
        
    END LOOP;
    
    -- Summary
    RAISE NOTICE '📊 SUMMARY:';
    RAISE NOTICE 'Total schedule categories created: %', (SELECT COUNT(*) FROM schedule_categories);
    RAISE NOTICE 'System categories: %', (SELECT COUNT(*) FROM schedule_categories WHERE is_system_category = true);
    RAISE NOTICE 'Custom categories: %', (SELECT COUNT(*) FROM schedule_categories WHERE is_system_category = false);
    
END $$;

-- Verify the results
SELECT 
    sc.name as category_name,
    sc.color,
    sc.is_system_category,
    s.name as school_name,
    ay.year as academic_year,
    sc.description
FROM schedule_categories sc
JOIN schools s ON sc.school_id = s.id
JOIN academic_years ay ON sc.academic_year_id = ay.id
ORDER BY s.name, sc.is_system_category DESC, sc.name;
