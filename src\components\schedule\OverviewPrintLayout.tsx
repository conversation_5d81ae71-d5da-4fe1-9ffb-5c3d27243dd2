import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar } from 'lucide-react';

interface OverviewPrintLayoutProps {
  schedules: any[];
  activeAcademicYear: any;
  academicWeeks: any[];
  selectedSubjectName?: string;
}

const OverviewPrintLayout: React.FC<OverviewPrintLayoutProps> = ({
  schedules,
  activeAcademicYear,
  academicWeeks,
  selectedSubjectName
}) => {
  // Calculate statistics
  const totalSchedules = schedules.length;

  // Convert academic_week and day_of_week to actual dates (SAME AS YearlyCalendarView)
  const scheduleDatesSet = new Set<string>();
  schedules.forEach(schedule => {
    // Use academic_week and day_of_week to calculate actual date
    if (schedule.academic_week && schedule.day_of_week && academicWeeks.length > 0) {
      const academicWeek = academicWeeks.find(week => week.weekNumber === schedule.academic_week);

      if (academicWeek) {
        // Calculate the actual date based on week start and day of week
        const weekStartDate = academicWeek.startDate instanceof Date
          ? academicWeek.startDate
          : new Date(academicWeek.startDate);
        const dayOffset = schedule.day_of_week === 7 ? 6 : schedule.day_of_week - 1; // Convert to 0-based, Sunday = 6

        const scheduleDate = new Date(weekStartDate);
        scheduleDate.setDate(weekStartDate.getDate() + dayOffset);

        const dateString = scheduleDate.toISOString().split('T')[0]; // Format: yyyy-MM-dd
        scheduleDatesSet.add(dateString);
      }
    }

    // Fallback: if schedule_date is available, use it directly
    if (schedule.schedule_date) {
      scheduleDatesSet.add(schedule.schedule_date);
    }
  });

  const activeDays = scheduleDatesSet.size;
  const totalMinutes = schedules.reduce((total, schedule) => {
    const startTime = schedule.start_time || '00:00';
    const endTime = schedule.end_time || '00:00';
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);
    const duration = (endHour * 60 + endMin) - (startHour * 60 + startMin);
    return total + (duration > 0 ? duration : 0);
  }, 0);
  const totalHours = Math.round(totalMinutes / 60 * 10) / 10;
  const totalJP = Math.round(totalMinutes / 45);

  console.log('📊 Print Layout Debug:', {
    totalSchedules,
    activeDays,
    totalMinutes,
    totalHours,
    totalJP,
    sampleSchedule: schedules[0],
    allSchedules: schedules,
    academicWeeksCount: academicWeeks.length,
    sampleAcademicWeek: academicWeeks[0],
    activeAcademicYear
  });

  // Generate calendar months (12 months)
  const months = [
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember',
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni'
  ];

  // Get schedules per month and dates with schedules
  const getMonthData = (monthIndex: number) => {
    const year = activeAcademicYear?.start_year || 2025;
    const actualMonth = monthIndex < 6 ? monthIndex + 7 : monthIndex - 5; // Jul=7, Aug=8, ..., Jun=6
    const actualYear = monthIndex < 6 ? year : year + 1;

    const monthSchedules = schedules.filter(schedule => {
      // Use academic_week and day_of_week to calculate actual date (SAME AS YearlyCalendarView)
      if (schedule.academic_week && schedule.day_of_week && academicWeeks.length > 0) {
        const academicWeek = academicWeeks.find(week => week.weekNumber === schedule.academic_week);
        if (academicWeek) {
          // Calculate the actual date based on week start and day of week
          const weekStartDate = academicWeek.startDate instanceof Date
            ? academicWeek.startDate
            : new Date(academicWeek.startDate);
          const dayOffset = schedule.day_of_week === 7 ? 6 : schedule.day_of_week - 1;

          const scheduleDate = new Date(weekStartDate);
          scheduleDate.setDate(weekStartDate.getDate() + dayOffset);

          return scheduleDate.getMonth() + 1 === actualMonth && scheduleDate.getFullYear() === actualYear;
        }
      }

      // Fallback: if schedule_date is available, use it directly
      if (schedule.schedule_date) {
        const scheduleDate = new Date(schedule.schedule_date);
        return scheduleDate.getMonth() + 1 === actualMonth && scheduleDate.getFullYear() === actualYear;
      }

      return false;
    });

    const scheduleDates = new Set<number>();
    monthSchedules.forEach(schedule => {
      // Use academic_week and day_of_week to calculate actual date (SAME AS YearlyCalendarView)
      if (schedule.academic_week && schedule.day_of_week && academicWeeks.length > 0) {
        const academicWeek = academicWeeks.find(week => week.weekNumber === schedule.academic_week);
        if (academicWeek) {
          // Calculate the actual date based on week start and day of week
          const weekStartDate = academicWeek.startDate instanceof Date
            ? academicWeek.startDate
            : new Date(academicWeek.startDate);
          const dayOffset = schedule.day_of_week === 7 ? 6 : schedule.day_of_week - 1;

          const scheduleDate = new Date(weekStartDate);
          scheduleDate.setDate(weekStartDate.getDate() + dayOffset);

          scheduleDates.add(scheduleDate.getDate());
        }
      }

      // Fallback: if schedule_date is available, use it directly
      if (schedule.schedule_date) {
        const date = new Date(schedule.schedule_date);
        scheduleDates.add(date.getDate());
      }
    });

    console.log(`📅 Month ${months[monthIndex]} (${actualMonth}/${actualYear}):`, {
      monthSchedules: monthSchedules.length,
      scheduleDates: Array.from(scheduleDates),
      sampleSchedule: monthSchedules[0]
    });

    return {
      count: monthSchedules.length,
      scheduleDates,
      year: actualYear,
      month: actualMonth
    };
  };

  // Generate calendar days for a month
  const generateCalendarDays = (year: number, month: number) => {
    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0);
    const daysInMonth = lastDay.getDate();
    let startingDayOfWeek = firstDay.getDay(); // 0 = Sunday

    // Convert to Monday = 0, Sunday = 6
    startingDayOfWeek = startingDayOfWeek === 0 ? 6 : startingDayOfWeek - 1;

    const days = [];

    // Add empty cells for days before the first day of month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  return (
    <>
      <style jsx>{`
        .calendar-day-box {
          width: 20px !important;
          height: 20px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          box-sizing: border-box !important;
          padding: 0 !important;
          margin: 0 !important;
        }

        .calendar-day-number {
          font-size: 8px !important;
          line-height: 1 !important;
          margin: 0 !important;
          padding: 0 !important;
          text-align: center !important;
          display: block !important;
        }

        @media print {
          .calendar-day-box {
            width: 20px !important;
            height: 20px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            box-sizing: border-box !important;
            padding: 0 !important;
            margin: 0 !important;
          }

          .calendar-day-number {
            font-size: 8px !important;
            line-height: 1 !important;
            margin: 0 !important;
            padding: 0 !important;
            text-align: center !important;
            display: block !important;
          }
        }
      `}</style>
      <div className="w-full h-screen bg-white p-4 print:p-2" style={{ minWidth: '1200px' }}>
      {/* Header - Ultra Compact */}
      <div className="text-center mb-4">
        <h1 className="text-xl font-bold text-gray-900 mb-1">
          PENANGGALAN JADWAL MAPEL
        </h1>
        <h2 className="text-sm text-gray-700 mb-1">
          SMA IT HSI
        </h2>
        <div className="text-xs text-gray-600">
          <span>Tahun Akademik {activeAcademicYear?.year_name || '2025/2026'}</span>
          {selectedSubjectName && (
            <span className="ml-4 font-medium text-gray-800">
              • {selectedSubjectName}
            </span>
          )}
        </div>
      </div>

      {/* Main Content - Full Width Calendar */}
      <div className="w-full">
        <Card className="bg-white border-gray-200">
          <CardContent className="p-2">
            {/* Ultra Compact Calendar Layout for 12 months */}
            <div className="space-y-1">
              {months.map((month, index) => {
                const monthData = getMonthData(index);

                return (
                  <div key={month} className="bg-gray-50 rounded p-2 border border-gray-200">
                    {/* Month Row - Ultra Compact horizontal layout */}
                    <div className="flex items-center gap-2">
                      {/* Month Name & Stats */}
                      <div className="w-14 text-left flex-shrink-0">
                        <h3 className="text-[10px] font-bold text-gray-900 leading-tight">{month}</h3>
                        <p className="text-[8px] text-blue-600 font-medium leading-tight">
                          {monthData.scheduleDates.size} hari efektif
                        </p>
                      </div>

                      {/* Days Grid - Ultra Compact horizontal layout */}
                      <div className="flex flex-wrap gap-0.5">
                        {Array.from({ length: 31 }, (_, i) => {
                          const dayNumber = i + 1;
                          const monthDate = new Date(monthData.year, monthData.month - 1, dayNumber);

                          // Check if this day exists in the month
                          if (monthDate.getMonth() !== monthData.month - 1) {
                            return null;
                          }

                          const hasSchedule = monthData.scheduleDates.has(dayNumber);

                          return (
                            <div
                              key={dayNumber}
                              className={`
                                calendar-day-box rounded transition-colors
                                ${hasSchedule
                                  ? 'bg-blue-500 text-white font-bold'
                                  : 'text-gray-600 border border-gray-300'
                                }
                              `}
                            >
                              <span
                                className={`calendar-day-number ${hasSchedule ? 'font-bold' : ''}`}
                              >
                                {dayNumber}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Summary Text - Ultra Compact */}
      <div className="mt-3 text-center">
        <div className="inline-flex items-center gap-4 text-xs text-gray-600 bg-gray-50 px-4 py-2 rounded">
          <span><strong>{totalSchedules}</strong> Total Jadwal</span>
          <span><strong>{activeDays}</strong> Hari Aktif</span>
          <span><strong>{totalHours}h</strong> Total Jam</span>
          <span><strong>{totalJP}</strong> Total JP</span>
          <span className="text-[10px]">• 1 JP = 45 menit</span>
        </div>

        {/* Legend - Compact */}
        <div className="mt-2 flex justify-center items-center gap-3 text-[10px] text-gray-500">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-blue-500 rounded"></div>
            <span>Tanggal dengan jadwal</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-gray-300 rounded"></div>
            <span>Tanggal kosong</span>
          </div>
        </div>
      </div>

      {/* Footer - Compact */}
      <div className="text-center mt-3 text-[10px] text-gray-500">
        Dicetak pada: {new Date().toLocaleDateString('id-ID', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })}
      </div>
    </div>
    </>
  );
};

export default OverviewPrintLayout;
