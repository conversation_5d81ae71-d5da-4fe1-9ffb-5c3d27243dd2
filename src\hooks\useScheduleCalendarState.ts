
import { useState } from 'react';
import { addWeeks, startOfWeek } from 'date-fns';
import { useAcademicWeeks } from '@/hooks/useAcademicWeeks';

export const useScheduleCalendarState = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<any>(null);
  const [selectedWeek, setSelectedWeek] = useState(1);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [selectedClassId, setSelectedClassId] = useState<string | null>(null);
  const [activeSchedule, setActiveSchedule] = useState<any>(null);
  const [editingSchedule, setEditingSchedule] = useState<any>(null);
  const [draggedSubject, setDraggedSubject] = useState<any>(null);
  
  const { activeAcademicYear } = useAcademicWeeks();

  // Calculate current week date based on selected week
  const getCurrentWeekDate = () => {
    if (!activeAcademicYear) return new Date();
    
    const startDate = new Date(activeAcademicYear.start_date);
    const weekStartDate = addWeeks(startOfWeek(startDate, { weekStartsOn: 1 }), selectedWeek - 1);
    return weekStartDate;
  };

  const handleTimeSlotClick = (timeSlot: any) => {
    setSelectedTimeSlot(timeSlot);
    setIsAddModalOpen(true);
  };

  const handleWeekSelect = (week: number) => {
    setSelectedWeek(week);
    const weekDate = getCurrentWeekDate();
    setSelectedDate(weekDate);
  };

  const handleScheduleEdit = (schedule: any) => {
    console.log('Editing schedule:', schedule);
    setEditingSchedule(schedule);
    setIsEditModalOpen(true);
  };

  const closeModal = () => {
    setIsAddModalOpen(false);
    setActiveSchedule(null);
    setSelectedTimeSlot(null);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setEditingSchedule(null);
  };

  return {
    isAddModalOpen,
    isEditModalOpen,
    selectedTimeSlot,
    selectedWeek,
    selectedDate,
    selectedClassId,
    activeSchedule,
    editingSchedule,
    draggedSubject,
    setSelectedClassId,
    setActiveSchedule,
    setDraggedSubject,
    setSelectedTimeSlot,
    setIsAddModalOpen,
    getCurrentWeekDate,
    handleTimeSlotClick,
    handleWeekSelect,
    handleScheduleEdit,
    closeModal,
    closeEditModal,
  };
};
